#!/bin/bash

# ABM-LLM-V2 Docker启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  ABM-LLM-V2 Docker 启动脚本${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     启动ABM-LLM-V2服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启服务"
    echo "  logs      查看服务日志"
    echo "  status    查看服务状态"
    echo "  clean     清理所有容器和镜像"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./build-image.sh      # 首次使用需要先构建镜像"
    echo "  $0 start             # 启动服务"
    echo "  $0 logs              # 查看日志"
    echo "  $0 stop              # 停止服务"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}



# 创建必要的目录
create_directories() {
    print_message "创建必要的目录..."
    mkdir -p ../data/conversations ../data/agents ../data/worlds ../logs
}

# 启动服务
start_services() {
    print_header
    print_message "启动ABM-LLM-V2服务..."
    create_directories

    docker-compose up -d

    print_message "服务启动完成！"
    echo ""
    echo "访问地址:"
    echo "  前端: http://localhost:3000"
    echo "  后端: http://localhost:8080"
    echo ""
    echo "查看日志: docker-compose logs -f"
    echo "停止服务: docker-compose down"
    echo ""
    print_message "提示: 如果是首次运行，请先执行 ./build-image.sh 构建镜像"
}



# 停止所有服务
stop_services() {
    print_message "停止所有服务..."
    docker-compose down
    print_message "所有服务已停止"
}

# 清理容器和镜像
clean_all() {
    print_warning "这将删除所有相关的容器、镜像和卷，确定要继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_message "清理容器和镜像..."
        docker-compose down -v --rmi all
        docker system prune -f
        print_message "清理完成"
    else
        print_message "取消清理操作"
    fi
}

# 查看日志
show_logs() {
    print_message "显示服务日志..."
    docker-compose logs -f
}

# 查看服务状态
show_status() {
    print_message "显示服务状态..."
    docker-compose ps
}

# 重启服务
restart_services() {
    print_message "重启服务..."
    docker-compose restart
    print_message "服务重启完成"
}

# 主逻辑
main() {
    check_docker

    case "${1:-help}" in
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        "clean")
            clean_all
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 切换到脚本所在目录
cd "$(dirname "$0")"

# 执行主函数
main "$@"
