#!/bin/bash

# ABM-LLM-V2 Docker镜像构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  ABM-LLM-V2 镜像构建脚本${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 默认配置
IMAGE_NAME="abm-llm-v2"
IMAGE_TAG="latest"
BUILD_CONTEXT="../"
DOCKERFILE_PATH="docker/Dockerfile"
NO_CACHE=false
PUSH_IMAGE=false
REGISTRY=""

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -n, --name NAME       镜像名称 (默认: abm-llm-v2)"
    echo "  -t, --tag TAG         镜像标签 (默认: latest)"
    echo "  -r, --registry REG    镜像仓库地址"
    echo "  --no-cache            不使用构建缓存"
    echo "  --push                构建完成后推送到仓库"
    echo "  --clean               构建前清理旧镜像"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 基础构建"
    echo "  $0 -t v1.0.0                        # 指定标签"
    echo "  $0 -r registry.example.com --push   # 构建并推送"
    echo "  $0 --no-cache --clean               # 清理并重新构建"
}

# 检查Docker环境
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未运行，请启动Docker"
        exit 1
    fi
    
    print_success "Docker环境检查通过"
}

# 检查构建环境
check_build_env() {
    print_info "检查构建环境..."
    
    # 检查必需文件
    local required_files=(
        "../requirements.txt"
        "../run_app.py"
        "../frontend/package.json"
        "Dockerfile"
        "supervisord.conf"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "缺少必需文件: $file"
            exit 1
        fi
    done
    
    print_success "构建环境检查通过"
}

# 清理旧镜像
clean_old_images() {
    print_info "清理旧镜像..."
    
    # 删除同名镜像
    if docker images -q "${FULL_IMAGE_NAME}" &> /dev/null; then
        docker rmi "${FULL_IMAGE_NAME}" 2>/dev/null || true
        print_success "已删除旧镜像: ${FULL_IMAGE_NAME}"
    fi
    
    # 清理悬空镜像
    if docker images -f "dangling=true" -q | grep -q .; then
        docker rmi $(docker images -f "dangling=true" -q) 2>/dev/null || true
        print_success "已清理悬空镜像"
    fi
}

# 构建镜像
build_image() {
    print_header
    print_info "开始构建镜像: ${FULL_IMAGE_NAME}"
    
    # 构建参数
    local build_args=""
    if [ "$NO_CACHE" = true ]; then
        build_args="--no-cache"
    fi
    
    # 显示构建信息
    echo ""
    print_info "构建配置:"
    echo "  镜像名称: ${FULL_IMAGE_NAME}"
    echo "  构建上下文: ${BUILD_CONTEXT}"
    echo "  Dockerfile: ${DOCKERFILE_PATH}"
    echo "  使用缓存: $([ "$NO_CACHE" = true ] && echo "否" || echo "是")"
    echo ""
    
    # 执行构建
    print_info "执行Docker构建..."
    if docker build $build_args -t "${FULL_IMAGE_NAME}" -f "${DOCKERFILE_PATH}" "${BUILD_CONTEXT}"; then
        print_success "镜像构建成功: ${FULL_IMAGE_NAME}"
    else
        print_error "镜像构建失败"
        exit 1
    fi
    
    # 显示镜像信息
    echo ""
    print_info "镜像信息:"
    docker images "${FULL_IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.Size}}\t{{.CreatedAt}}"
}

# 推送镜像
push_image() {
    if [ "$PUSH_IMAGE" = true ]; then
        print_info "推送镜像到仓库..."
        
        if [ -z "$REGISTRY" ]; then
            print_warning "未指定仓库地址，跳过推送"
            return
        fi
        
        if docker push "${FULL_IMAGE_NAME}"; then
            print_success "镜像推送成功: ${FULL_IMAGE_NAME}"
        else
            print_error "镜像推送失败"
            exit 1
        fi
    fi
}

# 测试镜像
test_image() {
    print_info "测试镜像..."
    
    # 创建临时容器测试
    local container_name="abm-test-$(date +%s)"
    
    print_info "启动测试容器: ${container_name}"
    if docker run -d --name "${container_name}" -p 18080:8080 -p 13000:3000 "${FULL_IMAGE_NAME}"; then
        print_success "测试容器启动成功"
        
        # 等待服务启动
        print_info "等待服务启动..."
        sleep 10
        
        # 测试后端健康检查
        if curl -f http://localhost:18080/api/health &> /dev/null; then
            print_success "后端服务测试通过"
        else
            print_warning "后端服务测试失败"
        fi
        
        # 清理测试容器
        docker stop "${container_name}" &> /dev/null || true
        docker rm "${container_name}" &> /dev/null || true
        print_info "已清理测试容器"
    else
        print_error "测试容器启动失败"
        exit 1
    fi
}

# 显示使用说明
show_usage() {
    echo ""
    print_success "镜像构建完成！"
    echo ""
    print_info "使用方法:"
    echo "  # 运行容器"
    echo "  docker run -d --name abm-app \\"
    echo "    -p 8080:8080 -p 3000:3000 \\"
    echo "    -v \$(pwd)/data:/app/data \\"
    echo "    -v \$(pwd)/logs:/app/logs \\"
    echo "    -v \$(pwd)/config.conf:/app/config.conf \\"
    echo "    ${FULL_IMAGE_NAME}"
    echo ""
    echo "  # 或使用docker-compose"
    echo "  docker-compose up -d"
    echo ""
    print_info "访问地址:"
    echo "  前端: http://localhost:3000"
    echo "  后端: http://localhost:8080"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--name)
                IMAGE_NAME="$2"
                shift 2
                ;;
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -r|--registry)
                REGISTRY="$2"
                shift 2
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --push)
                PUSH_IMAGE=true
                shift
                ;;
            --clean)
                CLEAN_OLD=true
                shift
                ;;
            --test)
                TEST_IMAGE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 构建完整镜像名
    if [ -n "$REGISTRY" ]; then
        FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    else
        FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
    fi
}

# 主函数
main() {
    # 切换到脚本所在目录
    cd "$(dirname "$0")"
    
    # 解析参数
    parse_args "$@"
    
    # 执行构建流程
    check_docker
    check_build_env
    
    if [ "$CLEAN_OLD" = true ]; then
        clean_old_images
    fi
    
    build_image
    
    if [ "$TEST_IMAGE" = true ]; then
        test_image
    fi
    
    push_image
    show_usage
}

# 执行主函数
main "$@"
