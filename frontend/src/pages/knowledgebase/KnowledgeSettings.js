import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Select, Button, Switch, Slider, InputNumber, Space, Typography, Divider, message, Alert, Empty, Tabs, Radio, Collapse } from 'antd';
import { SaveOutlined, SettingOutlined, LockOutlined, UserOutlined, TeamOutlined, GlobalOutlined, QuestionCircleOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const { Panel } = Collapse;

// 知识库名称映射
const knowledgeNames = {
  1: '客户服务知识库',
  2: '产品知识库',
  3: '销售培训资料',
  4: '技术文档库',
  5: '市场分析报告'
};

// 模拟知识库设置数据
const mockSettings = {
  1: {
    // 基本设置
    basic: {
      name: '客户服务知识库',
      description: '包含客户服务流程和解决方案的知识',
      type: 'text',
    },
    // 索引设置
    indexing: {
      chunk_size: 1000,
      chunk_overlap: 200,
      embedding_model: 'text-embedding-ada-002',
      index_method: 'faiss',
      similarity_metric: 'cosine',
      auto_update: true,
    },
    // 检索设置
    retrieval: {
      search_method: 'hybrid',
      top_k: 5,
      similarity_threshold: 0.7,
      reranking_enabled: true,
      reranking_model: 'cross-encoder/ms-marco-MiniLM-L-6-v2',
      max_tokens_per_doc: 4000,
    },
    // 访问控制
    access: {
      visibility: 'private',
      shared_with: ['user1', 'user2', 'team1'],
      require_auth: true,
      allow_export: false,
    }
  },
  2: {
    // 基本设置
    basic: {
      name: '产品知识库',
      description: '详细的产品信息和技术规格',
      type: 'vector',
    },
    // 索引设置
    indexing: {
      chunk_size: 800,
      chunk_overlap: 150,
      embedding_model: 'text-embedding-3-small',
      index_method: 'hnsw',
      similarity_metric: 'dot_product',
      auto_update: false,
    },
    // 检索设置
    retrieval: {
      search_method: 'vector',
      top_k: 8,
      similarity_threshold: 0.6,
      reranking_enabled: false,
      reranking_model: '',
      max_tokens_per_doc: 6000,
    },
    // 访问控制
    access: {
      visibility: 'team',
      shared_with: ['team2', 'team3'],
      require_auth: true,
      allow_export: true,
    }
  }
};

const KnowledgeSettings = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [form] = Form.useForm();
  const [selectedKnowledgeId, setSelectedKnowledgeId] = useState(1); // 默认选择第一个知识库
  const [selectedKnowledgeName, setSelectedKnowledgeName] = useState('');

  // 获取知识库设置
  useEffect(() => {
    fetchSettings(selectedKnowledgeId);
    setSelectedKnowledgeName(knowledgeNames[selectedKnowledgeId] || '');
  }, [selectedKnowledgeId, activeTab]);

  const fetchSettings = (id) => {
    setLoading(true);
    // 模拟API调用
    setTimeout(() => {
      const settingsData = mockSettings[id] || null;
      setSettings(settingsData);
      if (settingsData && settingsData[activeTab]) {
        // 使用 setTimeout 确保 Form 组件已经渲染完成
        setTimeout(() => {
          form.setFieldsValue(settingsData[activeTab]);
        }, 0);
      }
      setLoading(false);
    }, 500);
  };

  // 处理知识库选择变化
  const handleKnowledgeChange = (value) => {
    setSelectedKnowledgeId(value);
  };

  // 处理表单提交
  const handleSubmit = (values) => {
    if (!settings) return;

    // 更新设置
    const updatedSettings = {
      ...settings,
      [activeTab]: values
    };

    // 模拟API调用
    message.loading('正在保存设置...', 1)
      .then(() => {
        setSettings(updatedSettings);
        message.success('设置保存成功');
      });
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    if (settings && settings[key]) {
      // 使用 setTimeout 确保 Form 组件已经渲染完成
      setTimeout(() => {
        form.setFieldsValue(settings[key]);
      }, 0);
    }
  };

  // 渲染基本设置表单
  const renderBasicSettingsForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={settings?.basic || {}}
      >
        <Form.Item
          name="name"
          label="知识库名称"
          rules={[{ required: true, message: '请输入知识库名称' }]}
        >
          <Input placeholder="请输入知识库名称" />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述"
          rules={[{ required: true, message: '请输入知识库描述' }]}
        >
          <Input.TextArea rows={4} placeholder="请输入知识库描述" />
        </Form.Item>

        <Form.Item
          name="type"
          label="类型"
          rules={[{ required: true, message: '请选择知识库类型' }]}
        >
          <Select placeholder="请选择知识库类型">
            <Option value="text">文本</Option>
            <Option value="vector">向量</Option>
            <Option value="structured">结构化</Option>
          </Select>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
            保存设置
          </Button>
        </Form.Item>
      </Form>
    );
  };

  // 渲染索引设置表单
  const renderIndexingSettingsForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={settings?.indexing || {}}
      >
        <Alert
          message="索引设置影响知识库的性能和准确性"
          description="这些设置决定了文档如何被分块和索引，以及使用什么嵌入模型。修改这些设置可能需要重新索引知识库。"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <div style={{ display: 'flex', gap: '16px' }}>
          <Form.Item
            name="chunk_size"
            label="分块大小"
            tooltip="每个文本块的最大token数量"
            style={{ flex: 1 }}
            rules={[{ required: true, message: '请输入分块大小' }]}
          >
            <InputNumber min={100} max={4000} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="chunk_overlap"
            label="分块重叠"
            tooltip="相邻文本块之间重叠的token数量"
            style={{ flex: 1 }}
            rules={[{ required: true, message: '请输入分块重叠' }]}
          >
            <InputNumber min={0} max={1000} style={{ width: '100%' }} />
          </Form.Item>
        </div>

        <Form.Item
          name="embedding_model"
          label="嵌入模型"
          tooltip="用于生成文本向量的模型"
          rules={[{ required: true, message: '请选择嵌入模型' }]}
        >
          <Select placeholder="请选择嵌入模型">
            <Option value="text-embedding-ada-002">OpenAI Ada 002</Option>
            <Option value="text-embedding-3-small">OpenAI Embedding 3 Small</Option>
            <Option value="text-embedding-3-large">OpenAI Embedding 3 Large</Option>
            <Option value="bge-large-zh">BGE Large Chinese</Option>
            <Option value="bge-large-en">BGE Large English</Option>
            <Option value="custom">自定义模型</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="index_method"
          label="索引方法"
          tooltip="向量数据库使用的索引算法"
          rules={[{ required: true, message: '请选择索引方法' }]}
        >
          <Select placeholder="请选择索引方法">
            <Option value="faiss">FAISS</Option>
            <Option value="hnsw">HNSW</Option>
            <Option value="annoy">Annoy</Option>
            <Option value="flat">Flat (精确但较慢)</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="similarity_metric"
          label="相似度度量"
          tooltip="计算向量相似度的方法"
          rules={[{ required: true, message: '请选择相似度度量' }]}
        >
          <Select placeholder="请选择相似度度量">
            <Option value="cosine">余弦相似度</Option>
            <Option value="dot_product">点积</Option>
            <Option value="euclidean">欧几里得距离</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="auto_update"
          label="自动更新索引"
          valuePropName="checked"
          tooltip="当文档更新时自动重建索引"
        >
          <Switch />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
            保存设置
          </Button>
        </Form.Item>
      </Form>
    );
  };

  // 渲染检索设置表单
  const renderRetrievalSettingsForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={settings?.retrieval || {}}
      >
        <Form.Item
          name="search_method"
          label="搜索方法"
          tooltip="用于检索文档的方法"
          rules={[{ required: true, message: '请选择搜索方法' }]}
        >
          <Radio.Group>
            <Space direction="vertical">
              <Radio value="vector">
                <Text strong>向量搜索</Text>
                <div><Text type="secondary">使用语义相似度查找相关文档</Text></div>
              </Radio>
              <Radio value="keyword">
                <Text strong>关键词搜索</Text>
                <div><Text type="secondary">使用BM25等算法进行关键词匹配</Text></div>
              </Radio>
              <Radio value="hybrid">
                <Text strong>混合搜索 (推荐)</Text>
                <div><Text type="secondary">结合向量搜索和关键词搜索的优点</Text></div>
              </Radio>
            </Space>
          </Radio.Group>
        </Form.Item>

        <div style={{ display: 'flex', gap: '16px' }}>
          <Form.Item
            name="top_k"
            label="返回结果数量"
            tooltip="每次查询返回的最大文档数量"
            style={{ flex: 1 }}
            rules={[{ required: true, message: '请输入返回结果数量' }]}
          >
            <InputNumber min={1} max={20} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="similarity_threshold"
            label="相似度阈值"
            tooltip="只返回相似度高于此阈值的文档"
            style={{ flex: 1 }}
            rules={[{ required: true, message: '请输入相似度阈值' }]}
          >
            <Slider
              min={0}
              max={1}
              step={0.05}
              marks={{
                0: '0',
                0.5: '0.5',
                1: '1'
              }}
            />
          </Form.Item>
        </div>

        <Form.Item
          name="reranking_enabled"
          label="启用重排序"
          valuePropName="checked"
          tooltip="使用交叉编码器对检索结果进行重排序，提高准确性"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name="reranking_model"
          label="重排序模型"
          tooltip="用于重排序的交叉编码器模型"
          dependencies={['reranking_enabled']}
        >
          <Select
            placeholder="请选择重排序模型"
            disabled={!form.getFieldValue('reranking_enabled')}
          >
            <Option value="cross-encoder/ms-marco-MiniLM-L-6-v2">MiniLM-L-6</Option>
            <Option value="cross-encoder/ms-marco-MiniLM-L-12-v2">MiniLM-L-12</Option>
            <Option value="cross-encoder/mmarco-mMiniLMv2-L12-H384-v1">mMiniLMv2 (多语言)</Option>
            <Option value="custom">自定义模型</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="max_tokens_per_doc"
          label="每个文档最大Token数"
          tooltip="限制每个检索文档的最大token数量，以控制上下文长度"
          rules={[{ required: true, message: '请输入每个文档最大Token数' }]}
        >
          <InputNumber min={500} max={10000} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
            保存设置
          </Button>
        </Form.Item>
      </Form>
    );
  };

  // 渲染访问控制表单
  const renderAccessControlForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={settings?.access || {}}
      >
        <Form.Item
          name="visibility"
          label="可见性"
          rules={[{ required: true, message: '请选择可见性' }]}
        >
          <Radio.Group>
            <Space direction="vertical">
              <Radio value="private">
                <Space>
                  <LockOutlined />
                  <Text strong>私有</Text>
                </Space>
                <div><Text type="secondary">只有您和被授权的用户可以访问</Text></div>
              </Radio>
              <Radio value="team">
                <Space>
                  <TeamOutlined />
                  <Text strong>团队</Text>
                </Space>
                <div><Text type="secondary">您的团队成员可以访问</Text></div>
              </Radio>
              <Radio value="public">
                <Space>
                  <GlobalOutlined />
                  <Text strong>公开</Text>
                </Space>
                <div><Text type="secondary">所有系统用户都可以访问</Text></div>
              </Radio>
            </Space>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="shared_with"
          label="共享给"
          tooltip="选择可以访问此知识库的用户或团队"
          dependencies={['visibility']}
        >
          <Select
            mode="multiple"
            placeholder="请选择用户或团队"
            disabled={form.getFieldValue('visibility') === 'public'}
            style={{ width: '100%' }}
            options={[
              { label: '用户1', value: 'user1' },
              { label: '用户2', value: 'user2' },
              { label: '用户3', value: 'user3' },
              { label: '团队1', value: 'team1' },
              { label: '团队2', value: 'team2' },
              { label: '团队3', value: 'team3' },
            ]}
          />
        </Form.Item>

        <Form.Item
          name="require_auth"
          label="需要认证"
          valuePropName="checked"
          tooltip="即使是公开知识库，也需要用户登录才能访问"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name="allow_export"
          label="允许导出"
          valuePropName="checked"
          tooltip="允许用户导出知识库内容"
        >
          <Switch />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
            保存设置
          </Button>
        </Form.Item>
      </Form>
    );
  };

  return (
    <div>
      <div style={{ marginBottom: '16px' }}>
        <Title level={5}>知识库设置</Title>
        <Text type="secondary">配置知识库的索引、检索和访问控制参数</Text>
      </div>

      <div style={{ marginBottom: '16px' }}>
        <Select
          value={selectedKnowledgeId}
          onChange={handleKnowledgeChange}
          style={{ width: 200 }}
          placeholder="选择知识库"
        >
          {Object.entries(knowledgeNames).map(([id, name]) => (
            <Option key={id} value={Number(id)}>{name}</Option>
          ))}
        </Select>
      </div>

      {settings ? (
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          items={[
            {
              key: 'basic',
              label: '基本设置',
              children: renderBasicSettingsForm()
            },
            {
              key: 'indexing',
              label: '索引设置',
              children: renderIndexingSettingsForm()
            },
            {
              key: 'retrieval',
              label: '检索设置',
              children: renderRetrievalSettingsForm()
            },
            {
              key: 'access',
              label: '访问控制',
              children: renderAccessControlForm()
            }
          ]}
        />
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="未找到知识库设置"
          />
        </div>
      )}
    </div>
  );
};

export default KnowledgeSettings;
