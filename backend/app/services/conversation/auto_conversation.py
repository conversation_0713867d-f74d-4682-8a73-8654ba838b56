"""
自主讨论会话模块

提供自主讨论相关的功能，包括启动自主讨论、处理讨论轮次、生成总结等

函数与关键变量说明:
---------------------------------------

* start_auto_discussion - 启动智能体自主讨论
  - task_id: 行动任务ID
  - conversation_id: 会话ID
  - rounds: 讨论轮数
  - topic: 讨论主题
  - summarize: 是否在讨论结束后由第一个智能体总结
  - streaming: 是否使用流式输出
  - app_context: 应用上下文，用于流式处理
  - result_queue: 结果队列，用于流式处理

* _start_auto_discussion_impl - 自主讨论实现方法
  - task_id: 行动任务ID
  - conversation_id: 会话ID
  - rounds: 讨论轮数
  - topic: 讨论主题
  - summarize: 是否在讨论结束后由第一个智能体总结
  - streaming: 是否使用流式输出
  - result_queue: 结果队列，用于流式处理

* start_auto_discussion_stream - 启动智能体自主讨论（流式版本）
  - app_context: Flask应用上下文
  - task_id: 行动任务ID
  - conversation_id: 会话ID
  - rounds: 讨论轮数
  - topic: 讨论主题
  - summarize: 是否在讨论结束后由第一个智能体总结
  - result_queue: 结果队列
"""
import json
import logging
import queue
import traceback
import jwt
from typing import Dict, Any, List, Optional
from datetime import datetime
from flask import request, current_app

from app.models import db, Conversation, ConversationAgent, Message, Agent, Role, AutonomousTask, AutonomousTaskExecution, User
from app.utils.datetime_utils import get_current_time_with_timezone
from app.services.conversation.message_formater import format_agent_error_done, format_all_agents_done, serialize_message

# 导入需要的函数，避免循环导入
from app.services.conversation.message_processor import process_message_common

logger = logging.getLogger(__name__)

def get_current_user():
    """
    获取当前登录用户信息
    从JWT token中解析用户信息
    """
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            logger.warning("未提供认证令牌")
            return None

        token = auth_header.split(' ')[1]

        # 验证令牌
        payload = jwt.decode(
            token,
            current_app.config['SECRET_KEY'],
            algorithms=['HS256']
        )

        # 获取用户信息
        user_id = payload.get('user_id')
        username = payload.get('username')

        if user_id and username:
            return {
                'id': user_id,
                'username': username
            }
        else:
            logger.warning("JWT payload中缺少用户信息")
            return None

    except jwt.ExpiredSignatureError:
        logger.warning("JWT令牌已过期")
        return None
    except jwt.InvalidTokenError:
        logger.warning("无效的JWT令牌")
        return None
    except Exception as e:
        logger.error(f"获取当前用户失败: {str(e)}")
        return None

def start_auto_discussion(task_id: int, conversation_id: int, rounds: int = 1,
                          topic: str = None, summarize: bool = True,
                          streaming: bool = False, app_context = None,
                          result_queue: queue.Queue = None, summarizer_agent_id = None,
                          enable_planning: bool = False, planner_agent_id = None) -> Dict:
    """
    启动智能体自主讨论

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID
        rounds: 讨论轮数，默认为1
        topic: 讨论主题，默认为None
        summarize: 是否在讨论结束后进行总结，默认为True
        streaming: 是否使用流式输出，默认为False
        app_context: 应用上下文，用于流式处理
        result_queue: 结果队列，用于流式处理
        summarizer_agent_id: 指定进行总结的智能体ID，默认为None（使用第一个智能体）
        enable_planning: 是否启用计划功能，默认为False
        planner_agent_id: 指定进行计划的智能体ID，默认为None（使用第一个智能体）

    Returns:
        Dict: 讨论结果信息
    """
    # 处理默认参数
    if enable_planning is None:
        enable_planning = False
    if planner_agent_id is None:
        planner_agent_id = None

    # 如果是流式处理模式，需要应用上下文
    if streaming and app_context:
        with app_context:
            return _start_auto_discussion_impl(
                task_id, conversation_id, rounds, topic, summarize, streaming, result_queue,
                summarizer_agent_id, enable_planning, planner_agent_id
            )
    else:
        # 非流式处理不需要应用上下文
        return _start_auto_discussion_impl(
            task_id, conversation_id, rounds, topic, summarize, streaming, result_queue,
            summarizer_agent_id, enable_planning, planner_agent_id
        )

def _start_auto_discussion_impl(task_id: int, conversation_id: int, rounds: int = 1,
                              topic: str = None, summarize: bool = True,
                              streaming: bool = False, result_queue: queue.Queue = None,
                              summarizer_agent_id = None, enable_planning: bool = False,
                               planner_agent_id = None) -> Dict:
    """自主讨论实现方法"""
    # 构建任务键
    task_key = f"{task_id}:{conversation_id}"

    # 注册自动讨论任务
    _active_auto_discussions[task_key] = {
        'task_id': task_id,
        'conversation_id': conversation_id,
        'rounds': rounds,
        'topic': topic,
        'summarize': summarize,
        'streaming': streaming,
        'result_queue': result_queue,
        'summarizer_agent_id': summarizer_agent_id,
        'start_time': get_current_time_with_timezone()
    }

    logger.info(f"已注册自动讨论任务: {task_key}")
    try:
        # 流式模式初始化
        if streaming and result_queue:
            # 在队列中添加连接成功事件
            result_queue.put(json.dumps({
                'connectionStatus': 'connected'
            }))

        # 检查会话是否存在且属于该行动任务
        conversation = Conversation.query.get(conversation_id)
        if not conversation or conversation.action_task_id != task_id:
            error_msg = f"会话未找到或不属于行动任务: {task_id}"
            logger.error(error_msg)

            if streaming and result_queue:
                result_queue.put(json.dumps({
                    'connectionStatus': 'error',
                    'error': error_msg
                }))
                result_queue.put(None)
                return {'status': 'error', 'message': error_msg}
            else:
                raise ValueError(error_msg)

        # 获取会话中的所有非监督者智能体（按ID排序，确保顺序一致）
        conv_agents = ConversationAgent.query.join(Agent).filter(
            ConversationAgent.conversation_id == conversation_id,
            Agent.is_observer == False  # 过滤掉监督者智能体
        ).all()
        if not conv_agents or len(conv_agents) < 2:
            error_msg = "会话中至少需要两个任务智能体才能进行自主讨论（监督者智能体不参与自主任务）"
            logger.error(error_msg)

            if streaming and result_queue:
                result_queue.put(json.dumps({
                    'connectionStatus': 'error',
                    'error': error_msg
                }))
                result_queue.put(None)
                return {'status': 'error', 'message': error_msg}
            else:
                raise ValueError(error_msg)

        # 按ID排序智能体
        conv_agents.sort(key=lambda ca: ca.id)

        # 创建自主任务记录
        autonomous_task = AutonomousTask(
            conversation_id=conversation_id,
            type='discussion',  # 讨论模式
            status='active',
            config={
                'rounds': rounds,
                'topic': topic,
                'summarize': summarize,
                'summarizer_agent_id': summarizer_agent_id,
                'speaking_mode': 'sequential',  # 默认顺序模式
                'enable_planning': enable_planning,
                'planner_agent_id': planner_agent_id
            }
        )
        db.session.add(autonomous_task)
        db.session.flush()  # 获取ID但不提交事务

        # 获取当前登录用户信息
        current_user = get_current_user()
        trigger_source = current_user['username'] if current_user else 'user'

        # 创建自主任务执行记录
        autonomous_execution = AutonomousTaskExecution(
            autonomous_task_id=autonomous_task.id,
            execution_type='manual',  # 手动触发
            trigger_source=trigger_source,
            trigger_data={
                'task_id': task_id,
                'conversation_id': conversation_id,
                'user_action': 'start_auto_discussion',
                'user_id': current_user['id'] if current_user else None,
                'username': current_user['username'] if current_user else None
            },
            status='running'
        )
        db.session.add(autonomous_execution)
        db.session.commit()

        logger.info(f"已创建自主任务记录: autonomous_task_id={autonomous_task.id}, execution_id={autonomous_execution.id}")

        # 创建开始讨论的系统消息
        if not topic:
            topic = "请基于各自角色和知识，进行一次有深度的讨论"

        system_msg = Message(
            conversation_id=conversation_id,
            action_task_id=task_id,  # 添加行动任务ID
            content=f"提示：现在开始自主行动，共{rounds}轮，每位智能体将轮流行动。\n任务主题：{topic}",
            role="system",
            created_at=get_current_time_with_timezone()
        )
        db.session.add(system_msg)
        db.session.commit()

        # 流式模式发送系统消息
        if streaming and result_queue:
            result_queue.put(json.dumps({
                'message': {
                    'id': system_msg.id,
                    'content': system_msg.content,
                    'role': 'system',
                    'created_at': system_msg.created_at.isoformat() if system_msg.created_at else None
                }
            }))

        # 创建流式模式的回调函数（需要在计划阶段之前定义）
        from app.services.conversation.callback_utils import create_standard_sse_callback
        sse_callback = create_standard_sse_callback(streaming, result_queue)

        # 如果启用计划功能，先进行计划阶段
        if enable_planning:
            # 确定计划智能体
            planner_agent = None
            if planner_agent_id:
                planner_agent = Agent.query.get(planner_agent_id)

            if not planner_agent:
                # 使用第一个智能体作为计划者
                planner_agent = Agent.query.get(conv_agents[0].agent_id) if conv_agents else None

            if planner_agent:
                logger.info(f"开始计划阶段，计划智能体: {planner_agent.name}")

                # 创建计划提示词
                planning_prompt = f"<div style='color: #A0A0A0;'>@{planner_agent.name} 请为即将开始的{rounds}轮自主行动制定详细计划。请分析任务主题，制定行动策略，并将完整的计划写入共享记忆中，以便其他智能体参考。\n任务主题：{topic}</div>\n"

                planning_virtual_message = {
                    'content': planning_prompt,
                    'target_agent_id': planner_agent.id
                }

                if streaming:
                    # 流式模式通知用户计划阶段开始
                    agent_role = Role.query.get(planner_agent.role_id) if hasattr(planner_agent, 'role_id') and planner_agent.role_id else None
                    role_name = agent_role.name if agent_role else "智能助手"
                    sse_callback({
                        "type": "agentInfo",
                        "turnPrompt": f"由智能体 {planner_agent.name}({role_name}) 制定计划",
                        "agentId": str(planner_agent.id),
                        "agentName": f"{planner_agent.name}({role_name})",
                        "round": 0,  # 计划阶段在正式轮次之前
                        "totalRounds": rounds,
                        "responseOrder": 1,
                        "totalAgents": 1,
                        "isPlanning": True
                    })

                    # 流式模式处理计划
                    from app.services.conversation_service import ConversationService
                    response_completed, error_info = ConversationService._process_single_agent_response(
                        task_id=task_id,
                        conversation_id=conversation_id,
                        human_message=None,  # 虚拟消息
                        agent_id=planner_agent.id,
                        content=planning_prompt,
                        sse_callback=sse_callback,
                        result_queue=None
                    )

                    if not response_completed:
                        logger.warning(f"计划阶段失败: {error_info}")
                else:
                    # 非流式模式处理计划
                    from app.services.conversation_service import ConversationService
                    _, planning_message = ConversationService.add_message_to_conversation(
                        conversation_id,
                        planning_virtual_message,
                        is_virtual=True
                    )

                    if planning_message:
                        logger.info(f"计划已由{planner_agent.name}完成")
                    else:
                        logger.warning("计划生成失败")

                logger.info("计划阶段完成，开始正式讨论")

        # 存储所有消息ID
        message_ids = [system_msg.id]
        agent_map = {}

        try:
            # 导入需要的函数，避免循环导入
            from app.services.conversation_service import ConversationService

            # 获取每个智能体的信息
            for conv_agent in conv_agents:
                agent = Agent.query.get(conv_agent.agent_id)
                if agent:
                    agent_map[conv_agent.agent_id] = {
                        'id': agent.id,
                        'name': agent.name,
                        'description': agent.description
                    }

            # 开始讨论
            for round_num in range(1, rounds+1):
                # 检查任务是否已被停止
                if task_key not in _active_auto_discussions:
                    logger.info(f"自主任务已被停止，退出轮次循环: {task_key}")
                    # 更新自主任务状态为停止
                    try:
                        autonomous_execution.status = 'stopped'
                        autonomous_execution.end_time = get_current_time_with_timezone()
                        autonomous_execution.result = {
                            'status': 'stopped',
                            'message': '自主任务被用户手动停止'
                        }
                        autonomous_task.status = 'stopped'
                        db.session.commit()
                        logger.info(f"已更新自主任务状态为停止: autonomous_task_id={autonomous_task.id}")
                    except Exception as e:
                        logger.error(f"更新自主任务状态失败: {str(e)}")
                    return {'status': 'stopped', 'message': '自主任务被用户手动停止'}

                logger.info(f"开始第{round_num}轮行动...")

                # 流式模式发送轮次信息
                if streaming and result_queue:
                    result_queue.put(json.dumps({
                        'roundInfo': {
                            'current': round_num,
                            'total': rounds
                        }
                    }))

                # 每个智能体轮流发言
                for i, conv_agent in enumerate(conv_agents):
                    # 检查任务是否已被停止
                    if task_key not in _active_auto_discussions:
                        logger.info(f"自主任务已被停止，退出智能体循环: {task_key}")
                        return {'status': 'stopped', 'message': '自主任务被用户手动停止'}

                    agent_id = conv_agent.agent_id
                    agent_info = agent_map.get(agent_id, {'name': f'智能体{agent_id}'})

                    # 构建发言提示
                    prompt = f"<div style='color: #A0A0A0;'>@{agent_info.get('name')} 请你基于之前的信息，继续执行你的任务。你是该任务中的第{round_num}（共计{rounds}轮）轮行动的第{i+1}个行动者。有任何进展请更新共享记忆。\n任务主题：{topic}</div>\n"

                    # 如果是第一轮第一个发言者，提示不同
                    if round_num == 1 and i == 0:
                        prompt = f"<div style='color: #A0A0A0;'>@{agent_info.get('name')} 你是任务的第一个行动者，请就任务主题开始你的任务。请首先在共享记忆中指定计划。\n任务主题：{topic}</div>\n"

                    # 流式模式通知用户当前发言智能体信息，在通知横幅中显示
                    if streaming and result_queue:
                        agent = Agent.query.get(agent_id)
                        if agent:
                            agent_role = Role.query.get(agent.role_id) if hasattr(agent, 'role_id') and agent.role_id else None
                            role_name = agent_role.name if agent_role else "智能助手"
                            sse_callback({
                                "type": "agentInfo",
                                "turnPrompt": f"轮到智能体 {agent.name}({role_name}) 发言",
                                "agentId": str(agent_id),
                                "agentName": f"{agent.name}({role_name})",
                                "round": round_num,
                                "totalRounds": rounds,
                                "responseOrder": i + 1,
                                "totalAgents": len(conv_agents)
                            })

                    # 在开始处理智能体响应前再次检查任务是否已被停止
                    if task_key not in _active_auto_discussions:
                        logger.info(f"自主任务已被停止，跳过智能体 {agent_info.get('name')} 的处理: {task_key}")
                        return {'status': 'stopped', 'message': '自主任务被用户手动停止'}

                    # 创建一个虚拟的人类消息（不显示在会话中，仅作为智能体输入）
                    virtual_message_data = {
                        'content': prompt,
                        'target_agent_id': agent_id
                    }

                    if streaming:
                        # 流式模式使用_process_single_agent_response
                        response_completed, error_info = ConversationService._process_single_agent_response(
                            task_id=task_id,
                            conversation_id=conversation_id,
                            human_message=None,  # 虚拟消息，没有实际的human_message
                            agent_id=agent_id,
                            content=prompt,
                            sse_callback=sse_callback,  # 使用回调函数
                            result_queue=None  # 不结束流
                        )

                        if not response_completed:
                            logger.warning(f"智能体 {agent_info.get('name')} 未能成功生成响应，错误: {error_info}")

                            # 发送智能体结束信号，以便前端知道当前智能体已完成（即使失败）
                            agent = Agent.query.get(agent_id)
                            if agent:
                                agent_role = Role.query.get(agent.role_id) if hasattr(agent, 'role_id') and agent.role_id else None
                                role_name = agent_role.name if agent_role else "智能助手"

                                # 生成带有具体错误信息的内容
                                error_content = f"智能体处理失败: {agent.name}({role_name})\n错误原因: {error_info}"

                                formatted_msg = format_agent_error_done(
                                    agent_id=str(agent_id),
                                    agent_name=agent.name,
                                    role_name=role_name,
                                    timestamp=datetime.now().isoformat(),
                                    response_order=i + 1,
                                    error_content=error_content
                                )
                                sse_callback(formatted_msg["meta"])
                    else:
                        # 非流式模式使用add_message_to_conversation
                        _, agent_message = ConversationService.add_message_to_conversation(
                            conversation_id,
                            virtual_message_data,
                            is_virtual=True  # 标记为虚拟消息，不存储到数据库
                        )

                        if agent_message:
                            # 保存消息ID
                            message_ids.append(agent_message.id)
                            logger.info(f"智能体 {agent_info.get('name')} 已发言")
                        else:
                            logger.warning(f"智能体 {agent_info.get('name')} 未能成功生成响应")

                # 轮次完成后触发监督者检查
                try:
                    from app.services.supervisor_event_manager import supervisor_event_manager
                    supervisor_event_manager.on_round_completed(
                        conversation_id=conversation_id,
                        round_number=round_num
                    )
                except Exception as e:
                    logger.error(f"触发轮次完成监督者检查时出错: {str(e)}")
                    # 不影响主流程，继续执行

            # 如果需要总结，由指定智能体或第一个智能体进行总结
            if summarize and conv_agents:
                # 检查任务是否已被停止
                if task_key not in _active_auto_discussions:
                    logger.info(f"自主任务已被停止，跳过总结阶段: {task_key}")
                    return {'status': 'stopped', 'message': '自主任务被用户手动停止'}

                # 如果指定了总结智能体ID，使用指定的智能体
                if summarizer_agent_id:
                    # 检查指定的智能体是否在会话中
                    summarizer_agent = next((ca for ca in conv_agents if ca.agent_id == summarizer_agent_id), None)
                    if summarizer_agent:
                        summarizer_agent_id = summarizer_agent.agent_id
                    else:
                        # 如果指定的智能体不在会话中，使用第一个智能体
                        logger.warning(f"指定的总结智能体ID {summarizer_agent_id} 不在会话中，使用第一个智能体")
                        summarizer_agent_id = conv_agents[0].agent_id
                else:
                    # 如果没有指定总结智能体ID，使用第一个智能体
                    summarizer_agent_id = conv_agents[0].agent_id

                summarizer_agent_info = agent_map.get(summarizer_agent_id, {'name': f'智能体{summarizer_agent_id}'})

                # 创建系统提示消息，要求总结
                summary_prompt_msg = Message(
                    conversation_id=conversation_id,
                    action_task_id=task_id,  # 添加行动任务ID
                    content=f"提示：子任务已完成。请{summarizer_agent_info.get('name')}对当前任务进行总结。",
                    role="system",
                    created_at=get_current_time_with_timezone()
                )
                db.session.add(summary_prompt_msg)
                db.session.commit()
                message_ids.append(summary_prompt_msg.id)

                # 流式模式添加系统消息到队列
                if streaming and result_queue:
                    result_queue.put(json.dumps({
                        'message': {
                            'id': summary_prompt_msg.id,
                            'content': summary_prompt_msg.content,
                            'role': 'system',
                            'created_at': summary_prompt_msg.created_at.isoformat() if summary_prompt_msg.created_at else None
                        }
                    }))

                    # 通知用户总结阶段
                    agent = Agent.query.get(summarizer_agent_id)
                    if agent:
                        agent_role = Role.query.get(agent.role_id) if hasattr(agent, 'role_id') and agent.role_id else None
                        role_name = agent_role.name if agent_role else "智能助手"
                        sse_callback({
                            "type": "agentInfo",
                            "turnPrompt": f"由智能体 {agent.name}({role_name}) 总结任务",
                            "agentId": str(summarizer_agent_id),
                            "agentName": f"{agent.name}({role_name})",
                            "round": rounds,
                            "totalRounds": rounds,
                            "responseOrder": 1,
                            "totalAgents": 1,
                            "isSummarizing": True
                        })

                # 在开始总结前再次检查任务是否已被停止
                if task_key not in _active_auto_discussions:
                    logger.info(f"自主任务已被停止，跳过总结阶段: {task_key}")
                    return {'status': 'stopped', 'message': '自主任务被用户手动停止'}

                # 创建虚拟消息，要求指定智能体总结
                summary_prompt = f"<div style='color: #A0A0A0;'>@{agent.name} 请根据上面的行动内容，详细总结所有观点和结论，突出重点和共识，以及存在的分歧。请将总结记录到共享记忆中，并将最终结论写入任务结论中。\n任务主题：{topic}</div>\n"
                summary_virtual_message = {
                    'content': summary_prompt,
                    'target_agent_id': summarizer_agent_id
                }

                if streaming:
                    # 流式模式使用_process_single_agent_response
                    response_completed, error_info = ConversationService._process_single_agent_response(
                        task_id=task_id,
                        conversation_id=conversation_id,
                        human_message=None,  # 虚拟消息，没有实际的human_message
                        agent_id=summarizer_agent_id,
                        content=summary_prompt,
                        sse_callback=sse_callback,  # 使用回调函数
                        result_queue=None  # 不结束流
                    )

                    if not response_completed:
                        logger.warning(f"总结生成失败，错误: {error_info}")

                        # 发送智能体结束信号，以便前端知道当前智能体已完成（即使失败）
                        agent = Agent.query.get(summarizer_agent_id)
                        if agent:
                            agent_role = Role.query.get(agent.role_id) if hasattr(agent, 'role_id') and agent.role_id else None
                            role_name = agent_role.name if agent_role else "智能助手"

                            # 生成带有具体错误信息的内容
                            error_content = f"总结失败: {agent.name}({role_name}) 未能成功生成总结\n错误原因: {error_info}"

                            formatted_msg = format_agent_error_done(
                                agent_id=str(summarizer_agent_id),
                                agent_name=agent.name,
                                role_name=role_name,
                                timestamp=datetime.now().isoformat(),
                                response_order=1,
                                error_content=error_content
                            )
                            # 添加额外的isSummarizing字段
                            formatted_msg["meta"]["responseObj"]["response"]["isSummarizing"] = True
                            sse_callback(formatted_msg["meta"])
                else:
                    # 非流式模式使用add_message_to_conversation
                    _, summary_message = ConversationService.add_message_to_conversation(
                        conversation_id,
                        summary_virtual_message,
                        is_virtual=True
                    )

                    if summary_message:
                        message_ids.append(summary_message.id)
                        logger.info(f"总结已由{summarizer_agent_info.get('name')}完成")
                    else:
                        logger.warning("总结生成失败")

            # 创建结束讨论的系统消息
            end_msg = Message(
                conversation_id=conversation_id,
                action_task_id=task_id,  # 添加行动任务ID
                content=f"提示：自主任务已结束，共进行了{rounds}轮行动。",
                role="system",
                created_at=get_current_time_with_timezone()
            )
            db.session.add(end_msg)
            db.session.commit()
            message_ids.append(end_msg.id)

            # 流式模式添加系统消息到队列
            if streaming and result_queue:
                result_queue.put(json.dumps({
                    'message': {
                        'id': end_msg.id,
                        'content': end_msg.content,
                        'role': 'system',
                        'created_at': end_msg.created_at.isoformat() if end_msg.created_at else None
                    }
                }))

                # 发送完成事件
                formatted_done_msg = format_all_agents_done(
                    message=f'自主任务已完成，共{rounds}轮行动',
                    message_ids=message_ids
                )
                result_queue.put(serialize_message(formatted_done_msg))

                # 结束流
                result_queue.put(None)

            # 更新自主任务状态为完成
            try:
                autonomous_execution.status = 'completed'
                autonomous_execution.end_time = get_current_time_with_timezone()
                autonomous_execution.result = {
                    'status': 'success',
                    'message': f'自主任务已完成，共{rounds}轮行动',
                    'message_ids': message_ids,
                    'rounds_completed': rounds
                }
                autonomous_task.status = 'completed'
                db.session.commit()
                logger.info(f"已更新自主任务状态为完成: autonomous_task_id={autonomous_task.id}")
            except Exception as e:
                logger.error(f"更新自主任务状态失败: {str(e)}")

            # 从活动任务中移除
            task_key = f"{task_id}:{conversation_id}"
            if task_key in _active_auto_discussions:
                del _active_auto_discussions[task_key]
                logger.info(f"已从活动任务中移除自动讨论任务: {task_key}")

            return {
                'status': 'success',
                'message': f'自主任务已完成，共{rounds}轮行动',
                'message_ids': message_ids,
                'autonomous_task_id': autonomous_task.id
            }

        except Exception as e:
            logger.error(f"自主任务过程中出错: {str(e)}")
            traceback.print_exc()

            # 更新自主任务状态为失败
            try:
                autonomous_execution.status = 'failed'
                autonomous_execution.end_time = get_current_time_with_timezone()
                autonomous_execution.error_message = str(e)
                autonomous_execution.result = {
                    'status': 'error',
                    'error': str(e)
                }
                autonomous_task.status = 'stopped'
                db.session.commit()
                logger.info(f"已更新自主任务状态为失败: autonomous_task_id={autonomous_task.id}")
            except Exception as update_error:
                logger.error(f"更新自主任务状态失败: {str(update_error)}")

            # 创建错误消息
            error_msg = Message(
                conversation_id=conversation_id,
                action_task_id=task_id,  # 添加行动任务ID
                content=f"提示：自主任务过程中出错: {str(e)}",
                role="system",
                created_at=get_current_time_with_timezone()
            )
            db.session.add(error_msg)
            db.session.commit()
            message_ids.append(error_msg.id)

            # 流式模式添加错误消息到队列
            if streaming and result_queue:
                result_queue.put(json.dumps({
                    'message': {
                        'id': error_msg.id,
                        'content': error_msg.content,
                        'role': 'system',
                        'created_at': error_msg.created_at.isoformat() if error_msg.created_at else None
                    }
                }))

                # 发送错误事件
                result_queue.put(json.dumps({
                    'connectionStatus': 'error',
                    'error': str(e)
                }))

                # 结束流
                result_queue.put(None)

            # 从活动任务中移除
            task_key = f"{task_id}:{conversation_id}"
            if task_key in _active_auto_discussions:
                del _active_auto_discussions[task_key]
                logger.info(f"已从活动任务中移除自动讨论任务(错误): {task_key}")

            raise e

    except Exception as e:
        logger.error(f"启动自主讨论失败: {str(e)}")
        traceback.print_exc()

        # 流式模式发送错误事件
        if streaming and result_queue:
            result_queue.put(json.dumps({
                'connectionStatus': 'error',
                'error': str(e)
            }))
            result_queue.put(None)

        # 从活动任务中移除
        task_key = f"{task_id}:{conversation_id}"
        if task_key in _active_auto_discussions:
            del _active_auto_discussions[task_key]
            logger.info(f"已从活动任务中移除自动讨论任务(启动失败): {task_key}")

        raise e

def start_auto_discussion_stream(app_context, task_id: int, conversation_id: int,
                                rounds: int = 1, topic: str = None,
                                summarize: bool = True, result_queue: queue.Queue = None,
                                summarizer_agent_id = None, enable_planning: bool = False,
                                planner_agent_id = None):
    """启动智能体自主讨论（流式版本） - 兼容旧API"""
    return start_auto_discussion(
        task_id=task_id,
        conversation_id=conversation_id,
        rounds=rounds,
        topic=topic,
        summarize=summarize,
        streaming=True,
        app_context=app_context,
        result_queue=result_queue,
        summarizer_agent_id=summarizer_agent_id,
        enable_planning=enable_planning,
        planner_agent_id=planner_agent_id
    )

# 全局变量，用于跟踪活动的自动讨论任务
_active_auto_discussions = {}


def stop_auto_discussion(task_id: int, conversation_id: int) -> bool:
    """
    停止正在进行的自动讨论任务

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID

    Returns:
        bool: 是否成功停止任务
    """
    # 构建任务键
    task_key = f"{task_id}:{conversation_id}"

    # 记录开始停止任务
    logger.info(f"尝试停止自动讨论任务: {task_key}")

    # 检查是否有活动的自动讨论任务
    if task_key in _active_auto_discussions:
        try:
            # 获取任务信息
            task_info = _active_auto_discussions[task_key]

            # 如果有结果队列，发送取消信号
            if 'result_queue' in task_info and task_info['result_queue']:
                result_queue = task_info['result_queue']

                # 发送取消信号
                result_queue.put(json.dumps({
                    'connectionStatus': 'done',
                    'message': '自动讨论任务被用户手动停止'
                }))

                # 结束流
                result_queue.put(None)

            # 从活动任务中移除
            del _active_auto_discussions[task_key]

            # 更新自主任务状态为停止
            try:
                # 查找对应的自主任务记录
                autonomous_task = AutonomousTask.query.filter_by(
                    conversation_id=conversation_id,
                    status='active'
                ).order_by(AutonomousTask.created_at.desc()).first()

                if autonomous_task:
                    # 查找对应的执行记录
                    autonomous_execution = AutonomousTaskExecution.query.filter_by(
                        autonomous_task_id=autonomous_task.id,
                        status='running'
                    ).order_by(AutonomousTaskExecution.created_at.desc()).first()

                    if autonomous_execution:
                        autonomous_execution.status = 'stopped'
                        autonomous_execution.end_time = get_current_time_with_timezone()
                        autonomous_execution.result = {
                            'status': 'stopped',
                            'message': '自主任务被用户手动停止'
                        }

                    autonomous_task.status = 'stopped'
                    db.session.commit()
                    logger.info(f"已更新自主任务状态为停止: autonomous_task_id={autonomous_task.id}")
            except Exception as e:
                logger.error(f"更新自主任务状态失败: {str(e)}")

            # 创建系统消息，记录任务被停止
            try:
                # 创建系统消息
                end_msg = Message(
                    conversation_id=conversation_id,
                    action_task_id=task_id,
                    content="提示：自主任务被用户手动停止。",
                    role="system",
                    created_at=get_current_time_with_timezone()
                )
                db.session.add(end_msg)
                db.session.commit()

                logger.info(f"已添加任务停止系统消息: {end_msg.id}")
            except Exception as e:
                logger.error(f"添加任务停止系统消息失败: {str(e)}")

            logger.info(f"成功停止自动讨论任务: {task_key}")
            return True
        except Exception as e:
            logger.error(f"停止自动讨论任务出错: {str(e)}")
            return False
    else:
        logger.info(f"未找到活动的自动讨论任务: {task_key}")
        return False
