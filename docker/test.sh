#!/bin/bash

# ABM-LLM-V2 Docker配置测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  ABM-LLM-V2 Docker 配置测试${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 测试Docker环境
test_docker() {
    print_info "测试Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未运行"
        return 1
    fi
    
    print_success "Docker环境正常"
    
    # 检查Docker Compose
    if command -v docker-compose &> /dev/null; then
        print_success "Docker Compose (standalone) 可用"
    elif docker compose version &> /dev/null; then
        print_success "Docker Compose (plugin) 可用"
    else
        print_error "Docker Compose未安装"
        return 1
    fi
    
    return 0
}

# 测试配置文件
test_config_files() {
    print_info "测试配置文件..."
    
    local errors=0
    
    # 检查必需的文件
    local required_files=(
        "docker-compose.yml"
        "Dockerfile"
        "supervisord.conf"
        "build-image.sh"
        ".dockerignore"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "找到配置文件: $file"
        else
            print_error "缺少配置文件: $file"
            ((errors++))
        fi
    done
    
    # 检查可选文件
    local optional_files=(
        ".env.example"
    )

    for file in "${optional_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "找到可选文件: $file"
        else
            print_warning "可选文件不存在: $file"
        fi
    done
    
    # 检查环境变量文件
    if [ -f ".env" ]; then
        print_success "找到环境变量文件: .env"
    elif [ -f ".env.example" ]; then
        print_warning "未找到.env文件，但存在.env.example模板"
    else
        print_warning "未找到环境变量文件"
    fi
    
    return $errors
}

# 测试Docker Compose语法
test_compose_syntax() {
    print_info "测试Docker Compose语法..."

    local errors=0

    # 测试基础配置
    if docker-compose config &> /dev/null; then
        print_success "docker-compose.yml 语法正确"
    else
        print_error "docker-compose.yml 语法错误"
        docker-compose config
        ((errors++))
    fi

    return $errors
}

# 测试网络连通性
test_network() {
    print_info "测试网络配置..."

    # 检查端口是否被占用
    local ports=(3000 8080)
    local occupied_ports=()

    for port in "${ports[@]}"; do
        if lsof -i :$port &> /dev/null; then
            occupied_ports+=($port)
        fi
    done

    if [ ${#occupied_ports[@]} -eq 0 ]; then
        print_success "所有端口都可用"
    else
        print_warning "以下端口被占用: ${occupied_ports[*]}"
        print_info "请停止占用端口的服务或修改配置"
    fi

    return 0
}

# 测试构建依赖
test_build_dependencies() {
    print_info "测试构建依赖..."
    
    local errors=0
    
    # 检查项目根目录文件
    local root_files=(
        "../requirements.txt"
        "../run_app.py"
        "../frontend/package.json"
        "../config.conf"
    )
    
    for file in "${root_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "找到项目文件: $file"
        else
            print_error "缺少项目文件: $file"
            ((errors++))
        fi
    done
    
    return $errors
}

# 运行快速构建测试
test_quick_build() {
    print_info "运行快速构建测试..."

    print_info "测试Dockerfile语法..."
    if docker build --dry-run -f Dockerfile .. &> /dev/null; then
        print_success "Dockerfile语法测试通过"
    else
        print_error "Dockerfile语法测试失败"
        return 1
    fi

    print_info "提示: 完整构建测试请运行 ./build-image.sh --test"

    return 0
}

# 主测试函数
run_tests() {
    print_header
    
    local total_errors=0
    
    # 运行各项测试
    test_docker || ((total_errors++))
    echo ""
    
    test_config_files || ((total_errors++))
    echo ""
    
    test_compose_syntax || ((total_errors++))
    echo ""
    
    test_network || ((total_errors++))
    echo ""
    
    test_build_dependencies || ((total_errors++))
    echo ""
    
    # 可选的构建测试
    if [ "${1:-}" = "--build" ]; then
        test_quick_build || ((total_errors++))
        echo ""
    fi
    
    # 输出测试结果
    echo -e "${BLUE}================================${NC}"
    if [ $total_errors -eq 0 ]; then
        print_success "所有测试通过！Docker配置就绪。"
        echo ""
        print_info "可以使用以下命令启动服务："
        echo "  ./start.sh dev    # 开发环境"
        echo "  ./start.sh prod   # 生产环境"
        echo "  ./start.sh full   # 完整环境"
    else
        print_error "发现 $total_errors 个问题，请修复后重试。"
        return 1
    fi
    echo -e "${BLUE}================================${NC}"
    
    return 0
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --build   运行构建测试（需要更多时间）"
    echo "  --help    显示此帮助信息"
    echo ""
    echo "此脚本将测试Docker配置的完整性和正确性。"
}

# 切换到脚本所在目录
cd "$(dirname "$0")"

# 解析命令行参数
case "${1:-}" in
    "--help")
        show_help
        exit 0
        ;;
    *)
        run_tests "$@"
        ;;
esac
