---
description: 
globs: 
alwaysApply: true
---

# 项目开发规范

## 环境配置
- 整个程序运行在conda虚拟环境abm中，Python命令路径：使用 `conda activate abm` 激活环境
- 执行命令时，一定要在虚拟环境`abm`中运行，不要在base环境中
- OpenAI的API文档在https://platform.openai.com/docs/overview
- MCP的文档在https://modelcontextprotocol.io/introduction
- 前端调用后端时，正确的主机与端口配置是http://localhost:8080

## 前后端规范
- 后端所依赖的包，都要写在requirements.txt中，并且在引入新包之前要查看其有没有类似的，不要引入冗余或功能重复的包
- 后端所使用的包，尽量使用最新的
- 执行后端命令，在项目根目录中使用`python run_app.py`

## 项目结构
- **前后端分离**：
- 后端代码放在 `app` 目录
- 前端代码放在 `frontend` 目录
- 测试代码分别放在对应目录的 `tests` 文件夹中
- 【重要】创建前后端代码的时候要遵从现在的代码结构

## 包管理规范
- 前端使用 **pnpm** 安装依赖包

## 开发流程
1. 每个新功能必须先实现后端，后实现前端，同时在API.md中记录
2. 每个新功能必须编写对应的测试用例
3. 完成一个功能后，更新 `PLAN.md` 中的进度表格
4. 所有API实现必须记录到 `API.md` 文件中，确保前端正确调用

## 文档维护
- 项目只需一个 `README.md` 文件，每次有大的功能变动都需要更新以下文件
- `PLAN.md` 用于记录项目进度
- `API.md` 用于记录API规范和实现状态
- `DB.md` 是数据库结构

## 重要原则
- **不考虑向后兼容性**：未完成或未使用的函数不需要提前实现或预留
- 代码应当精简高效，避免冗余和未使用的代码
- 代码应该有较高的可维护性，安装功能、架构等做分割，尽量避免不要一个文件上千行
- 增加新功能时，务必通读相关目录结构与代码，修改老函数为主，防止多添加