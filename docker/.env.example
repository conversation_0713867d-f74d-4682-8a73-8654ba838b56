# ABM-LLM-V2 Docker环境变量配置文件（基础版）
# 复制此文件为 .env 并根据需要修改配置

# ================================
# 后端配置
# ================================

# Flask环境
FLASK_ENV=production

# 服务器配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8080

# 日志配置
LOG_LEVEL=INFO

# 编码配置
PYTHONIOENCODING=utf-8
LC_ALL=C.UTF-8
LANG=C.UTF-8

# ================================
# 前端配置
# ================================

# Node环境
NODE_ENV=development

# 前端服务器配置
FRONTEND_PORT=3000

# API配置
REACT_APP_API_URL=http://localhost:8080/api

# 开发模式下的热重载
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true
