#!/bin/bash


# https://github.com/jae-jae/fetcher-mcp
# 安装fetcher-mcp
npx playwright install chromium
npx fetcher-mcp install

# 安装filesystem
npx @modelcontextprotocol/server-filesystem install

# 安装memory
npx @modelcontextprotocol/server-memory install

# 安装searxng
npx @modelcontextprotocol/server-searxng install

# 安装sequential-thinking
npx @modelcontextprotocol/server-sequential-thinking install

# 安装fetch
pip install mcp-server-fetch

# Metatrader mcp
# Recommend: https://mcp.so/server/metatrader-mcp-server/ariadng?tab=content
# https://mcp.so/server/mcp-metatrader5-server/Qoyyuum?tab=content
# https://mcp.so/server/metatrader-mcp/chymian?tab=content
pip install mcp-metatrader5-server
# metatrader-http-server --login <YOUR_MT5_LOGIN> --password <YOUR_MT5_PASSWORD> --server <YOUR_MT5_SERVER> --host 0.0.0.0 --port 8000

# Milvus MCP
# https://github.com/zilliztech/mcp-server-milvus
