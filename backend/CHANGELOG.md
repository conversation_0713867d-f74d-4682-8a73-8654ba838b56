# Core Principles
- Always respond in Chinese
- Find and fix the root cause of problems instead of implementing temporary solutions
- Strictly follow all user-defined rules
- Before handling requests, thoroughly understand the relevant project structure and files
- Add clear comments explaining functionality when creating or modifying files
- This is a new project, so when implementing new features, do delete old implementations of the same functionality, don't keep redundant code or compatible to old code.
- Focus on implementing core functionality, don't modify unrelated code
- Keep code implementation concise and efficient, avoid unnecessary code
- Function and variable names should be concise and directly reflect their functionality
- Report errors directly without setting ambiguous statuses, ensuring quick fixes
- Avoid hardcoding constants unless explicitly requested

# Code Style
- Maintain code consistency, follow existing code style in the project
- Prefer functional and declarative programming patterns
- Use modular code design, avoid repetition
- Use clear, descriptive variable names
- Ensure type safety, use appropriate type annotations
- Keep code organization clear with logical structure

# Error Handling
- Handle errors and edge cases at the beginning of functions
- Use early returns for error conditions to avoid deep nesting
- Implement comprehensive error catching and handling mechanisms
- Provide meaningful error messages

# Testing and Validation
- Verify functionality works properly after implementation changes
- Inform users how to test new features
- Ensure code complies with the project's technical specifications

# Documentation and Comments
- Add detailed comments for all important code
- Preserve useful existing comments unless obviously incorrect or outdated
- Use concise and clear language in comments
- Document all changes and their reasons
