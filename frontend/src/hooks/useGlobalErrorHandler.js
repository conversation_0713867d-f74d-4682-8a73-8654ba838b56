/**
 * 全局错误处理Hook
 * 
 * 用于统一处理应用中的各种错误，特别是license相关错误
 */
import { useEffect } from 'react';
import { message } from 'antd';

/**
 * 全局错误处理Hook
 */
export const useGlobalErrorHandler = () => {
  useEffect(() => {
    // 监听未捕获的Promise拒绝
    const handleUnhandledRejection = (event) => {
      const error = event.reason;
      
      // 检查是否是license错误
      if (error && error.isLicenseError) {
        console.log('全局错误处理器: 捕获到license错误，已由axios拦截器处理');
        // license错误已经由axios拦截器处理，不需要额外操作
        event.preventDefault(); // 阻止默认的错误处理
        return;
      }
      
      // 检查是否是已处理的错误
      if (error && error.handled) {
        console.log('全局错误处理器: 捕获到已处理的错误，跳过');
        event.preventDefault();
        return;
      }
      
      // 处理其他未捕获的错误
      console.error('全局错误处理器: 未捕获的Promise拒绝:', error);
      
      // 根据错误类型显示不同的提示
      if (error && error.message) {
        // 检查是否是网络错误
        if (error.message.includes('网络') || error.message.includes('连接')) {
          message.error('网络连接异常，请检查网络设置');
        } else if (error.message.includes('超时')) {
          message.error('请求超时，请稍后重试');
        } else {
          // 其他错误显示具体信息
          message.error(`操作失败: ${error.message}`);
        }
      } else {
        message.error('发生未知错误，请稍后重试');
      }
      
      event.preventDefault();
    };
    
    // 监听未捕获的JavaScript错误
    const handleError = (event) => {
      console.error('全局错误处理器: 未捕获的JavaScript错误:', event.error);
      
      // 检查是否是license相关错误
      if (event.error && event.error.isLicenseError) {
        console.log('全局错误处理器: JavaScript license错误已处理');
        event.preventDefault();
        return;
      }
      
      // 显示通用错误提示
      message.error('页面发生错误，请刷新页面重试');
      event.preventDefault();
    };
    
    // 添加事件监听器
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);
    
    // 清理函数
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, []);
};

export default useGlobalErrorHandler;
