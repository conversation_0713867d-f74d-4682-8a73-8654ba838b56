"""
HTTP连接管理器

直接管理HTTP连接和线程，用于流式请求的取消
"""

import threading
import time
import socket
import logging
import signal
import os
from typing import Dict, Optional, Any
import requests
from urllib3.poolmanager import PoolManager
from urllib3.util.connection import create_connection

logger = logging.getLogger(__name__)

class ConnectionManager:
    """HTTP连接管理器 - 直接管理连接和线程"""

    def __init__(self):
        self._active_connections: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        # 线程中断标志，用于强制终止流式处理
        self._thread_interrupt_flags: Dict[str, threading.Event] = {}

    def register_connection(self, request_id: str, session: requests.Session,
                          response: Optional[requests.Response] = None,
                          thread: Optional[threading.Thread] = None) -> None:
        """
        注册HTTP连接

        Args:
            request_id: 请求ID
            session: requests会话对象
            response: 响应对象（可选）
            thread: 处理线程（可选）
        """
        with self._lock:
            # 创建线程中断标志
            interrupt_flag = threading.Event()
            self._thread_interrupt_flags[request_id] = interrupt_flag

            self._active_connections[request_id] = {
                'session': session,
                'response': response,
                'thread': thread,
                'created_at': time.time(),
                'cancelled': False,
                'interrupt_flag': interrupt_flag
            }
            logger.info(f"[连接管理器] 已注册连接: {request_id}")

    def update_connection(self, request_id: str, response: requests.Response = None,
                         thread: threading.Thread = None) -> None:
        """
        更新连接信息

        Args:
            request_id: 请求ID
            response: 响应对象
            thread: 处理线程
        """
        with self._lock:
            if request_id in self._active_connections:
                if response:
                    self._active_connections[request_id]['response'] = response
                if thread:
                    self._active_connections[request_id]['thread'] = thread
                logger.debug(f"[连接管理器] 已更新连接: {request_id}")

    def force_close_connection(self, request_id: str) -> bool:
        """
        强制关闭连接 - 直接命中要害

        Args:
            request_id: 请求ID

        Returns:
            bool: 是否成功关闭
        """
        with self._lock:
            if request_id not in self._active_connections:
                logger.info(f"[连接管理器] 连接不存在: {request_id}")
                return True

            connection_info = self._active_connections[request_id]

            # 标记为已取消
            connection_info['cancelled'] = True

            # 设置线程中断标志
            interrupt_flag = connection_info.get('interrupt_flag')
            if interrupt_flag:
                interrupt_flag.set()
                logger.info(f"[连接管理器] 已设置线程中断标志: {request_id}")

            success = False

            try:
                # 1. 强制关闭底层socket连接
                response = connection_info.get('response')
                if response:
                    self._force_close_socket(response, request_id)
                    success = True

                # 2. 关闭session和连接池
                session = connection_info.get('session')
                if session:
                    self._force_close_session(session, request_id)
                    success = True

                # 3. 如果有处理线程，尝试终止（注意：这是危险操作，但在这种情况下是必要的）
                thread = connection_info.get('thread')
                if thread and thread.is_alive():
                    logger.warning(f"[连接管理器] 检测到活动线程，但Python不支持强制终止线程: {request_id}")
                    # Python不支持强制终止线程，但我们已经关闭了底层连接
                    # 线程会在下次尝试读取数据时收到异常并退出

                logger.info(f"[连接管理器] 成功强制关闭连接: {request_id}")

            except Exception as e:
                logger.error(f"[连接管理器] 强制关闭连接时出错: {request_id}, 错误: {str(e)}")
                # 即使出错也认为成功，避免前端卡住
                success = True

            finally:
                # 从活动连接中移除
                del self._active_connections[request_id]
                # 注意：不立即清理中断标志，让线程有时间检测到中断状态
                # 中断标志将在cleanup_old_connections中清理，或者在下次注册同名连接时覆盖
                logger.info(f"[连接管理器] 已从活动连接中移除: {request_id}")
                logger.debug(f"[连接管理器] 中断标志保留，等待线程检测: {request_id}")

            return success

    def _force_close_socket(self, response: requests.Response, request_id: str) -> None:
        """强制关闭底层socket连接"""
        try:
            # 方法1: 关闭响应对象
            if hasattr(response, 'close'):
                response.close()
                logger.debug(f"[连接管理器] 已关闭响应对象: {request_id}")

            # 方法2: 关闭原始响应流
            if hasattr(response, 'raw') and response.raw:
                if hasattr(response.raw, 'close'):
                    response.raw.close()
                    logger.debug(f"[连接管理器] 已关闭原始响应流: {request_id}")

                # 方法3: 关闭底层连接
                if hasattr(response.raw, '_connection'):
                    conn = response.raw._connection
                    if conn and hasattr(conn, 'sock') and conn.sock:
                        try:
                            # 强制关闭socket
                            conn.sock.shutdown(socket.SHUT_RDWR)
                            conn.sock.close()
                            logger.debug(f"[连接管理器] 已强制关闭底层socket: {request_id}")
                        except Exception as e:
                            logger.debug(f"[连接管理器] 关闭socket时出错: {str(e)}")

                # 方法4: 关闭文件指针
                if hasattr(response.raw, '_fp') and response.raw._fp:
                    if hasattr(response.raw._fp, 'close'):
                        response.raw._fp.close()
                        logger.debug(f"[连接管理器] 已关闭文件指针: {request_id}")

                # 方法5: 尝试关闭urllib3连接
                if hasattr(response.raw, '_original_response'):
                    orig_resp = response.raw._original_response
                    if hasattr(orig_resp, 'close'):
                        orig_resp.close()
                        logger.debug(f"[连接管理器] 已关闭urllib3原始响应: {request_id}")

        except Exception as e:
            logger.debug(f"[连接管理器] 关闭socket时出错: {request_id}, 错误: {str(e)}")

    def _force_close_session(self, session: requests.Session, request_id: str) -> None:
        """强制关闭session和连接池"""
        try:
            # 关闭session
            if hasattr(session, 'close'):
                session.close()
                logger.debug(f"[连接管理器] 已关闭session: {request_id}")

            # 强制关闭连接池中的所有连接
            if hasattr(session, 'adapters'):
                for adapter in session.adapters.values():
                    if hasattr(adapter, 'poolmanager') and adapter.poolmanager:
                        # 关闭连接池
                        if hasattr(adapter.poolmanager, 'clear'):
                            adapter.poolmanager.clear()
                            logger.debug(f"[连接管理器] 已清空连接池: {request_id}")

                        # 强制关闭所有池中的连接
                        if hasattr(adapter.poolmanager, 'pools'):
                            for pool in adapter.poolmanager.pools.values():
                                if hasattr(pool, 'close'):
                                    pool.close()
                                    logger.debug(f"[连接管理器] 已关闭连接池: {request_id}")

        except Exception as e:
            logger.debug(f"[连接管理器] 关闭session时出错: {request_id}, 错误: {str(e)}")

    def is_cancelled(self, request_id: str) -> bool:
        """
        检查连接是否已被取消

        Args:
            request_id: 请求ID

        Returns:
            bool: 是否已被取消
        """
        with self._lock:
            if request_id in self._active_connections:
                return self._active_connections[request_id].get('cancelled', False)
            return False

    def should_interrupt(self, request_id: str) -> bool:
        """
        检查线程是否应该中断

        Args:
            request_id: 请求ID

        Returns:
            bool: 是否应该中断
        """
        with self._lock:
            if request_id in self._thread_interrupt_flags:
                return self._thread_interrupt_flags[request_id].is_set()
            # 如果连接已被移除，也认为应该中断
            return request_id not in self._active_connections

    def cleanup_old_connections(self, max_age_seconds: int = 3600) -> None:
        """
        清理超时的连接和中断标志

        Args:
            max_age_seconds: 最大存活时间（秒）
        """
        current_time = time.time()
        to_remove = []

        with self._lock:
            for request_id, conn_info in self._active_connections.items():
                if current_time - conn_info['created_at'] > max_age_seconds:
                    to_remove.append(request_id)

        for request_id in to_remove:
            logger.info(f"[连接管理器] 清理超时连接: {request_id}")
            self.force_close_connection(request_id)

        # 清理孤立的中断标志（没有对应活动连接的）
        with self._lock:
            orphaned_flags = []
            for request_id in self._thread_interrupt_flags:
                if request_id not in self._active_connections:
                    # 检查标志是否已经被设置了足够长的时间（给线程时间检测）
                    if self._thread_interrupt_flags[request_id].is_set():
                        orphaned_flags.append(request_id)

            for request_id in orphaned_flags:
                del self._thread_interrupt_flags[request_id]
                logger.debug(f"[连接管理器] 清理孤立的中断标志: {request_id}")

    def clear_interrupt_flag(self, request_id: str) -> bool:
        """
        清理特定的中断标志

        Args:
            request_id: 请求ID

        Returns:
            bool: 是否成功清理
        """
        with self._lock:
            if request_id in self._thread_interrupt_flags:
                del self._thread_interrupt_flags[request_id]
                logger.debug(f"[连接管理器] 已清理中断标志: {request_id}")
                return True
            return False

    def get_active_connections(self) -> Dict[str, Dict[str, Any]]:
        """获取所有活动连接信息"""
        with self._lock:
            return dict(self._active_connections)

    def get_interrupt_flags_count(self) -> int:
        """获取当前中断标志数量（用于调试）"""
        with self._lock:
            return len(self._thread_interrupt_flags)

# 全局连接管理器实例
connection_manager = ConnectionManager()
