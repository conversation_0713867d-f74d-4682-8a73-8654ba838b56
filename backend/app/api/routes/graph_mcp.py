"""
图谱增强MCP服务接口

提供OpenAI兼容的MCP服务接口，支持外部应用调用图谱增强功能
"""

import json
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, Response, stream_with_context

from app.models import GraphEnhancement
from app.services.graph_enhancement_service import GraphEnhancementService

# 创建Blueprint
graph_mcp_bp = Blueprint('graph_mcp_api', __name__)

# 图谱增强服务实例
graph_service = GraphEnhancementService()

# ==================== OpenAI兼容接口 ====================

@graph_mcp_bp.route('/v1/chat/completions', methods=['POST'])
def openai_compatible_chat():
    """OpenAI兼容的聊天完成接口"""
    try:
        data = request.get_json()
        
        # 解析OpenAI格式的请求
        messages = data.get('messages', [])
        model = data.get('model', 'graph-enhancement')
        stream = data.get('stream', False)
        temperature = data.get('temperature', 0.7)
        max_tokens = data.get('max_tokens', 2000)
        
        # 提取用户查询
        user_message = None
        for message in messages:
            if message.get('role') == 'user':
                user_message = message.get('content', '')
                break
        
        if not user_message:
            return jsonify({
                'error': {
                    'message': '未找到用户消息',
                    'type': 'invalid_request_error',
                    'code': 'missing_user_message'
                }
            }), 400
        
        # 获取图谱增强配置
        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'error': {
                    'message': '图谱增强服务未启用',
                    'type': 'service_unavailable_error',
                    'code': 'service_disabled'
                }
            }), 503
        
        # 执行图谱增强查询
        query_params = {
            'mode': 'hybrid',
            'top_k': 60,
            'chunk_top_k': 10,
            'response_type': 'Multiple Paragraphs'
        }
        
        success, result = graph_service.query(config, user_message, query_params)
        
        if not success:
            return jsonify({
                'error': {
                    'message': f'查询失败: {result}',
                    'type': 'internal_error',
                    'code': 'query_failed'
                }
            }), 500
        
        # 构建OpenAI格式的响应
        response_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
        created = int(datetime.now().timestamp())
        
        if stream:
            # 流式响应
            def generate_stream():
                # 开始流
                start_chunk = {
                    'id': response_id,
                    'object': 'chat.completion.chunk',
                    'created': created,
                    'model': model,
                    'choices': [{
                        'index': 0,
                        'delta': {'role': 'assistant', 'content': ''},
                        'finish_reason': None
                    }]
                }
                yield f"data: {json.dumps(start_chunk)}\n\n"

                # 分块发送内容
                chunk_size = 50
                content = str(result)
                for i in range(0, len(content), chunk_size):
                    chunk = content[i:i+chunk_size]
                    content_chunk = {
                        'id': response_id,
                        'object': 'chat.completion.chunk',
                        'created': created,
                        'model': model,
                        'choices': [{
                            'index': 0,
                            'delta': {'content': chunk},
                            'finish_reason': None
                        }]
                    }
                    yield f"data: {json.dumps(content_chunk)}\n\n"

                # 结束流
                end_chunk = {
                    'id': response_id,
                    'object': 'chat.completion.chunk',
                    'created': created,
                    'model': model,
                    'choices': [{
                        'index': 0,
                        'delta': {},
                        'finish_reason': 'stop'
                    }]
                }
                yield f"data: {json.dumps(end_chunk)}\n\n"
                yield "data: [DONE]\n\n"
            
            return Response(
                stream_with_context(generate_stream()),
                mimetype='text/plain',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'X-Accel-Buffering': 'no'
                }
            )
        else:
            # 非流式响应
            return jsonify({
                'id': response_id,
                'object': 'chat.completion',
                'created': created,
                'model': model,
                'choices': [{
                    'index': 0,
                    'message': {
                        'role': 'assistant',
                        'content': str(result)
                    },
                    'finish_reason': 'stop'
                }],
                'usage': {
                    'prompt_tokens': len(user_message.split()),
                    'completion_tokens': len(str(result).split()),
                    'total_tokens': len(user_message.split()) + len(str(result).split())
                }
            })
        
    except Exception as e:
        current_app.logger.error(f"OpenAI兼容接口错误: {e}")
        return jsonify({
            'error': {
                'message': f'内部服务器错误: {str(e)}',
                'type': 'internal_error',
                'code': 'server_error'
            }
        }), 500

@graph_mcp_bp.route('/v1/models', methods=['GET'])
def list_models():
    """列出可用模型"""
    return jsonify({
        'object': 'list',
        'data': [
            {
                'id': 'graph-enhancement',
                'object': 'model',
                'created': int(datetime.now().timestamp()),
                'owned_by': 'graph-enhancement-service',
                'permission': [],
                'root': 'graph-enhancement',
                'parent': None
            }
        ]
    })

# ==================== MCP特定接口 ====================

@graph_mcp_bp.route('/mcp/tools', methods=['GET'])
def list_mcp_tools():
    """列出MCP工具"""
    return jsonify({
        'tools': [
            {
                'name': 'graph_query',
                'description': '使用图谱增强技术查询知识库',
                'inputSchema': {
                    'type': 'object',
                    'properties': {
                        'query': {
                            'type': 'string',
                            'description': '要查询的问题或内容'
                        },
                        'mode': {
                            'type': 'string',
                            'enum': ['hybrid', 'local', 'global', 'mix'],
                            'description': '查询模式',
                            'default': 'hybrid'
                        },
                        'top_k': {
                            'type': 'integer',
                            'description': '返回结果数量',
                            'default': 60,
                            'minimum': 1,
                            'maximum': 200
                        }
                    },
                    'required': ['query']
                }
            },
            {
                'name': 'graph_add_document',
                'description': '向图谱增强系统添加文档',
                'inputSchema': {
                    'type': 'object',
                    'properties': {
                        'content': {
                            'type': 'string',
                            'description': '要添加的文档内容'
                        }
                    },
                    'required': ['content']
                }
            },
            {
                'name': 'graph_status',
                'description': '获取图谱增强系统状态',
                'inputSchema': {
                    'type': 'object',
                    'properties': {}
                }
            }
        ]
    })

@graph_mcp_bp.route('/mcp/tools/call', methods=['POST'])
def call_mcp_tool():
    """调用MCP工具"""
    try:
        data = request.get_json()
        
        tool_name = data.get('name')
        arguments = data.get('arguments', {})
        
        if tool_name == 'graph_query':
            return handle_graph_query(arguments)
        elif tool_name == 'graph_add_document':
            return handle_graph_add_document(arguments)
        elif tool_name == 'graph_status':
            return handle_graph_status(arguments)
        else:
            return jsonify({
                'error': f'未知工具: {tool_name}'
            }), 400
            
    except Exception as e:
        current_app.logger.error(f"MCP工具调用错误: {e}")
        return jsonify({
            'error': f'工具调用失败: {str(e)}'
        }), 500

def handle_graph_query(arguments):
    """处理图谱查询工具"""
    query = arguments.get('query')
    if not query:
        return jsonify({'error': '查询内容不能为空'}), 400
    
    config = GraphEnhancement.query.first()
    if not config or not config.enabled:
        return jsonify({'error': '图谱增强服务未启用'}), 503
    
    query_params = {
        'mode': arguments.get('mode', 'hybrid'),
        'top_k': arguments.get('top_k', 60),
        'chunk_top_k': 10,
        'response_type': 'Multiple Paragraphs'
    }
    
    success, result = graph_service.query(config, query, query_params)
    
    if success:
        return jsonify({
            'content': [
                {
                    'type': 'text',
                    'text': str(result)
                }
            ]
        })
    else:
        return jsonify({'error': f'查询失败: {result}'}), 500

def handle_graph_add_document(arguments):
    """处理添加文档工具"""
    content = arguments.get('content')
    if not content:
        return jsonify({'error': '文档内容不能为空'}), 400
    
    config = GraphEnhancement.query.first()
    if not config or not config.enabled:
        return jsonify({'error': '图谱增强服务未启用'}), 503
    
    success, message = graph_service.add_documents(config, [content])
    
    if success:
        return jsonify({
            'content': [
                {
                    'type': 'text',
                    'text': message
                }
            ]
        })
    else:
        return jsonify({'error': message}), 500

def handle_graph_status(arguments):
    """处理状态查询工具"""
    config = GraphEnhancement.query.first()
    if not config:
        return jsonify({
            'content': [
                {
                    'type': 'text',
                    'text': '图谱增强服务未配置'
                }
            ]
        })
    
    status = graph_service.get_status(config)
    
    return jsonify({
        'content': [
            {
                'type': 'text',
                'text': json.dumps(status, ensure_ascii=False, indent=2)
            }
        ]
    })
