// 自动生成的行动空间数据
export const actionSpacesData = {
  "action_spaces": [
    {
      "id": 1,
      "name": "某集团智能专家协作平台",
      "description": "基于多智能体技术的企业级决策支持系统，连接某集团内外部专家资源，通过多种协作模式实现跨领域、跨部门的高效协同",
      "settings": {
        "collaborative_modes": [
          "sequential",
          "panel",
          "debate",
          "collaborative"
        ],
        "default_mode": "debate"
      },
      "rule_sets": [
        {
          "id": 1,
          "name": "基础问答规则集",
          "description": "提供问答服务的基本规则集"
        }
      ],
      "tags": [
        {
          "id": 1,
          "name": "制造业",
          "type": "industry",
          "color": "#1890ff"
        },
        {
          "id": 5,
          "name": "家电",
          "type": "industry",
          "color": "#722ed1"
        },
        {
          "id": 6,
          "name": "消费电子",
          "type": "industry",
          "color": "#13c2c2"
        },
        {
          "id": 17,
          "name": "产品研发",
          "type": "scenario",
          "color": "#1890ff"
        },
        {
          "id": 18,
          "name": "供应链",
          "type": "scenario",
          "color": "#52c41a"
        },
        {
          "id": 19,
          "name": "客户服务",
          "type": "scenario",
          "color": "#faad14"
        },
        {
          "id": 20,
          "name": "市场营销",
          "type": "scenario",
          "color": "#eb2f96"
        },
        {
          "id": 23,
          "name": "国际业务",
          "type": "scenario",
          "color": "#2f54eb"
        },
        {
          "id": 24,
          "name": "生产制造",
          "type": "scenario",
          "color": "#fa541c"
        }
      ],
      "created_at": "2025-03-31T06:07:58.346110",
      "updated_at": "2025-03-31T06:07:58.346111"
    },
    {
      "id": 2,
      "name": "冰箱产品创新研发",
      "description": "某集团新一代智能冰箱研发场景，整合产品经理、硬件工程师、软件架构师等多方专家意见，提升产品创新效率和质量",
      "settings": {
        "collaborative_modes": [
          "debate",
          "collaborative"
        ],
        "default_mode": "debate",
        "company": "某集团"
      },
      "rule_sets": [
        {
          "id": 2,
          "name": "团队协作规则集",
          "description": "多角色协作的规则集"
        }
      ],
      "tags": [
        {
          "id": 1,
          "name": "制造业",
          "type": "industry",
          "color": "#1890ff"
        },
        {
          "id": 5,
          "name": "家电",
          "type": "industry",
          "color": "#722ed1"
        },
        {
          "id": 6,
          "name": "消费电子",
          "type": "industry",
          "color": "#13c2c2"
        },
        {
          "id": 17,
          "name": "产品研发",
          "type": "scenario",
          "color": "#1890ff"
        },
        {
          "id": 28,
          "name": "研发",
          "type": "scenario",
          "color": "#722ed1"
        },
        {
          "id": 21,
          "name": "质量控制",
          "type": "scenario",
          "color": "#722ed1"
        }
      ],
      "created_at": "2025-03-31T06:07:58.348773",
      "updated_at": "2025-03-31T06:07:58.348774"
    },
    {
      "id": 3,
      "name": "全球供应链风险管理",
      "description": "某集团全球供应链风险预警与应对场景，提前识别供应链风险，快速制定应对策略，确保生产连续性",
      "settings": {
        "collaborative_modes": [
          "collaborative"
        ],
        "default_mode": "collaborative",
        "company": "某集团"
      },
      "rule_sets": [
        {
          "id": 3,
          "name": "教育学习规则集",
          "description": "教育和学习场景的规则集"
        }
      ],
      "tags": [
        {
          "id": 1,
          "name": "制造业",
          "type": "industry",
          "color": "#1890ff"
        },
        {
          "id": 5,
          "name": "家电",
          "type": "industry",
          "color": "#722ed1"
        },
        {
          "id": 9,
          "name": "物流",
          "type": "industry",
          "color": "#a0d911"
        },
        {
          "id": 18,
          "name": "供应链",
          "type": "scenario",
          "color": "#52c41a"
        },
        {
          "id": 22,
          "name": "风险管理",
          "type": "scenario",
          "color": "#13c2c2"
        },
        {
          "id": 23,
          "name": "国际业务",
          "type": "scenario",
          "color": "#2f54eb"
        },
        {
          "id": 33,
          "name": "资源管理",
          "type": "scenario",
          "color": "#1890ff"
        }
      ],
      "created_at": "2025-03-31T06:07:58.348985",
      "updated_at": "2025-03-31T06:07:58.348986"
    },
    {
      "id": 4,
      "name": "家电技术服务支持",
      "description": "某集团客户服务场景，加速复杂技术问题的解决，提高一次解决率和客户满意度",
      "settings": {
        "collaborative_modes": [
          "sequential"
        ],
        "default_mode": "sequential",
        "company": "某集团"
      },
      "rule_sets": [
        {
          "id": 4,
          "name": "企业战略规则集",
          "description": "企业战略决策和竞争分析规则"
        },
        {
          "id": 5,
          "name": "业务风险评估规则集",
          "description": "业务风险评估和管理的规则集"
        }
      ],
      "tags": [
        {
          "id": 5,
          "name": "家电",
          "type": "industry",
          "color": "#722ed1"
        },
        {
          "id": 8,
          "name": "服务业",
          "type": "industry",
          "color": "#fa541c"
        },
        {
          "id": 19,
          "name": "客户服务",
          "type": "scenario",
          "color": "#faad14"
        },
        {
          "id": 29,
          "name": "诊断",
          "type": "scenario",
          "color": "#13c2c2"
        },
        {
          "id": 21,
          "name": "质量控制",
          "type": "scenario",
          "color": "#722ed1"
        }
      ],
      "created_at": "2025-03-31T06:07:58.349165",
      "updated_at": "2025-03-31T06:07:58.349166"
    },
    {
      "id": 5,
      "name": "新品营销策略制定",
      "description": "某集团新产品上市的全渠道整合营销策略制定场景，最大化市场覆盖和宣传效果",
      "settings": {
        "collaborative_modes": [
          "panel"
        ],
        "default_mode": "panel",
        "company": "某集团"
      },
      "rule_sets": [
        {
          "id": 6,
          "name": "法律分析规则集",
          "description": "法律案例分析和辩护策略制定规则"
        },
        {
          "id": 7,
          "name": "法律证据评估规则集",
          "description": "法律证据收集和评估的规则集"
        }
      ],
      "tags": [
        {
          "id": 5,
          "name": "家电",
          "type": "industry",
          "color": "#722ed1"
        },
        {
          "id": 6,
          "name": "消费电子",
          "type": "industry",
          "color": "#13c2c2"
        },
        {
          "id": 4,
          "name": "零售业",
          "type": "industry",
          "color": "#eb2f96"
        },
        {
          "id": 20,
          "name": "市场营销",
          "type": "scenario",
          "color": "#eb2f96"
        },
        {
          "id": 25,
          "name": "销售",
          "type": "scenario",
          "color": "#a0d911"
        },
        {
          "id": 31,
          "name": "战略规划",
          "type": "scenario",
          "color": "#a0d911"
        }
      ],
      "created_at": "2025-03-31T06:07:58.349326",
      "updated_at": "2025-03-31T06:07:58.349327"
    },
    {
      "id": 6,
      "name": "跨文化市场拓展",
      "description": "某产品进入新国际市场的策略制定场景，制定适应本地文化的市场进入策略，实现海外市场快速渗透",
      "settings": {
        "collaborative_modes": [
          "debate"
        ],
        "default_mode": "debate",
        "company": "某集团"
      },
      "rule_sets": [
        {
          "id": 8,
          "name": "个性化教学规则集",
          "description": "个性化教学和学习路径设计的规则集"
        }
      ],
      "tags": [
        {
          "id": 5,
          "name": "家电",
          "type": "industry",
          "color": "#722ed1"
        },
        {
          "id": 6,
          "name": "消费电子",
          "type": "industry",
          "color": "#13c2c2"
        },
        {
          "id": 4,
          "name": "零售业",
          "type": "industry",
          "color": "#eb2f96"
        },
        {
          "id": 23,
          "name": "国际业务",
          "type": "scenario",
          "color": "#2f54eb"
        },
        {
          "id": 20,
          "name": "市场营销",
          "type": "scenario",
          "color": "#eb2f96"
        },
        {
          "id": 31,
          "name": "战略规划",
          "type": "scenario",
          "color": "#a0d911"
        }
      ],
      "created_at": "2025-03-31T06:07:58.349493",
      "updated_at": "2025-03-31T06:07:58.349494"
    },
    {
      "id": 7,
      "name": "智能制造工艺优化",
      "description": "某集团智能制造场景，提升生产线效率，降低能源消耗，保持产品质量稳定性",
      "settings": {
        "collaborative_modes": [
          "collaborative"
        ],
        "default_mode": "collaborative",
        "company": "某集团"
      },
      "rule_sets": [
        {
          "id": 9,
          "name": "农业生产规则集",
          "description": "农业生产优化和资源分配规则"
        },
        {
          "id": 10,
          "name": "农业环境规则集",
          "description": "农业环境因素和可持续发展规则"
        }
      ],
      "tags": [
        {
          "id": 1,
          "name": "制造业",
          "type": "industry",
          "color": "#1890ff"
        },
        {
          "id": 5,
          "name": "家电",
          "type": "industry",
          "color": "#722ed1"
        },
        {
          "id": 11,
          "name": "工业",
          "type": "industry",
          "color": "#eb2f96"
        },
        {
          "id": 24,
          "name": "生产制造",
          "type": "scenario",
          "color": "#fa541c"
        },
        {
          "id": 21,
          "name": "质量控制",
          "type": "scenario",
          "color": "#722ed1"
        },
        {
          "id": 33,
          "name": "资源管理",
          "type": "scenario",
          "color": "#1890ff"
        }
      ],
      "created_at": "2025-03-31T06:07:58.349649",
      "updated_at": "2025-03-31T06:07:58.349649"
    },
    {
      "id": 8,
      "name": "行动空间1",
      "description": "这是第一个行动空间的描述",
      "settings": {},
      "rule_sets": [
        {
          "id": 11,
          "name": "医疗会诊规则集",
          "description": "促进跨专科协作和知识整合，提高诊断准确率和治疗方案质量"
        }
      ],
      "tags": [],
      "created_at": "2025-03-31T06:07:58.349794",
      "updated_at": "2025-03-31T06:07:58.349795"
    },
    {
      "id": 9,
      "name": "智能助手空间",
      "description": "用于日常交互的智能助手空间，提供问答、创意和任务执行等功能。",
      "settings": {
        "default_mode": "sequential",
        "max_agents": 5,
        "enable_environment_variables": true
      },
      "rule_sets": [
        {
          "id": 12,
          "name": "技术评估规则集",
          "description": "技术架构评估和优化建议规则"
        },
        {
          "id": 13,
          "name": "技术实施规则集",
          "description": "技术方案实施和风险控制规则"
        }
      ],
      "tags": [],
      "created_at": "2025-03-31T06:07:58.349939",
      "updated_at": "2025-03-31T06:07:58.349940"
    },
    {
      "id": 10,
      "name": "团队协作空间",
      "description": "多角色协作共同解决问题的空间，支持辩论、头脑风暴和团队决策。",
      "settings": {
        "default_mode": "panel",
        "max_agents": 10,
        "enable_voting": true,
        "enable_environment_variables": true
      },
      "rule_sets": [],
      "tags": [],
      "created_at": "2025-03-31T06:07:58.350087",
      "updated_at": "2025-03-31T06:07:58.350088"
    },
    {
      "id": 11,
      "name": "教育学习空间",
      "description": "专注于知识传授和学习的空间，适合教学和自学场景。",
      "settings": {
        "default_mode": "sequential",
        "max_agents": 3,
        "enable_assessment": true,
        "enable_environment_variables": true
      },
      "rule_sets": [],
      "tags": [],
      "created_at": "2025-03-31T06:07:58.350241",
      "updated_at": "2025-03-31T06:07:58.350242"
    },
    {
      "id": 12,
      "name": "企业决策分析空间",
      "description": "企业战略决策和竞争分析专用空间，支持多角色协作分析商业问题。",
      "settings": {
        "default_mode": "panel",
        "max_agents": 8,
        "enable_voting": true,
        "enable_environment_variables": true,
        "enable_resource_analysis": true
      },
      "rule_sets": [],
      "tags": [],
      "created_at": "2025-03-31T06:07:58.350389",
      "updated_at": "2025-03-31T06:07:58.350389"
    },
    {
      "id": 13,
      "name": "法律分析空间",
      "description": "法律案例分析和辩护策略制定专用空间，适合法律相关问题讨论。",
      "settings": {
        "default_mode": "debate",
        "max_agents": 6,
        "enable_evidence_management": true,
        "enable_environment_variables": true,
        "enable_precedent_search": true
      },
      "rule_sets": [],
      "tags": [],
      "created_at": "2025-03-31T06:07:58.350531",
      "updated_at": "2025-03-31T06:07:58.350531"
    },
    {
      "id": 14,
      "name": "教育辅助空间",
      "description": "智能教育和个性化学习路径设计专用空间，支持因材施教。",
      "settings": {
        "default_mode": "sequential",
        "max_agents": 4,
        "enable_assessment": true,
        "enable_environment_variables": true,
        "enable_learning_path": true
      },
      "rule_sets": [],
      "tags": [],
      "created_at": "2025-03-31T06:07:58.350674",
      "updated_at": "2025-03-31T06:07:58.350675"
    },
    {
      "id": 15,
      "name": "农业智能分析空间",
      "description": "农业生产优化和资源分配专用空间，支持农业数据分析和决策。",
      "settings": {
        "default_mode": "collaborative",
        "max_agents": 5,
        "enable_environment_variables": true,
        "enable_resource_optimization": true,
        "enable_weather_integration": true
      },
      "rule_sets": [],
      "tags": [],
      "created_at": "2025-03-31T06:07:58.350830",
      "updated_at": "2025-03-31T06:07:58.350831"
    },
    {
      "id": 16,
      "name": "医疗诊断空间",
      "description": "医疗诊断和治疗方案制定专用空间，支持多专家会诊和病例讨论。",
      "settings": {
        "default_mode": "panel",
        "max_agents": 7,
        "enable_environment_variables": true,
        "enable_diagnosis_verification": true,
        "enable_treatment_planning": true
      },
      "rule_sets": [],
      "tags": [],
      "created_at": "2025-03-31T06:07:58.350991",
      "updated_at": "2025-03-31T06:07:58.350992"
    },
    {
      "id": 17,
      "name": "技术评估空间",
      "description": "技术架构评估和优化建议专用空间，适合技术决策和架构讨论。",
      "settings": {
        "default_mode": "collaborative",
        "max_agents": 6,
        "enable_environment_variables": true,
        "enable_code_analysis": true,
        "enable_architecture_review": true
      },
      "rule_sets": [],
      "tags": [],
      "created_at": "2025-03-31T06:07:58.351146",
      "updated_at": "2025-03-31T06:07:58.351146"
    }
  ]
};
