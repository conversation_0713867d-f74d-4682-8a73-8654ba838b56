"""
图谱增强API路由

处理与图谱增强相关的所有API请求，包括：
- 配置管理（增删改查）
- 状态查询
- 测试查询
- 框架集成
"""

import os
import json
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from sqlalchemy.exc import IntegrityError

from app.models import GraphEnhancement, db
from app.services.graph_enhancement_service import GraphEnhancementService

# 创建Blueprint
graph_enhancement_bp = Blueprint('graph_enhancement_api', __name__)

# 图谱增强服务实例
graph_service = GraphEnhancementService()

# ==================== 配置管理接口 ====================

@graph_enhancement_bp.route('/graph-enhancement/config', methods=['GET'])
def get_graph_enhancement_config():
    """获取图谱增强配置"""
    try:
        # 获取或创建默认配置
        config = GraphEnhancement.query.first()
        if not config:
            # 创建默认配置
            config = GraphEnhancement(
                name='默认图谱增强配置',
                description='系统默认的图谱增强配置',
                enabled=False,
                framework='lightrag'
            )
            db.session.add(config)
            db.session.commit()
        
        return jsonify({
            'success': True,
            'data': {
                'id': config.id,
                'enabled': config.enabled,
                'framework': config.framework,
                'name': config.name,
                'description': config.description,
                'working_dir': config.working_dir,
                'llm_config': config.llm_config,
                'embedding_config': config.embedding_config,
                'default_query_mode': config.default_query_mode,
                'top_k': config.top_k,
                'chunk_top_k': config.chunk_top_k,
                'max_entity_tokens': config.max_entity_tokens,
                'max_relation_tokens': config.max_relation_tokens,
                'framework_config': config.framework_config or {},
                'status': config.status or {},
                'created_at': config.created_at.isoformat() if config.created_at else None,
                'updated_at': config.updated_at.isoformat() if config.updated_at else None
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取图谱增强配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取图谱增强配置失败: {str(e)}'
        }), 500

@graph_enhancement_bp.route('/graph-enhancement/config', methods=['POST'])
def update_graph_enhancement_config():
    """更新图谱增强配置"""
    try:
        data = request.get_json()
        
        # 获取或创建配置
        config = GraphEnhancement.query.first()
        if not config:
            config = GraphEnhancement(
                name='默认图谱增强配置',
                description='系统默认的图谱增强配置'
            )
            db.session.add(config)
        
        # 更新配置字段
        if 'enabled' in data:
            config.enabled = data['enabled']
        if 'framework' in data:
            config.framework = data['framework']
        if 'name' in data:
            config.name = data['name']
        if 'description' in data:
            config.description = data['description']
        if 'working_dir' in data:
            config.working_dir = data['working_dir']
        if 'llm_config' in data:
            config.llm_config = data['llm_config']
        if 'embedding_config' in data:
            config.embedding_config = data['embedding_config']
        if 'default_query_mode' in data:
            config.default_query_mode = data['default_query_mode']
        if 'top_k' in data:
            config.top_k = data['top_k']
        if 'chunk_top_k' in data:
            config.chunk_top_k = data['chunk_top_k']
        if 'max_entity_tokens' in data:
            config.max_entity_tokens = data['max_entity_tokens']
        if 'max_relation_tokens' in data:
            config.max_relation_tokens = data['max_relation_tokens']
        if 'framework_config' in data:
            config.framework_config = data['framework_config']
        
        db.session.commit()
        
        # 如果启用了图谱增强，初始化服务
        if config.enabled:
            try:
                success, message = graph_service.initialize_framework(config)
                if not success:
                    return jsonify({
                        'success': False,
                        'message': f'初始化图谱增强框架失败: {message}'
                    }), 500
            except Exception as e:
                current_app.logger.error(f"初始化图谱增强框架异常: {e}")
                return jsonify({
                    'success': False,
                    'message': f'初始化图谱增强框架异常: {str(e)}'
                }), 500
        
        return jsonify({
            'success': True,
            'message': '图谱增强配置更新成功',
            'data': {
                'id': config.id,
                'enabled': config.enabled,
                'framework': config.framework
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新图谱增强配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新图谱增强配置失败: {str(e)}'
        }), 500

# ==================== 状态查询接口 ====================

@graph_enhancement_bp.route('/graph-enhancement/status', methods=['GET'])
def get_graph_enhancement_status():
    """获取图谱增强状态"""
    try:
        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': True,
                'data': {
                    'enabled': False,
                    'status': 'disabled',
                    'message': '图谱增强未启用'
                }
            })
        
        # 获取框架状态
        status_info = graph_service.get_status(config)
        
        return jsonify({
            'success': True,
            'data': status_info
        })
        
    except Exception as e:
        current_app.logger.error(f"获取图谱增强状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取图谱增强状态失败: {str(e)}'
        }), 500

# ==================== 测试查询接口 ====================

@graph_enhancement_bp.route('/graph-enhancement/test-query', methods=['POST'])
def test_graph_enhancement_query():
    """测试图谱增强查询"""
    try:
        data = request.get_json()
        
        query = data.get('query', '')
        if not query:
            return jsonify({
                'success': False,
                'message': '查询内容不能为空'
            }), 400
        
        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': False,
                'message': '图谱增强未启用'
            }), 400
        
        # 执行查询
        query_params = {
            'mode': data.get('mode', config.default_query_mode),
            'top_k': data.get('top_k', config.top_k),
            'chunk_top_k': data.get('chunk_top_k', config.chunk_top_k),
            'response_type': data.get('response_type', 'Multiple Paragraphs')
        }
        
        start_time = datetime.now()
        success, result = graph_service.query(config, query, query_params)
        end_time = datetime.now()
        
        response_time = (end_time - start_time).total_seconds()
        
        if success:
            return jsonify({
                'success': True,
                'data': {
                    'query': query,
                    'result': result,
                    'response_time': response_time,
                    'query_params': query_params,
                    'framework': config.framework
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': f'查询失败: {result}'
            }), 500
        
    except Exception as e:
        current_app.logger.error(f"测试图谱增强查询失败: {e}")
        return jsonify({
            'success': False,
            'message': f'测试图谱增强查询失败: {str(e)}'
        }), 500

# ==================== 连接测试接口 ====================

@graph_enhancement_bp.route('/graph-enhancement/test-connection', methods=['POST'])
def test_graph_enhancement_connection():
    """测试图谱增强连接"""
    try:
        data = request.get_json()
        framework = data.get('framework', 'lightrag')
        framework_config = data.get('framework_config', {})
        
        # 测试连接
        success, message = graph_service.test_connection(framework, framework_config)
        
        return jsonify({
            'success': success,
            'message': message,
            'framework': framework
        })
        
    except Exception as e:
        current_app.logger.error(f"测试图谱增强连接失败: {e}")
        return jsonify({
            'success': False,
            'message': f'测试图谱增强连接失败: {str(e)}'
        }), 500

# ==================== 数据管理接口 ====================

@graph_enhancement_bp.route('/graph-enhancement/rebuild-index', methods=['POST'])
def rebuild_graph_enhancement_index():
    """重建图谱增强索引"""
    try:
        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': False,
                'message': '图谱增强未启用'
            }), 400
        
        # 重建索引
        success, message = graph_service.rebuild_index(config)
        
        return jsonify({
            'success': success,
            'message': message
        })
        
    except Exception as e:
        current_app.logger.error(f"重建图谱增强索引失败: {e}")
        return jsonify({
            'success': False,
            'message': f'重建图谱增强索引失败: {str(e)}'
        }), 500

@graph_enhancement_bp.route('/graph-enhancement/clear-graph', methods=['POST'])
def clear_graph_enhancement_data():
    """清空图谱增强数据"""
    try:
        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': False,
                'message': '图谱增强未启用'
            }), 400

        # 清空数据
        success, message = graph_service.clear_data(config)

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        current_app.logger.error(f"清空图谱增强数据失败: {e}")
        return jsonify({
            'success': False,
            'message': f'清空图谱增强数据失败: {str(e)}'
        }), 500

@graph_enhancement_bp.route('/graph-enhancement/add-documents', methods=['POST'])
def add_documents_to_graph():
    """添加文档到图谱增强系统"""
    try:
        data = request.get_json()

        documents = data.get('documents', [])
        if not documents:
            return jsonify({
                'success': False,
                'message': '文档列表不能为空'
            }), 400

        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': False,
                'message': '图谱增强未启用'
            }), 400

        # 添加文档
        success, message = graph_service.add_documents(config, documents)

        return jsonify({
            'success': success,
            'message': message,
            'document_count': len(documents)
        })

    except Exception as e:
        current_app.logger.error(f"添加文档到图谱增强系统失败: {e}")
        return jsonify({
            'success': False,
            'message': f'添加文档失败: {str(e)}'
        }), 500
