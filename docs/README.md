# ABM-LLM 智能体对话模拟平台

一个结合大语言模型能力与传统智能体建模(ABM)优势的创新平台，专注于模拟基于对话的智能体互动。

## 系统亮点

### 大语言模型与传统智能体建模的融合
- 利用大语言模型的语义理解能力增强智能体交互的真实性和复杂性
- 保留了传统ABM的结构化建模和规则定义优势
- 创新的双引擎架构，同时支持自然语言规则和逻辑规则

### 对比传统ABM软件的创新优势

| 功能特性 | 我们的系统 | 传统ABM软件(NetLogo/Mesa/AnyLogic) |
|---------|-----------|----------------------------------|
| **规则定义** | 双引擎：支持自然语言规则和程序逻辑规则 | 仅支持编程式规则或简单条件 |
| **用户门槛** | 适合技术和非技术背景用户 | 主要面向有编程能力的用户 |
| **监督者机制** | 内置监督者角色，可自动监控和干预 | 缺乏自动化监督机制 |
| **交互重点** | 以对话和沟通为中心 | 以空间移动和状态变化为中心 |
| **控制能力** | 通过MCP插件实现对话+控制混合模式 | 主要关注状态变化，缺乏复杂控制接口 |
| **变量系统** | 模板与实例分离，更灵活 | 变量定义与实例通常混合 |
| **应用场景** | 适合模拟人类对话、决策和协作 | 适合物理系统和简单行为模拟 |

### 与智能体编排平台的比较

| 功能特性 | 我们的系统 | 现代LLM平台(Dify/Langflow/RAGFlow) |
|---------|-----------|----------------------------------|
| **多智能体协作** | 内置多角色协作框架，支持复杂互动模式 | 主要面向单一智能体或简单的多轮对话 |
| **规则系统** | 双引擎混合规则系统，语义规则与逻辑规则结合 | 主要依赖流程图式编排或简单的逻辑链 |
| **环境变量** | 完整的环境变量架构，支持公共变量与角色变量 | 有限的变量管理，缺乏完整模拟环境 |
| **交互模拟** | 专注模拟真实人类交互和复杂协作模式 | 关注单次查询响应或固定流程 |
| **行动空间概念** | 基于ABM的行动空间模型，可精确控制智能体交互 | 缺乏结构化的交互环境设计 |
| **监督者机制** | 内置监督者角色，能监控和干预模拟过程 | 通常无专门的监控角色或机制 |
| **应用重点** | 复杂多方交互场景，如专家会议、团队协作 | 知识检索、简单服务对话、工作流自动化 |
| **专业性** | 兼具通用性和专业领域深度 | 通用性强但专业领域深度有限 |

### 核心创新功能

#### 1. 双引擎规则系统
- 自然语言规则引擎：处理复杂语义和模糊条件
- 逻辑规则引擎：处理精确计算和确定性逻辑
- 两种规则无缝协作，取长补短

#### 2. 行动空间监督者角色
- 自动监控智能体行为和规则执行
- 根据预设条件进行干预和调整
- 提供模拟过程中的动态反馈

#### 3. 环境变量模板-实例架构
- 定义阶段：创建变量模板和结构
- 实例化阶段：变量在行动空间中具体化
- 支持公共环境变量和角色专属变量

#### 4. 面向对话的交互设计
- 智能体之间的自然语言对话
- 多种对话模式：顺序、小组、辩论、协作
- 基于对话历史的决策和学习

#### 5. MCP插件系统
- 不仅限于对话，还支持智能体执行控制和行动
- 角色可配置MCP（Model-Control-Protocol）插件
- 智能体能与外部系统和工具交互
- 支持调用API、访问数据库、控制设备等实际行动
- 使模拟场景从纯对话扩展到真实行动领域

## 适用场景

本系统特别适合模拟涉及多方沟通、协商和决策的复杂场景：

- **企业决策与管理**：战略制定、团队协作、项目管理
- **医疗会诊**：多专家诊断讨论和治疗方案制定
- **教育培训**：模拟课堂讨论、案例分析、辩论训练
- **政策分析**：模拟政策影响、公共讨论和多方协商
- **研发创新**：产品开发讨论、创意碰撞、工艺改进
- **智能系统控制**：通过MCP插件控制智能家居、工业设备或软件系统
- **自动化工作流**：智能体基于对话结果执行实际操作，如数据分析、报告生成
- **虚实结合场景**：智能体不仅讨论决策，还能实际执行和验证解决方案

## 系统架构

平台基于现代Web技术栈构建：

- **后端**：Python + Flask，负责智能体逻辑、规则引擎和行动空间管理
- **前端**：React，提供直观的用户界面和可视化
- **数据存储**：关系型数据库 + JSON结构，灵活存储各类数据
- **AI接口**：接入多种大语言模型API，支持不同能力和成本的选择

## 开发状态

本项目目前处于活跃开发阶段，欢迎贡献和建议。

## 项目结构

- `app/`: 后端服务，使用 Flask 实现
- `frontend/`: 前端应用，使用 React 实现

## 功能特点

### 多智能体行动

系统支持多种行动模式，以专家会议为例，包括如下规则集：
- 顺序模式 (Sequential)：智能体按顺序发言
- 小组模式 (Panel)：智能体组成专家小组共同讨论
- 辩论模式 (Debate)：智能体分成正反两方进行辩论
- 协作模式 (Collaborative)：智能体共同协作解决问题

### 行动空间设定与规则集

系统采用基于ABM (Agent-Based Modeling) 的ODD框架定义行动空间设定和规则集：

#### 行动空间设定组成

每个行动空间设定包含以下要素：
- 基本信息：名称、描述
- 规则说明：参与者需遵循的基本规则
- ODD框架：完整的ABM模型定义
- 多种规则集：同一行动空间下不同的交互规则选项

#### 规则集示例 - 学术研讨会

学术研讨会行动空间设定中包含多种规则集，用户可在创建会话时选择：

1. **顺序发言模式**
   - 每位专家按顺序发表观点
   - 主持人控制发言轮次
   - 不允许打断他人发言
   - 每人有固定发言时间限制

2. **共同讨论模式**
   - 专家自由发言
   - 允许在他人观点基础上拓展
   - 鼓励提问和交流
   - 主持人引导讨论方向

3. **结构化辩论模式**
   - 专家分为正反两方
   - 按照开篇陈述、反驳、交叉质询、总结等阶段进行
   - 遵循严格的发言顺序和时间限制
   - 主持人担任裁判角色

4. **头脑风暴模式**
   - 鼓励快速生成创意和解决方案
   - 延迟批判，先收集想法
   - 允许自由发言和思维发散
   - 主持人负责收集和整理想法

规则集定义了智能体的行为模式、交互规则和会话流程，使得同一行动空间设定下可以有多种不同的对话体验。

## 运行项目

### 后端服务

1. 进入项目根目录

2. 激活conda环境：
   ```bash
   source /opt/homebrew/Caskroom/miniconda/base/etc/profile.d/conda.sh && conda activate abm
   ```

3. 运行应用：
   ```bash
   python run_app.py
   ```

后端服务将在 http://localhost:8080 运行。

### 前端应用

1. 进入前端目录：
   ```bash
   cd frontend
   ```

2. 安装依赖：
   ```bash
   pnpm install
   ```

3. 启动开发服务器：
   ```bash
   npm start
   ```

前端应用将在 http://localhost:3000 运行。

## 技术栈

- 后端：
  - Flask
  - SQLAlchemy
  - SQLite
  - Python

- 前端：
  - React
  - Ant Design
  - React Router
  - Axios
