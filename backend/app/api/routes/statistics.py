"""
系统统计API路由

提供系统概览和各种统计数据的API接口
"""
from flask import Blueprint, jsonify
from app.services.statistics_service import StatisticsService
import logging

logger = logging.getLogger(__name__)

# 创建Blueprint
statistics_bp = Blueprint('statistics_api', __name__)

@statistics_bp.route('/statistics/overview', methods=['GET'])
def get_system_overview():
    """
    获取系统概览统计数据

    Returns:
        JSON: 包含系统各项统计数据的响应
    """
    try:
        statistics = StatisticsService.get_system_overview()
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        logger.error(f"获取系统概览统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取系统统计数据失败',
            'message': str(e)
        }), 500

@statistics_bp.route('/statistics/tasks', methods=['GET'])
def get_task_statistics():
    """
    获取任务相关的详细统计数据

    Returns:
        JSON: 包含任务统计数据的响应
    """
    try:
        statistics = StatisticsService.get_task_statistics()
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        logger.error(f"获取任务统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取任务统计数据失败',
            'message': str(e)
        }), 500

@statistics_bp.route('/statistics/roles', methods=['GET'])
def get_role_statistics():
    """
    获取角色相关的详细统计数据

    Returns:
        JSON: 包含角色统计数据的响应
    """
    try:
        statistics = StatisticsService.get_role_statistics()
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        logger.error(f"获取角色统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取角色统计数据失败',
            'message': str(e)
        }), 500

@statistics_bp.route('/statistics/action-spaces', methods=['GET'])
def get_action_space_statistics():
    """
    获取行动空间相关的详细统计数据

    Returns:
        JSON: 包含行动空间统计数据的响应
    """
    try:
        statistics = StatisticsService.get_action_space_statistics()
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        logger.error(f"获取行动空间统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取行动空间统计数据失败',
            'message': str(e)
        }), 500

@statistics_bp.route('/statistics/activity-trends', methods=['GET'])
def get_activity_trends():
    """
    获取活动趋势统计数据

    Returns:
        JSON: 包含活动趋势统计数据的响应
    """
    try:
        statistics = StatisticsService.get_activity_trends()
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        logger.error(f"获取活动趋势统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取活动趋势统计数据失败',
            'message': str(e)
        }), 500

@statistics_bp.route('/statistics/interactions', methods=['GET'])
def get_interaction_statistics():
    """
    获取交互活动统计数据

    Returns:
        JSON: 包含交互统计数据的响应
    """
    try:
        statistics = StatisticsService.get_interaction_statistics()
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        logger.error(f"获取交互统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取交互统计数据失败',
            'message': str(e)
        }), 500

@statistics_bp.route('/statistics/ecosystem', methods=['GET'])
def get_ecosystem_statistics():
    """
    获取智能体生态统计数据

    Returns:
        JSON: 包含生态统计数据的响应
    """
    try:
        statistics = StatisticsService.get_ecosystem_statistics()
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        logger.error(f"获取生态统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取生态统计数据失败',
            'message': str(e)
        }), 500

@statistics_bp.route('/statistics/resources', methods=['GET'])
def get_system_resources():
    """
    获取系统资源统计数据

    Returns:
        JSON: 包含系统资源统计数据的响应
    """
    try:
        statistics = StatisticsService.get_system_resources()
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        logger.error(f"获取系统资源统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取系统资源统计数据失败',
            'message': str(e)
        }), 500

@statistics_bp.route('/statistics/users', methods=['GET'])
def get_user_statistics():
    """
    获取用户活动统计数据

    Returns:
        JSON: 包含用户统计数据的响应
    """
    try:
        statistics = StatisticsService.get_user_statistics()
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        logger.error(f"获取用户统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取用户统计数据失败',
            'message': str(e)
        }), 500

@statistics_bp.route('/statistics/autonomous-tasks', methods=['GET'])
def get_autonomous_task_statistics():
    """
    获取自主行动任务统计数据

    Returns:
        JSON: 包含自主行动任务统计数据的响应
    """
    try:
        statistics = StatisticsService.get_autonomous_task_statistics()
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        logger.error(f"获取自主行动任务统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取自主行动任务统计数据失败',
            'message': str(e)
        }), 500

@statistics_bp.route('/statistics/dashboard', methods=['GET'])
def get_dashboard_data():
    """
    获取仪表盘所需的所有统计数据

    Returns:
        JSON: 包含仪表盘所有统计数据的响应
    """
    try:
        # 获取系统概览数据
        overview = StatisticsService.get_system_overview()

        # 获取任务统计数据
        task_stats = StatisticsService.get_task_statistics()

        # 获取角色统计数据
        role_stats = StatisticsService.get_role_statistics()

        # 获取行动空间统计数据
        space_stats = StatisticsService.get_action_space_statistics()

        # 获取新增的统计数据
        activity_trends = StatisticsService.get_activity_trends()
        interactions = StatisticsService.get_interaction_statistics()
        ecosystem = StatisticsService.get_ecosystem_statistics()
        resources = StatisticsService.get_system_resources()
        users = StatisticsService.get_user_statistics()
        autonomous_tasks = StatisticsService.get_autonomous_task_statistics()

        # 合并所有数据
        dashboard_data = {
            'overview': overview,
            'tasks': task_stats,
            'roles': role_stats,
            'action_spaces': space_stats,
            'activity_trends': activity_trends,
            'interactions': interactions,
            'ecosystem': ecosystem,
            'resources': resources,
            'users': users,
            'autonomous_tasks': autonomous_tasks
        }

        return jsonify({
            'success': True,
            'data': dashboard_data
        })
    except Exception as e:
        logger.error(f"获取仪表盘数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取仪表盘数据失败',
            'message': str(e)
        }), 500
