#!/usr/bin/env python3
"""
数据库迁移脚本：统一知识库类型
将所有知识库的type字段统一设置为'knowledge'，移除技术分类概念
"""

import os
import sys
import sqlite3
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_knowledge_types():
    """执行知识库类型迁移"""
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.db')
    
    if not os.path.exists(db_path):
        logger.warning(f"数据库文件不存在: {db_path}")
        logger.info("这是正常的，如果这是首次运行，数据库将在应用启动时创建")
        return True
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查knowledges表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='knowledges'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            logger.info("knowledges表不存在，跳过迁移")
            conn.close()
            return True
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(knowledges)")
        columns = cursor.fetchall()
        
        logger.info("当前knowledges表结构:")
        column_names = []
        for column in columns:
            column_names.append(column[1])
            logger.info(f"  {column[1]} {column[2]} (nullable: {not column[3]}, default: {column[4]})")
        
        if 'type' not in column_names:
            logger.info("type字段不存在，跳过迁移")
            conn.close()
            return True
        
        # 查询现有知识库数据
        cursor.execute("SELECT id, name, type FROM knowledges")
        records = cursor.fetchall()
        
        if not records:
            logger.info("knowledges表为空，无需迁移")
            conn.close()
            return True
        
        logger.info(f"找到 {len(records)} 个知识库记录")
        
        # 显示迁移前的状态
        logger.info("迁移前的知识库类型分布:")
        type_counts = {}
        for record in records:
            record_id, name, type_value = record
            type_counts[type_value] = type_counts.get(type_value, 0) + 1
            logger.info(f"  ID: {record_id}, 名称: {name}, 类型: {type_value}")
        
        logger.info("类型统计:")
        for type_name, count in type_counts.items():
            logger.info(f"  {type_name}: {count} 个")
        
        # 执行迁移：将所有知识库的type设置为'knowledge'
        logger.info("开始迁移：将所有知识库类型统一设置为'knowledge'...")
        
        cursor.execute("UPDATE knowledges SET type = 'knowledge'")
        updated_count = cursor.rowcount
        
        # 提交更改
        conn.commit()
        
        logger.info(f"成功更新 {updated_count} 个知识库记录")
        
        # 验证迁移结果
        logger.info("验证迁移结果...")
        cursor.execute("SELECT id, name, type FROM knowledges")
        updated_records = cursor.fetchall()
        
        logger.info("迁移后的知识库状态:")
        for record in updated_records:
            record_id, name, type_value = record
            logger.info(f"  ID: {record_id}, 名称: {name}, 类型: {type_value}")
        
        # 检查是否所有记录都已更新
        cursor.execute("SELECT COUNT(*) FROM knowledges WHERE type != 'knowledge'")
        remaining_count = cursor.fetchone()[0]
        
        if remaining_count == 0:
            logger.info("✅ 所有知识库类型已成功统一为'knowledge'")
        else:
            logger.warning(f"⚠️  仍有 {remaining_count} 个知识库的类型未更新")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def main():
    """主函数"""
    logger.info("开始数据库迁移：统一知识库类型")
    logger.info("=" * 50)
    
    success = migrate_knowledge_types()
    
    logger.info("=" * 50)
    if success:
        logger.info("✅ 迁移完成！")
        logger.info("所有知识库的类型已统一设置为'knowledge'")
        logger.info("用户界面将不再显示技术分类（文本/向量/结构化）")
        sys.exit(0)
    else:
        logger.error("❌ 迁移失败！")
        sys.exit(1)

if __name__ == '__main__':
    main()
