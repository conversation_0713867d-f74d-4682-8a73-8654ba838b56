from setuptools import setup, find_packages

setup(
    name="abm-llm-conversation",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "flask==3.0.2",
        "python-dotenv==1.0.1",
        "requests==2.31.0",
        "configparser==6.0.1",
        "flask-cors==4.0.0",
        "gunicorn==21.2.0",
        "python-jose==3.3.0",
    ],
    extras_require={
        "test": [
            "pytest==8.0.0",
            "pytest-cov==4.1.0",
        ],
    },
) 