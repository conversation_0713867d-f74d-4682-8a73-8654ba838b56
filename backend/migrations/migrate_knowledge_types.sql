-- 数据库迁移脚本：统一知识库类型
-- 将所有知识库的type字段统一设置为'knowledge'

-- 开始事务
BEGIN TRANSACTION;

-- 显示迁移前的状态
.print "=== 知识库类型迁移开始 ==="
.print "迁移前的知识库状态:"
SELECT 
    id,
    name,
    type,
    description
FROM knowledges 
ORDER BY id;

.print ""
.print "迁移前的类型分布:"
SELECT 
    type,
    COUNT(*) as count
FROM knowledges 
GROUP BY type;

-- 执行迁移：将所有知识库的type设置为'knowledge'
.print ""
.print "执行迁移：将所有知识库类型统一设置为'knowledge'..."

UPDATE knowledges 
SET type = 'knowledge'
WHERE type != 'knowledge' OR type IS NULL;

-- 显示迁移后的状态
.print ""
.print "迁移后的知识库状态:"
SELECT 
    id,
    name,
    type,
    description
FROM knowledges 
ORDER BY id;

.print ""
.print "迁移后的类型分布:"
SELECT 
    type,
    COUNT(*) as count
FROM knowledges 
GROUP BY type;

-- 验证迁移结果
.print ""
.print "验证迁移结果:"
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 所有知识库类型已成功统一为knowledge'
        ELSE '⚠️  仍有 ' || COUNT(*) || ' 个知识库的类型未更新'
    END as result
FROM knowledges 
WHERE type != 'knowledge';

-- 提交事务
COMMIT;

.print ""
.print "=== 知识库类型迁移完成 ==="
