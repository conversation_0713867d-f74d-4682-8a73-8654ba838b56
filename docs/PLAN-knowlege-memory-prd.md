# 知识库与分区记忆功能 PRD

## 1. 概述

本文档描述知识库管理和分区记忆功能的产品需求，包括数据库模型设计、后端API和前端界面交互。

### 1.1 知识库管理功能概述

知识库功能允许用户创建、编辑和管理供角色使用的知识库。知识库可以一对多地绑定到角色，使角色能够利用知识库中的内容增强其功能。系统支持本地知识库创建以及导入第三方知识库，满足不同场景下的知识管理需求。

### 1.2 分区记忆功能概述

分区记忆功能允许用户创建和管理角色的记忆分区，这些分区在创建任务时会被实例化并与智能体绑定。实例化的记忆可以被编辑并转换为模板，供其他智能体使用。

## 2. 数据库设计

### 2.1 知识库相关数据模型

已有的数据库表：
- `Knowledge` - 存储知识库基本信息
- `RoleKnowledge` - 存储角色与知识库的多对多关系

需要扩展的字段：
- `Knowledge` 表添加 `is_template` 字段，标识是否为模板
- `Knowledge` 表添加 `source_id` 字段，用于记录模板来源
- `Knowledge` 表添加 `source_type` 字段，用于记录知识库来源类型（本地创建、第三方导入等）
- `Knowledge` 表添加 `external_id` 字段，用于存储第三方知识库的唯一标识符
- `Knowledge` 表添加 `external_config` 字段，用于存储第三方知识库连接配置（JSON格式）

### 2.2 分区记忆相关数据模型

已有的数据库表：
- `Memory` - 存储记忆分区基本信息
- `RoleMemory` - 存储角色与记忆分区的多对多关系

需要新增的表：
1. `MemoryEntry` - 存储记忆条目
   - `id` - 主键
   - `memory_id` - 外键，关联到Memory表
   - `agent_id` - 外键，关联到Agent表(可为空)
   - `action_task_id` - 外键，关联到ActionTask表(可为空)
   - `action_space_id` - 外键，关联到ActionSpace表(可为空)
   - `content` - 记忆内容
   - `is_template` - 是否为模板
   - `template_id` - 模板ID(可为空)
   - `created_at` - 创建时间
   - `updated_at` - 更新时间

2. `MemoryPartition` - 存储记忆分区定义
   - `id` - 主键
   - `name` - 分区名称
   - `description` - 分区描述
   - `type` - 分区类型(如短期、长期、情感等)
   - `capacity` - 容量限制
   - `role_id` - 角色ID(可为空)
   - `is_template` - 是否为模板
   - `created_at` - 创建时间
   - `updated_at` - 更新时间

## 3. 后端API设计

### 3.1 知识库API

#### 3.1.1 获取知识库列表
- 路径: `GET /api/knowledges`
- 参数: 
  - `page`: 页码
  - `limit`: 每页数量
  - `type`: 可选，知识库类型过滤
  - `source_type`: 可选，知识库来源类型过滤
- 响应: 知识库列表

#### 3.1.2 获取知识库详情
- 路径: `GET /api/knowledges/{id}`
- 响应: 知识库详细信息

#### 3.1.3 创建知识库
- 路径: `POST /api/knowledges`
- 请求体: 知识库信息(name, description, type, content等)
- 响应: 创建的知识库

#### 3.1.4 更新知识库
- 路径: `PUT /api/knowledges/{id}`
- 请求体: 更新的知识库信息
- 响应: 更新后的知识库

#### 3.1.5 删除知识库
- 路径: `DELETE /api/knowledges/{id}`
- 响应: 成功消息

#### 3.1.6 为角色绑定知识库
- 路径: `POST /api/roles/{roleId}/knowledges/{knowledgeId}`
- 响应: 绑定关系

#### 3.1.7 为角色解除知识库
- 路径: `DELETE /api/roles/{roleId}/knowledges/{knowledgeId}`
- 响应: 成功消息

#### 3.1.8 获取角色绑定的知识库
- 路径: `GET /api/roles/{roleId}/knowledges`
- 响应: 知识库列表

#### 3.1.9 将知识库转为模板
- 路径: `POST /api/knowledges/{id}/template`
- 响应: 创建的模板

#### 3.1.10 获取支持的第三方知识库类型
- 路径: `GET /api/knowledges/external/types`
- 响应: 支持的第三方知识库类型列表和连接配置说明

#### 3.1.11 导入第三方知识库
- 路径: `POST /api/knowledges/import`
- 请求体: 
  - `name`: 导入后的知识库名称
  - `description`: 知识库描述
  - `source_type`: 第三方知识库类型（如"dify", "langchain", "ragflow"等）
  - `connection_config`: 连接配置，根据不同类型包含不同字段
    - 例如API密钥、服务器URL、知识库ID等
  - `sync_mode`: 同步模式（"copy"或"link"）
    - copy模式会复制内容到本地
    - link模式保持与外部知识库的连接
- 响应: 导入的知识库信息

#### 3.1.12 同步第三方知识库
- 路径: `POST /api/knowledges/{id}/sync`
- 请求体: 
  - `sync_all`: 是否全量同步
  - `sync_items`: 可选，部分同步项ID列表
- 响应: 同步状态及结果

#### 3.1.13 获取第三方知识库同步状态
- 路径: `GET /api/knowledges/{id}/sync/status`
- 响应: 
  - `status`: 同步状态（"syncing", "completed", "failed"）
  - `progress`: 同步进度（0-100）
  - `last_sync_time`: 上次同步时间
  - `items_count`: 已同步项数量
  - `error_message`: 失败时的错误信息

### 3.2 分区记忆API

#### 3.2.1 获取记忆分区列表
- 路径: `GET /api/memories`
- 参数:
  - `page`: 页码
  - `limit`: 每页数量
  - `type`: 可选，分区类型过滤
- 响应: 记忆分区列表

#### 3.2.2 获取记忆分区详情
- 路径: `GET /api/memories/{id}`
- 响应: 分区详细信息

#### 3.2.3 创建记忆分区
- 路径: `POST /api/memories`
- 请求体: 分区信息(name, description, type, capacity等)
- 响应: 创建的分区

#### 3.2.4 更新记忆分区
- 路径: `PUT /api/memories/{id}`
- 请求体: 更新的分区信息
- 响应: 更新后的分区

#### 3.2.5 删除记忆分区
- 路径: `DELETE /api/memories/{id}`
- 响应: 成功消息

#### 3.2.6 获取分区中的记忆条目
- 路径: `GET /api/memories/{id}/entries`
- 参数:
  - `page`: 页码
  - `limit`: 每页数量
  - `agent_id`: 可选，智能体ID过滤
- 响应: 记忆条目列表

#### 3.2.7 创建记忆条目
- 路径: `POST /api/memories/{id}/entries`
- 请求体: 记忆条目信息(content, agent_id等)
- 响应: 创建的记忆条目

#### 3.2.8 更新记忆条目
- 路径: `PUT /api/memories/entries/{id}`
- 请求体: 更新的记忆条目信息
- 响应: 更新后的记忆条目

#### 3.2.9 删除记忆条目
- 路径: `DELETE /api/memories/entries/{id}`
- 响应: 成功消息

#### 3.2.10 将记忆条目转为模板
- 路径: `POST /api/memories/entries/{id}/template`
- 响应: 创建的模板

#### 3.2.11 获取角色绑定的记忆分区
- 路径: `GET /api/roles/{roleId}/memories`
- 响应: 记忆分区列表

#### 3.2.12 为角色绑定记忆分区
- 路径: `POST /api/roles/{roleId}/memories/{memoryId}`
- 响应: 绑定关系

#### 3.2.13 为角色解除记忆分区
- 路径: `DELETE /api/roles/{roleId}/memories/{memoryId}`
- 响应: 成功消息

#### 3.2.14 智能体实例化记忆
- 路径: `POST /api/agents/{agentId}/memories/instantiate`
- 请求体: 记忆分区ID列表
- 响应: 实例化的记忆列表

## 4. 前端界面设计

### 4.1 知识库管理页面

#### 4.1.1 知识库列表视图
- 展示所有知识库，包括名称、描述、类型和创建时间
- 提供创建、编辑和删除知识库的按钮
- 支持按类型过滤和搜索知识库
- 提供将知识库绑定到角色的功能
- 新增"导入外部知识库"按钮，点击打开导入流程

#### 4.1.2 知识库详情视图
- 展示知识库的详细信息
- 允许编辑知识库内容
- 显示已绑定此知识库的角色列表
- 提供将知识库转为模板的选项
- 对于外部导入的知识库，显示来源信息和连接状态
- 对于外部导入的知识库，提供"同步"按钮和同步历史记录

#### 4.1.3 角色关联视图
- 展示与知识库关联的角色
- 允许添加或移除角色关联
- 显示角色使用知识库的情况统计

#### 4.1.4 外部知识库导入视图
- 提供支持的第三方知识库类型列表
- 根据所选类型显示所需的连接配置表单
- 提供连接测试功能，验证连接配置的正确性
- 显示导入选项，如同步模式（复制/链接）
- 提供导入预览，显示将导入的内容概览
- 导入进度显示和状态提示

#### 4.1.5 外部知识库同步管理视图
- 显示已导入的外部知识库同步状态
- 提供手动同步触发功能
- 显示同步历史记录和变更详情
- 支持配置自动同步计划
- 提供同步错误诊断和修复建议

### 4.2 分区记忆管理页面

#### 4.2.1 记忆分区列表视图
- 展示所有记忆分区，包括名称、类型、容量和已使用数量
- 提供创建、编辑和删除分区的按钮
- 支持按类型过滤和搜索分区

#### 4.2.2 分区详情视图
- 展示分区的详细信息
- 显示分区中的记忆条目列表
- 提供添加、编辑和删除记忆条目的功能
- 允许将记忆条目转为模板

#### 4.2.3 实例化记忆视图
- 展示已实例化的记忆，按智能体、任务或行动空间分组
- 提供编辑实例化记忆的功能
- 允许将实例化记忆转为模板

#### 4.2.4 角色记忆关联视图
- 展示与记忆分区关联的角色
- 允许添加或移除角色关联
- 显示角色使用记忆分区的情况统计

## 5. 实施计划

### 5.1 数据库更新
1. 扩展Knowledge表，添加is_template和source_id字段
2. 扩展Knowledge表，添加source_type、external_id和external_config字段
3. 创建MemoryEntry表
4. 创建MemoryPartition表
5. 更新已有的Memory和RoleMemory表的关联

### 5.2 后端开发
1. 实现知识库相关API
2. 实现分区记忆相关API
3. 完善角色与知识库、记忆分区的关联API
4. 添加智能体实例化记忆的功能
5. 开发第三方知识库连接器和适配层
6. 实现第三方知识库导入和同步功能

### 5.3 前端开发
1. 完善知识库管理页面
2. 实现分区记忆管理页面
3. 添加角色关联视图
4. 实现实例化记忆视图
5. 开发外部知识库导入和同步管理界面

### 5.4 测试与部署
1. 单元测试各API功能
2. 集成测试前后端交互
3. 用户界面测试
4. 第三方知识库连接测试
5. 部署更新并监控系统性能

## 6. 扩展考虑

### 6.1 知识库扩展
- 支持更多知识库类型，如图像、音频等
- 添加知识库版本控制
- 实现知识库内容的自动提取和索引
- 支持更多第三方知识库平台的集成（如Dify、FastGPT、LangChain、RAGFlow等）
- 支持批量导入和管理多个外部知识库
- 提供知识库内容的差异比较和冲突解决机制
- 开发知识库内容转换适配器，支持不同格式之间的转换
- 实现知识库内容的权限和访问控制

### 6.2 分区记忆扩展
- 添加更多记忆分区类型，如情景记忆、过程记忆等
- 实现记忆衰减机制，模拟人类记忆特性
- 添加记忆优先级和标签系统 