"""
外部知识库API路由

处理与外部知识库相关的所有API请求，包括：
- 提供商管理
- 外部知识库管理  
- 角色关联管理
- 查询功能
"""

from flask import Blueprint, request, jsonify
from app.models import (
    ExternalKnowledgeProvider, ExternalKnowledge, RoleExternalKnowledge,
    ExternalKnowledgeQueryLog, Role, db
)
from app.services.external_knowledge import AdapterFactory, ExternalKnowledgeService
from sqlalchemy.exc import IntegrityError
from flask import current_app
import time
import requests
from datetime import datetime

# 创建Blueprint
external_knowledge_bp = Blueprint('external_knowledge_api', __name__)

# ==================== 提供商管理接口 ====================

@external_knowledge_bp.route('/external-kb/providers', methods=['GET'])
def get_providers():
    """获取所有外部知识库提供商"""
    try:
        providers = ExternalKnowledgeProvider.query.filter_by(status='active').all()
        
        result = []
        for provider in providers:
            # 统计该提供商下的知识库数量
            kb_count = ExternalKnowledge.query.filter_by(
                provider_id=provider.id, 
                status='active'
            ).count()
            
            result.append({
                'id': provider.id,
                'name': provider.name,
                'type': provider.type,
                'base_url': provider.base_url,
                'status': provider.status,
                'knowledge_count': kb_count,
                'created_at': provider.created_at.isoformat() if provider.created_at else None,
                'updated_at': provider.updated_at.isoformat() if provider.updated_at else None
            })
        
        return jsonify({
            'success': True,
            'data': result,
            'total': len(result)
        })
        
    except Exception as e:
        current_app.logger.error(f"获取提供商列表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取提供商列表失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/providers', methods=['POST'])
def create_provider():
    """创建外部知识库提供商"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'type', 'base_url', 'api_key']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查名称是否重复
        existing = ExternalKnowledgeProvider.query.filter_by(name=data['name']).first()
        if existing:
            return jsonify({
                'success': False,
                'message': '提供商名称已存在'
            }), 400
        
        # 创建提供商
        provider = ExternalKnowledgeProvider(
            name=data['name'],
            type=data['type'],
            base_url=data['base_url'].rstrip('/'),
            api_key=data['api_key'],
            config=data.get('config', {}),
            status='active'
        )
        
        db.session.add(provider)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '提供商创建成功',
            'data': {
                'id': provider.id,
                'name': provider.name,
                'type': provider.type,
                'base_url': provider.base_url,
                'status': provider.status,
                'created_at': provider.created_at.isoformat()
            }
        })
        
    except IntegrityError:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '提供商名称已存在'
        }), 400
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建提供商失败: {e}")
        return jsonify({
            'success': False,
            'message': f'创建提供商失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/providers/<int:provider_id>', methods=['GET'])
def get_provider_detail(provider_id):
    """获取单个外部知识库提供商详情"""
    try:
        provider = ExternalKnowledgeProvider.query.get_or_404(provider_id)

        # 统计该提供商下的知识库数量
        kb_count = ExternalKnowledge.query.filter_by(
            provider_id=provider.id,
            status='active'
        ).count()

        return jsonify({
            'success': True,
            'data': {
                'id': provider.id,
                'name': provider.name,
                'type': provider.type,
                'base_url': provider.base_url,
                'api_key': provider.api_key,  # 详情接口包含API Key
                'config': provider.config,
                'status': provider.status,
                'knowledge_count': kb_count,
                'created_at': provider.created_at.isoformat() if provider.created_at else None,
                'updated_at': provider.updated_at.isoformat() if provider.updated_at else None
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取提供商详情失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取提供商详情失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/providers/<int:provider_id>', methods=['PUT'])
def update_provider(provider_id):
    """更新外部知识库提供商"""
    try:
        provider = ExternalKnowledgeProvider.query.get_or_404(provider_id)
        data = request.get_json()
        
        # 更新字段
        if 'name' in data:
            # 检查名称是否重复（排除自己）
            existing = ExternalKnowledgeProvider.query.filter(
                ExternalKnowledgeProvider.name == data['name'],
                ExternalKnowledgeProvider.id != provider_id
            ).first()
            if existing:
                return jsonify({
                    'success': False,
                    'message': '提供商名称已存在'
                }), 400
            provider.name = data['name']
        
        if 'type' in data:
            provider.type = data['type']
        if 'base_url' in data:
            provider.base_url = data['base_url'].rstrip('/')
        if 'api_key' in data:
            provider.api_key = data['api_key']
        if 'config' in data:
            provider.config = data['config']
        if 'status' in data:
            provider.status = data['status']
        
        provider.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '提供商更新成功',
            'data': {
                'id': provider.id,
                'name': provider.name,
                'type': provider.type,
                'base_url': provider.base_url,
                'status': provider.status,
                'updated_at': provider.updated_at.isoformat()
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新提供商失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新提供商失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/providers/<int:provider_id>', methods=['DELETE'])
def delete_provider(provider_id):
    """删除外部知识库提供商"""
    try:
        provider = ExternalKnowledgeProvider.query.get_or_404(provider_id)
        
        # 检查是否有关联的知识库
        kb_count = ExternalKnowledge.query.filter_by(provider_id=provider_id).count()
        if kb_count > 0:
            return jsonify({
                'success': False,
                'message': f'无法删除提供商，还有 {kb_count} 个关联的知识库'
            }), 400
        
        db.session.delete(provider)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '提供商删除成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除提供商失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除提供商失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/providers/<int:provider_id>/test', methods=['POST'])
def test_provider_connection(provider_id):
    """测试提供商连接"""
    try:
        provider = ExternalKnowledgeProvider.query.get_or_404(provider_id)

        # 使用适配器工厂测试连接
        provider_config = {
            'base_url': provider.base_url,
            'api_key': provider.api_key,
            'config': provider.config or {}
        }

        result = AdapterFactory.test_adapter(provider.type, provider_config)

        return jsonify({
            'success': result['success'],
            'message': result['message'],
            'data': {
                'response_time': result['response_time'],
                'status': 'connected' if result['success'] else 'failed'
            }
        })

    except Exception as e:
        current_app.logger.error(f"测试提供商连接失败: {e}")
        return jsonify({
            'success': False,
            'message': f'测试连接失败: {str(e)}'
        }), 500

# ==================== 外部知识库管理接口 ====================

@external_knowledge_bp.route('/external-kb/knowledges', methods=['GET'])
def get_external_knowledges():
    """获取所有外部知识库"""
    try:
        # 支持按提供商筛选
        provider_id = request.args.get('provider_id', type=int)
        
        query = db.session.query(ExternalKnowledge, ExternalKnowledgeProvider).join(
            ExternalKnowledgeProvider, 
            ExternalKnowledge.provider_id == ExternalKnowledgeProvider.id
        ).filter(ExternalKnowledge.status == 'active')
        
        if provider_id:
            query = query.filter(ExternalKnowledge.provider_id == provider_id)
        
        results = query.all()
        
        data = []
        for knowledge, provider in results:
            # 统计角色关联数量
            role_count = RoleExternalKnowledge.query.filter_by(
                external_knowledge_id=knowledge.id
            ).count()
            
            data.append({
                'id': knowledge.id,
                'name': knowledge.name,
                'description': knowledge.description,
                'external_kb_id': knowledge.external_kb_id,
                'query_config': knowledge.query_config,
                'status': knowledge.status,
                'role_count': role_count,
                'provider': {
                    'id': provider.id,
                    'name': provider.name,
                    'type': provider.type
                },
                'created_at': knowledge.created_at.isoformat() if knowledge.created_at else None,
                'updated_at': knowledge.updated_at.isoformat() if knowledge.updated_at else None
            })
        
        return jsonify({
            'success': True,
            'data': data,
            'total': len(data)
        })

    except Exception as e:
        current_app.logger.error(f"获取外部知识库列表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取外部知识库列表失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/knowledges', methods=['POST'])
def create_external_knowledge():
    """创建外部知识库"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['name', 'provider_id', 'external_kb_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400

        # 验证提供商是否存在
        provider = ExternalKnowledgeProvider.query.get(data['provider_id'])
        if not provider:
            return jsonify({
                'success': False,
                'message': '指定的提供商不存在'
            }), 400

        # 检查名称是否重复
        existing = ExternalKnowledge.query.filter_by(name=data['name']).first()
        if existing:
            return jsonify({
                'success': False,
                'message': '知识库名称已存在'
            }), 400

        # 创建外部知识库
        knowledge = ExternalKnowledge(
            name=data['name'],
            description=data.get('description', ''),
            provider_id=data['provider_id'],
            external_kb_id=data['external_kb_id'],
            query_config=data.get('query_config', {
                'top_k': 5,
                'similarity_threshold': 0.7,
                'max_tokens': 4000
            }),
            status='active'
        )

        db.session.add(knowledge)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '外部知识库创建成功',
            'data': {
                'id': knowledge.id,
                'name': knowledge.name,
                'description': knowledge.description,
                'external_kb_id': knowledge.external_kb_id,
                'provider_id': knowledge.provider_id,
                'query_config': knowledge.query_config,
                'status': knowledge.status,
                'created_at': knowledge.created_at.isoformat()
            }
        })

    except IntegrityError:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '知识库名称已存在'
        }), 400
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建外部知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'创建外部知识库失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/knowledges/<int:knowledge_id>', methods=['PUT'])
def update_external_knowledge(knowledge_id):
    """更新外部知识库"""
    try:
        knowledge = ExternalKnowledge.query.get_or_404(knowledge_id)
        data = request.get_json()

        # 更新字段
        if 'name' in data:
            # 检查名称是否重复（排除自己）
            existing = ExternalKnowledge.query.filter(
                ExternalKnowledge.name == data['name'],
                ExternalKnowledge.id != knowledge_id
            ).first()
            if existing:
                return jsonify({
                    'success': False,
                    'message': '知识库名称已存在'
                }), 400
            knowledge.name = data['name']

        if 'description' in data:
            knowledge.description = data['description']
        if 'provider_id' in data:
            knowledge.provider_id = data['provider_id']
        if 'external_kb_id' in data:
            knowledge.external_kb_id = data['external_kb_id']
        if 'query_config' in data:
            knowledge.query_config = data['query_config']
        if 'status' in data:
            knowledge.status = data['status']

        knowledge.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '外部知识库更新成功',
            'data': {
                'id': knowledge.id,
                'name': knowledge.name,
                'description': knowledge.description,
                'provider_id': knowledge.provider_id,
                'external_kb_id': knowledge.external_kb_id,
                'query_config': knowledge.query_config,
                'status': knowledge.status,
                'updated_at': knowledge.updated_at.isoformat(),
                'provider': {
                    'id': knowledge.provider.id,
                    'name': knowledge.provider.name,
                    'type': knowledge.provider.type
                }
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新外部知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新外部知识库失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/knowledges/<int:knowledge_id>', methods=['DELETE'])
def delete_external_knowledge(knowledge_id):
    """删除外部知识库"""
    try:
        knowledge = ExternalKnowledge.query.get_or_404(knowledge_id)

        # 检查是否有角色关联
        role_count = RoleExternalKnowledge.query.filter_by(external_knowledge_id=knowledge_id).count()
        if role_count > 0:
            return jsonify({
                'success': False,
                'message': f'无法删除知识库，还有 {role_count} 个角色关联'
            }), 400

        db.session.delete(knowledge)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '外部知识库删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除外部知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除外部知识库失败: {str(e)}'
        }), 500

# ==================== 角色关联管理接口 ====================

@external_knowledge_bp.route('/roles/<int:role_id>/external-knowledges', methods=['GET'])
def get_role_external_knowledges(role_id):
    """获取角色绑定的外部知识库"""
    try:
        # 验证角色是否存在
        role = Role.query.get_or_404(role_id)

        # 查询角色绑定的外部知识库
        query = db.session.query(
            RoleExternalKnowledge, ExternalKnowledge, ExternalKnowledgeProvider
        ).join(
            ExternalKnowledge,
            RoleExternalKnowledge.external_knowledge_id == ExternalKnowledge.id
        ).join(
            ExternalKnowledgeProvider,
            ExternalKnowledge.provider_id == ExternalKnowledgeProvider.id
        ).filter(RoleExternalKnowledge.role_id == role_id)

        results = query.all()

        data = []
        for binding, knowledge, provider in results:
            data.append({
                'id': binding.id,
                'role_id': binding.role_id,
                'external_knowledge_id': binding.external_knowledge_id,
                'config': binding.config,
                'knowledge': {
                    'id': knowledge.id,
                    'name': knowledge.name,
                    'description': knowledge.description,
                    'external_kb_id': knowledge.external_kb_id,
                    'query_config': knowledge.query_config,
                    'status': knowledge.status
                },
                'provider': {
                    'id': provider.id,
                    'name': provider.name,
                    'type': provider.type
                },
                'created_at': binding.created_at.isoformat() if binding.created_at else None
            })

        return jsonify({
            'success': True,
            'data': data,
            'total': len(data),
            'role': {
                'id': role.id,
                'name': role.name
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取角色外部知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取角色外部知识库失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/roles/<int:role_id>/external-knowledges/<int:knowledge_id>', methods=['POST'])
def bind_role_external_knowledge(role_id, knowledge_id):
    """为角色绑定外部知识库"""
    try:
        current_app.logger.info(f"开始绑定外部知识库: role_id={role_id}, knowledge_id={knowledge_id}")

        # 验证角色和知识库是否存在
        role = Role.query.get_or_404(role_id)
        knowledge = ExternalKnowledge.query.get_or_404(knowledge_id)

        current_app.logger.info(f"角色和外部知识库验证成功: role={role.name}, knowledge={knowledge.name}")

        # 检查是否已经绑定
        existing = RoleExternalKnowledge.query.filter_by(
            role_id=role_id,
            external_knowledge_id=knowledge_id
        ).first()

        if existing:
            return jsonify({
                'success': False,
                'message': '该角色已绑定此外部知识库'
            }), 400

        # 获取配置参数
        data = request.get_json() or {}
        config = data.get('config', {})

        # 创建绑定关系
        binding = RoleExternalKnowledge(
            role_id=role_id,
            external_knowledge_id=knowledge_id,
            config=config
        )

        db.session.add(binding)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '外部知识库绑定成功',
            'data': {
                'id': binding.id,
                'role_id': binding.role_id,
                'external_knowledge_id': binding.external_knowledge_id,
                'config': binding.config,
                'created_at': binding.created_at.isoformat()
            }
        })

    except IntegrityError:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '该角色已绑定此外部知识库'
        }), 400
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"绑定角色外部知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'绑定失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/roles/<int:role_id>/external-knowledges/<int:knowledge_id>', methods=['DELETE'])
def unbind_role_external_knowledge(role_id, knowledge_id):
    """解除角色外部知识库绑定"""
    try:
        current_app.logger.info(f"开始解绑外部知识库: role_id={role_id}, knowledge_id={knowledge_id}")

        # 查找绑定关系
        binding = RoleExternalKnowledge.query.filter_by(
            role_id=role_id,
            external_knowledge_id=knowledge_id
        ).first()

        current_app.logger.info(f"查找绑定关系结果: binding={binding}")

        # 如果没找到，让我们查看这个角色的所有绑定关系
        if not binding:
            all_bindings = RoleExternalKnowledge.query.filter_by(role_id=role_id).all()
            current_app.logger.info(f"角色 {role_id} 的所有外部知识库绑定: {[(b.id, b.external_knowledge_id) for b in all_bindings]}")

            return jsonify({
                'success': False,
                'message': '绑定关系不存在'
            }), 404

        db.session.delete(binding)
        db.session.commit()

        current_app.logger.info(f"外部知识库解绑成功: role_id={role_id}, knowledge_id={knowledge_id}")

        return jsonify({
            'success': True,
            'message': '外部知识库绑定解除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"解除角色外部知识库绑定失败: {e}")
        return jsonify({
            'success': False,
            'message': f'解除绑定失败: {str(e)}'
        }), 500

# ==================== 新增适配器功能接口 ====================

@external_knowledge_bp.route('/external-kb/knowledges/<int:knowledge_id>/test', methods=['POST'])
def test_knowledge_connection(knowledge_id):
    """测试外部知识库连接"""
    try:
        result = ExternalKnowledgeService.test_knowledge_connection(knowledge_id)

        return jsonify({
            'success': result['success'],
            'message': result['message'],
            'data': {
                'response_time': result['response_time']
            }
        })

    except Exception as e:
        current_app.logger.error(f"测试知识库连接失败: {e}")
        return jsonify({
            'success': False,
            'message': f'测试连接失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/knowledges/<int:knowledge_id>/query', methods=['POST'])
def query_external_knowledge(knowledge_id):
    """查询外部知识库"""
    try:
        data = request.get_json()
        query_text = data.get('query', '')
        query_params = data.get('params', {})

        if not query_text:
            return jsonify({
                'success': False,
                'message': '查询文本不能为空'
            }), 400

        # 获取知识库信息
        knowledge = ExternalKnowledge.query.get_or_404(knowledge_id)
        provider = ExternalKnowledgeProvider.query.get_or_404(knowledge.provider_id)

        # 创建适配器
        adapter = AdapterFactory.create_adapter(
            provider.type,
            {
                'base_url': provider.base_url,
                'api_key': provider.api_key,
                'config': provider.config or {}
            }
        )

        # 执行查询
        knowledge_config = {
            'external_kb_id': knowledge.external_kb_id,
            'query_config': knowledge.query_config or {}
        }

        result = adapter.query_knowledge(knowledge_config, query_text)

        # 记录查询日志
        log_entry = ExternalKnowledgeQueryLog(
            external_knowledge_id=knowledge_id,
            role_id=None,  # 直接查询没有角色信息
            query_text=query_text,
            response_data=result if result['success'] else None,
            query_time=result.get('query_time', 0),
            status='success' if result['success'] else 'error',
            error_message=result.get('error_message') if not result['success'] else None
        )

        db.session.add(log_entry)
        db.session.commit()

        return jsonify(result)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"查询外部知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/knowledges/<int:knowledge_id>/info', methods=['GET'])
def get_external_knowledge_info(knowledge_id):
    """获取外部知识库详细信息"""
    try:
        result = ExternalKnowledgeService.get_knowledge_info(knowledge_id)

        if result['success']:
            return jsonify({
                'success': True,
                'data': result['info']
            })
        else:
            return jsonify({
                'success': False,
                'message': result['error_message']
            }), 400

    except Exception as e:
        current_app.logger.error(f"获取知识库信息失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取信息失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/provider-types', methods=['GET'])
def get_provider_types():
    """获取支持的提供商类型"""
    try:
        supported_types = AdapterFactory.get_supported_types()

        return jsonify({
            'success': True,
            'data': supported_types
        })

    except Exception as e:
        current_app.logger.error(f"获取提供商类型失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/external-kb/provider-types/<provider_type>/config', methods=['GET'])
def get_provider_default_config(provider_type):
    """获取提供商默认配置"""
    try:
        default_config = AdapterFactory.get_default_config(provider_type)

        return jsonify({
            'success': True,
            'data': default_config
        })

    except Exception as e:
        current_app.logger.error(f"获取默认配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        }), 500

@external_knowledge_bp.route('/roles/<int:role_id>/external-knowledges/query', methods=['POST'])
def query_role_external_knowledges(role_id):
    """为角色查询外部知识库"""
    try:
        data = request.get_json()
        query_text = data.get('query', '')
        query_params = data.get('params', {})

        if not query_text:
            return jsonify({
                'success': False,
                'message': '查询文本不能为空'
            }), 400

        # 验证角色是否存在
        role = Role.query.get_or_404(role_id)

        # 使用服务查询
        result = ExternalKnowledgeService.query_knowledge_for_role(
            role_id, query_text, query_params
        )

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"角色知识库查询失败: {e}")
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500
