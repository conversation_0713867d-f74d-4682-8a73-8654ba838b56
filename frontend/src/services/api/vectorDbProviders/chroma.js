import api from '../axios';

/**
 * Chroma向量数据库API服务
 * 提供与Chroma向量数据库相关的API函数
 */
const chromaAPI = {
  /**
   * 测试Chroma连接
   * @param {Object} config Chroma配置
   * @returns {Promise<Object>} 连接测试结果
   */
  testConnection: async (config) => {
    try {
      const response = await api.post('/vector-db/test-connection', {
        provider: 'chroma',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('测试Chroma连接失败:', error);
      throw error;
    }
  },

  /**
   * 验证Chroma配置
   * @param {Object} config Chroma配置
   * @returns {Promise<Object>} 验证结果
   */
  validateConfig: async (config) => {
    try {
      const response = await api.post('/vector-db/validate-config', {
        provider: 'chroma',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('验证Chroma配置失败:', error);
      throw error;
    }
  },

  /**
   * 获取Chroma集合列表
   * @param {Object} config Chroma配置
   * @returns {Promise<Object>} 集合列表
   */
  listCollections: async (config) => {
    try {
      const response = await api.post('/vector-db/list-collections', {
        provider: 'chroma',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('获取Chroma集合列表失败:', error);
      throw error;
    }
  },

  /**
   * 创建Chroma集合
   * @param {Object} config Chroma配置
   * @param {string} collectionName 集合名称
   * @param {Object} metadata 集合元数据
   * @returns {Promise<Object>} 创建结果
   */
  createCollection: async (config, collectionName, metadata = {}) => {
    try {
      const response = await api.post('/vector-db/create-collection', {
        provider: 'chroma',
        config: config,
        collection_name: collectionName,
        metadata: metadata
      });
      return response.data;
    } catch (error) {
      console.error('创建Chroma集合失败:', error);
      throw error;
    }
  },

  /**
   * 向量搜索
   * @param {Object} config Chroma配置
   * @param {string} collectionName 集合名称
   * @param {Object} searchParams 搜索参数
   * @returns {Promise<Object>} 搜索结果
   */
  vectorSearch: async (config, collectionName, searchParams) => {
    try {
      const response = await api.post('/vector-db/vector-search', {
        provider: 'chroma',
        config: config,
        collection_name: collectionName,
        search_params: searchParams
      });
      return response.data;
    } catch (error) {
      console.error('Chroma向量搜索失败:', error);
      throw error;
    }
  }
};

export default chromaAPI;
