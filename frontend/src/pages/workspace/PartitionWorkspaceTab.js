import { useState, useEffect } from 'react';
import { Card, Typography, message, Tabs, Table, Button, Space, Modal, Breadcrumb } from 'antd';
import { BookOutlined, FolderOutlined, EyeOutlined, DeleteOutlined, HomeOutlined } from '@ant-design/icons';
import TaskSelector from './components/TaskSelector';
import WorkspaceFileViewer from './components/WorkspaceFileViewer';
import WorkspaceTemplateTab from './WorkspaceTemplateTab';
import { workspaceAPI } from '../../services/api/workspace';
import { actionTaskAPI } from '../../services/api/actionTask';
import { getFileIcon, processFileData } from '../../utils/workspaceUtils';

const { Text, Title } = Typography;

/**
 * 工作空间浏览器标签页组件
 * 简化的文件浏览器界面
 */
const PartitionWorkspaceTab = () => {
  const [selectedTask, setSelectedTask] = useState(null);
  const [workspaceFiles, setWorkspaceFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [isViewerVisible, setIsViewerVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('workspace');
  const [currentPath, setCurrentPath] = useState(''); // 当前浏览的路径
  const [breadcrumbs, setBreadcrumbs] = useState([]); // 面包屑导航
  const [agentInfo, setAgentInfo] = useState({}); // 智能体信息缓存

  // 监听任务变化，清理状态
  useEffect(() => {
    if (selectedTask) {
      // 当任务切换时，清理相关状态
      setCurrentPath('');
      setBreadcrumbs([]);
      setSelectedFile(null);
      setIsViewerVisible(false);
      // 不清理 agentInfo，让 loadWorkspaceFiles 来处理
    }
  }, [selectedTask?.id]);










  // 获取智能体信息
  const loadAgentInfo = async (task) => {
    try {
      // 获取任务的智能体信息
      const agents = await actionTaskAPI.getAgents(task.id);
      console.log('获取到的智能体信息:', agents);
      const agentMap = {};
      agents.forEach(agent => {
        agentMap[agent.id] = {
          name: agent.name,
          role_name: agent.role_name
        };
      });
      console.log('处理后的智能体映射:', agentMap);
      setAgentInfo(agentMap);
    } catch (error) {
      console.error('获取智能体信息失败:', error);
    }
  };

  // 加载任务的工作空间文件
  const loadWorkspaceFiles = async (task, path = '') => {
    if (!task) {
      setWorkspaceFiles([]);
      setBreadcrumbs([]);
      setCurrentPath('');
      setAgentInfo({}); // 清理智能体信息
      return;
    }

    setLoading(true);
    try {
      // 检查是否需要重新加载智能体信息
      // 如果当前任务ID与上次加载的不同，或者智能体信息为空，则重新加载
      let currentAgentInfo = agentInfo;
      const needReloadAgentInfo = Object.keys(agentInfo).length === 0 ||
                                  !agentInfo._taskId ||
                                  agentInfo._taskId !== task.id;

      if (needReloadAgentInfo) {
        await loadAgentInfo(task);
        // 重新获取任务的智能体信息，确保我们有最新的数据
        const agents = await actionTaskAPI.getAgents(task.id);
        const agentMap = { _taskId: task.id }; // 添加任务ID标记
        agents.forEach(agent => {
          agentMap[agent.id] = {
            name: agent.name,
            role_name: agent.role_name
          };
        });
        currentAgentInfo = agentMap;
        setAgentInfo(agentMap);
      }

      const data = await workspaceAPI.getWorkspaceFiles(task.id, path);

      // 统一处理所有文件数据，使用当前的智能体信息
      const processedFiles = processFileData(data, currentAgentInfo);

      // 设置当前路径和面包屑导航
      setCurrentPath(path);

      if (path) {
        // 子目录：添加面包屑导航
        const pathParts = path.split('/');
        const breadcrumbs = [{ name: task.title, path: '' }];

        // 构建面包屑路径
        let currentPath = '';
        pathParts.forEach((part) => {
          currentPath = currentPath ? `${currentPath}/${part}` : part;
          breadcrumbs.push({
            name: part,
            path: currentPath
          });
        });

        setBreadcrumbs(breadcrumbs);
      } else {
        // 根目录：只显示任务名
        setBreadcrumbs([{ name: task.title, path: '' }]);
      }

      setWorkspaceFiles(processedFiles);

      // 后端已经提供了文件大小，不需要前端异步加载

    } catch (error) {
      console.error('加载工作空间文件失败:', error);
      message.error('加载工作空间文件失败');
      setWorkspaceFiles([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理任务选择
  const handleTaskSelect = (task) => {
    setSelectedTask(task);
    setSelectedFile(null);
    loadWorkspaceFiles(task, ''); // 加载根目录
  };

  // 处理目录点击
  const handleDirectoryClick = (directory) => {
    if (directory.isDirectory) {
      // 构建新的路径：当前路径 + 目录名
      const newPath = currentPath ? `${currentPath}/${directory.file_name}` : directory.file_name;
      loadWorkspaceFiles(selectedTask, newPath);
    }
  };

  // 处理面包屑导航
  const handleBreadcrumbClick = (breadcrumb) => {
    loadWorkspaceFiles(selectedTask, breadcrumb.path);
  };

  // 查看文件
  const handleViewFile = (file) => {
    setSelectedFile(file);
    setIsViewerVisible(true);
  };



  // 删除文件
  const handleDeleteFile = (file) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文件 "${file.file_name}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await workspaceAPI.deleteWorkspaceFile(file.file_path);
          message.success('文件删除成功');
          loadWorkspaceFiles(selectedTask, currentPath); // 重新加载当前目录
        } catch (error) {
          console.error('删除文件失败:', error);
          message.error('删除文件失败');
        }
      }
    });
  };

  // 文件列表的列定义
  const columns = [
    {
      title: '文件名',
      dataIndex: 'file_name',
      key: 'file_name',
      width: '50%',
      render: (text, record) => (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            cursor: record.isDirectory ? 'pointer' : 'default',
            minHeight: '40px',
            padding: '4px 0'
          }}
          onClick={() => record.isDirectory && handleDirectoryClick(record)}
        >
          {/* 文件/文件夹图标 */}
          <div style={{
            width: 16,
            height: 16,
            marginRight: 8,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexShrink: 0
          }}>
            {record.icon || getFileIcon(record.file_name, record.isDirectory)}
          </div>

          {/* 文件信息区域 */}
          <div style={{ flex: 1, minWidth: 0 }}>
            <div style={{
              fontWeight: 500,
              fontSize: '14px',
              lineHeight: '20px',
              color: record.isDirectory ? '#1890ff' : '#262626',
              marginBottom: record.display_name && record.display_name !== record.file_name ? '2px' : 0,
              wordBreak: 'break-word'
            }}>
              {text}
            </div>
            {record.display_name && record.display_name !== record.file_name && (
              <div style={{
                fontSize: '12px',
                color: '#8c8c8c',
                lineHeight: '16px'
              }}>
                {record.display_name}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: '20%',
      render: (text, record) => record.isDirectory ? '' : text
    },
    {
      title: '修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: '20%',
      render: (text) => new Date(text).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      width: '10%',
      render: (_, record) => (
        record.isDirectory ? null : (
          <Space size="small">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewFile(record)}
              title="查看"
            />

            <Button
              type="text"
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => handleDeleteFile(record)}
              title="删除"
            />
          </Space>
        )
      )
    }
  ];

  return (
    <div className="partition-memory-tab">
      <div style={{ marginBottom: '24px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 20
        }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>工作空间浏览器</Title>
            <Text type="secondary">
              以文件浏览器的方式查看和管理所有行动任务的智能体工作空间和共享工作区，支持查看、编辑、删除。
            </Text>
          </div>
        </div>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'workspace',
            label: (
              <span>
                <FolderOutlined style={{ color: '#1677ff' }} />
                工作空间浏览器
              </span>
            ),
            children: (
              <div style={{ display: 'flex', height: 'calc(100vh - 300px)', gap: '16px' }}>
                {/* 左侧：任务列表 */}
                <div style={{ width: 280, flexShrink: 0 }}>
                  <TaskSelector
                    onTaskSelect={handleTaskSelect}
                    selectedTask={selectedTask}
                  />
                </div>

                {/* 右侧：文件列表 */}
                <div style={{ flex: 1 }}>
                  <Card
                    title={
                      selectedTask ? (
                        <div>
                          <FolderOutlined style={{ marginRight: 8 }} />
                          {selectedTask.title} - 项目文件
                        </div>
                      ) : (
                        <div>
                          <FolderOutlined style={{ marginRight: 8 }} />
                          项目文件列表
                        </div>
                      )
                    }
                    style={{ height: '100%' }}
                    styles={{ body: { padding: 0 } }}
                  >
                    {/* 面包屑导航 */}
                    {selectedTask && breadcrumbs.length > 0 && (
                      <div style={{
                        padding: '12px 16px',
                        borderBottom: '1px solid #f0f0f0',
                        backgroundColor: '#fafafa'
                      }}>
                        <Breadcrumb
                          items={breadcrumbs.map((crumb, index) => ({
                            key: index,
                            title: (
                              <span
                                onClick={() => handleBreadcrumbClick(crumb)}
                                style={{
                                  cursor: index < breadcrumbs.length - 1 ? 'pointer' : 'default',
                                  color: index < breadcrumbs.length - 1 ? '#1890ff' : 'inherit'
                                }}
                              >
                                {index === 0 && <HomeOutlined style={{ marginRight: 4 }} />}
                                {crumb.name}
                              </span>
                            )
                          }))}
                        />
                      </div>
                    )}

                    <Table
                      columns={columns}
                      dataSource={workspaceFiles}
                      loading={loading}
                      pagination={false}
                      size="middle"
                      rowClassName={() => 'workspace-table-row'}
                      locale={{
                        emptyText: selectedTask ? '该任务暂无项目文件' : '请先选择一个任务'
                      }}
                      // 标准文件浏览器，不需要树形展开
                    />


                  </Card>
                </div>
              </div>
            )
          },
          {
            key: 'template',
            label: (
              <span>
                <BookOutlined style={{ color: '#52c41a' }} />
                工作空间模板
              </span>
            ),
            children: <WorkspaceTemplateTab />
          }
        ]}
      />

      {/* 文件查看器 */}
      <WorkspaceFileViewer
        visible={isViewerVisible}
        file={selectedFile}
        onClose={() => {
          setIsViewerVisible(false);
          setSelectedFile(null);
        }}
        onSave={() => {
          message.success('文件保存成功');
          loadWorkspaceFiles(selectedTask, currentPath); // 重新加载当前目录
        }}
      />
    </div>
  );
};

export default PartitionWorkspaceTab;
