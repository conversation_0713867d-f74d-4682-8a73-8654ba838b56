import React, { useState, useEffect } from 'react';
import {
  Card, Button, Space, Table, Typography, Modal, Form, Select,
  message, Badge, Popconfirm, List, Avatar, Input, Tag, Empty
} from 'antd';
import {
  UserOutlined, PlusOutlined, DeleteOutlined, LinkOutlined,
  DatabaseOutlined, ApiOutlined, SettingOutlined
} from '@ant-design/icons';
import { externalKnowledgeAPI } from '../../../services/api';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Search } = Input;

const RoleKnowledgeBinding = () => {
  const [roles, setRoles] = useState([]);
  const [knowledges, setKnowledges] = useState([]);
  const [selectedRole, setSelectedRole] = useState(null);
  const [roleBindings, setRoleBindings] = useState([]);
  const [availableKnowledges, setAvailableKnowledges] = useState([]);
  const [loading, setLoading] = useState(false);
  const [bindingLoading, setBingingLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [roleSearchText, setRoleSearchText] = useState('');
  const [form] = Form.useForm();

  // 获取初始数据
  useEffect(() => {
    fetchRoles();
    fetchKnowledges();
  }, []);

  // 当选择角色时，获取该角色的绑定信息
  useEffect(() => {
    if (selectedRole) {
      fetchRoleBindings(selectedRole.id);
    }
  }, [selectedRole]);

  const fetchRoles = async () => {
    try {
      const response = await externalKnowledgeAPI.getAllRoles();
      if (response.success) {
        setRoles(response.data);
        // 默认选择第一个角色
        if (response.data.length > 0) {
          setSelectedRole(response.data[0]);
        }
      }
    } catch (error) {
      message.error('获取角色列表失败');
      console.error('获取角色列表失败:', error);
    }
  };

  const fetchKnowledges = async () => {
    try {
      const response = await externalKnowledgeAPI.getExternalKnowledges();
      if (response.success) {
        setKnowledges(response.data);
      }
    } catch (error) {
      console.error('获取外部知识库列表失败:', error);
    }
  };

  const fetchRoleBindings = async (roleId) => {
    setLoading(true);
    try {
      const response = await externalKnowledgeAPI.getRoleExternalKnowledges(roleId);
      if (response.success) {
        setRoleBindings(response.data);
      } else {
        message.error(response.message || '获取角色绑定失败');
      }
    } catch (error) {
      message.error('获取角色绑定失败');
      console.error('获取角色绑定失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 显示添加绑定模态框
  const showBindingModal = async () => {
    if (!selectedRole) {
      message.warning('请先选择一个角色');
      return;
    }

    try {
      // 获取可用的外部知识库（排除已绑定的）
      const boundKnowledgeIds = roleBindings.map(binding => binding.external_knowledge_id);
      const available = knowledges.filter(kb => !boundKnowledgeIds.includes(kb.id));
      setAvailableKnowledges(available);
      
      if (available.length === 0) {
        message.info('该角色已绑定所有可用的外部知识库');
        return;
      }

      form.resetFields();
      setModalVisible(true);
    } catch (error) {
      message.error('获取可用知识库失败');
      console.error('获取可用知识库失败:', error);
    }
  };

  // 处理绑定提交
  const handleBindingSubmit = async (values) => {
    setBingingLoading(true);
    try {
      const response = await externalKnowledgeAPI.bindRoleExternalKnowledge(
        selectedRole.id,
        values.knowledge_id,
        values.config || {}
      );
      
      if (response.success) {
        message.success('知识库绑定成功');
        fetchRoleBindings(selectedRole.id);
        setModalVisible(false);
      } else {
        message.error(response.message || '绑定失败');
      }
    } catch (error) {
      message.error('绑定失败');
      console.error('绑定失败:', error);
    } finally {
      setBingingLoading(false);
    }
  };

  // 解除绑定
  const handleUnbind = async (knowledgeId) => {
    try {
      const response = await externalKnowledgeAPI.unbindRoleExternalKnowledge(
        selectedRole.id,
        knowledgeId
      );
      
      if (response.success) {
        message.success('绑定解除成功');
        fetchRoleBindings(selectedRole.id);
      } else {
        message.error(response.message || '解除绑定失败');
      }
    } catch (error) {
      message.error('解除绑定失败');
      console.error('解除绑定失败:', error);
    }
  };

  // 过滤角色列表
  const filteredRoles = roles.filter(role => 
    role.name.toLowerCase().includes(roleSearchText.toLowerCase()) ||
    (role.description && role.description.toLowerCase().includes(roleSearchText.toLowerCase()))
  );

  const bindingColumns = [
    {
      title: '知识库名称',
      key: 'knowledge_name',
      render: (_, record) => (
        <Space>
          <DatabaseOutlined style={{ color: '#1677ff' }} />
          <span>{record.knowledge.name}</span>
        </Space>
      ),
    },
    {
      title: '提供商',
      key: 'provider',
      render: (_, record) => (
        <Space>
          <ApiOutlined style={{ color: '#52c41a' }} />
          <span>{record.provider.name}</span>
          <Tag color="blue">{record.provider.type.toUpperCase()}</Tag>
        </Space>
      ),
    },
    {
      title: '外部ID',
      key: 'external_kb_id',
      render: (_, record) => (
        <Text code>{record.knowledge.external_kb_id}</Text>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (_, record) => (
        <Badge 
          status={record.knowledge.status === 'active' ? 'success' : 'default'} 
          text={record.knowledge.status === 'active' ? '活跃' : '停用'} 
        />
      ),
    },
    {
      title: '绑定时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => date ? new Date(date).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<SettingOutlined />}
            onClick={() => message.info('配置功能开发中')}
            size="small"
          >
            配置
          </Button>
          <Popconfirm
            title="确定要解除这个知识库绑定吗？"
            description="解除后该角色将无法访问此外部知识库。"
            onConfirm={() => handleUnbind(record.external_knowledge_id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              解除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ display: 'flex', gap: '16px', height: '600px' }}>
      {/* 左侧角色列表 */}
      <Card
        title="角色列表"
        style={{ width: '300px', height: '100%' }}
        styles={{ body: { padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' } }}
      >
        <Search
          placeholder="搜索角色"
          value={roleSearchText}
          onChange={(e) => setRoleSearchText(e.target.value)}
          style={{ marginBottom: '12px' }}
          allowClear
        />
        
        <List
          dataSource={filteredRoles}
          renderItem={(role) => (
            <List.Item
              onClick={() => setSelectedRole(role)}
              style={{
                cursor: 'pointer',
                backgroundColor: selectedRole?.id === role.id ? '#e6f7ff' : 'transparent',
                borderRadius: '4px',
                padding: '8px',
                marginBottom: '4px'
              }}
            >
              <List.Item.Meta
                avatar={<Avatar icon={<UserOutlined />} />}
                title={role.name}
                description={
                  <div>
                    <Text type="secondary" ellipsis style={{ fontSize: '12px' }}>
                      {role.description || '暂无描述'}
                    </Text>
                    <div style={{ marginTop: '4px' }}>
                      <Badge 
                        count={roleBindings.filter(b => b.role_id === role.id).length} 
                        showZero 
                        color="blue" 
                        size="small"
                      />
                      <Text type="secondary" style={{ fontSize: '11px', marginLeft: '4px' }}>
                        个绑定
                      </Text>
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>

      {/* 右侧绑定管理 */}
      <Card 
        title={
          <Space>
            <LinkOutlined />
            <span>知识库绑定管理</span>
            {selectedRole && (
              <Tag color="blue">{selectedRole.name}</Tag>
            )}
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showBindingModal}
            disabled={!selectedRole}
          >
            添加绑定
          </Button>
        }
        style={{ flex: 1, height: '100%' }}
        styles={{ body: { height: 'calc(100% - 57px)', overflow: 'auto' } }}
      >
        {selectedRole ? (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <Title level={5} style={{ margin: 0 }}>
                {selectedRole.name} 的外部知识库绑定
              </Title>
              <Text type="secondary">
                {selectedRole.description || '暂无描述'}
              </Text>
            </div>

            <Table
              columns={bindingColumns}
              dataSource={roleBindings}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
                showTotal: (total) => `共 ${total} 个绑定`,
              }}
              locale={{
                emptyText: (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="该角色还没有绑定任何外部知识库"
                  />
                )
              }}
            />
          </div>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="请从左侧选择一个角色来管理其知识库绑定"
          />
        )}
      </Card>

      {/* 添加绑定模态框 */}
      <Modal
        title={`为 "${selectedRole?.name}" 添加知识库绑定`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleBindingSubmit}
        >
          <Form.Item
            name="knowledge_id"
            label="选择外部知识库"
            rules={[{ required: true, message: '请选择要绑定的外部知识库' }]}
          >
            <Select placeholder="请选择外部知识库" showSearch>
              {availableKnowledges.map(kb => (
                <Option key={kb.id} value={kb.id}>
                  <Space>
                    <DatabaseOutlined />
                    <span>{kb.name}</span>
                    <Tag size="small" color="blue">{kb.provider.type.toUpperCase()}</Tag>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {kb.provider.name}
                    </Text>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="config"
            label="绑定配置 (可选)"
            help="可以为该角色定制特殊的查询参数，留空则使用知识库默认配置"
          >
            <Input.TextArea 
              rows={3} 
              placeholder='{"priority": 1, "custom_params": {}}'
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={bindingLoading}>
                确定绑定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RoleKnowledgeBinding;
