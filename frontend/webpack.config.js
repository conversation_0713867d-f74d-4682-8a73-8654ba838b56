const path = require('path');
const webpack = require('webpack');
const dotenv = require('dotenv');

// 加载.env环境变量
const env = dotenv.config().parsed || {};
// 从环境变量中获取后端URL
const BACKEND_URL = env.REACT_APP_API_URL ? env.REACT_APP_API_URL.replace('/api', '') : 'http://localhost:8080';

module.exports = {
  // ... 其他webpack配置将保持不变
  
  // 添加devServer配置
  devServer: {
    allowedHosts: ['localhost', '127.0.0.1','0.0.0.0'],
    host: process.env.HOST || 'localhost',
    port: process.env.PORT || 3000,
    hot: true,
    historyApiFallback: true,
    proxy: {
      '/api': {
        target: process.env.REACT_APP_API_URL ? process.env.REACT_APP_API_URL.replace('/api', '') : BACKEND_URL || 'http://localhost:8080',
        pathRewrite: { '^/api': '/api' },
        changeOrigin: true,
        secure: false
      },
    },
  },
}; 
