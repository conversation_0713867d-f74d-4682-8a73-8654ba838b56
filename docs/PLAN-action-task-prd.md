# 行动任务详情 - 产品需求文档

## 1. 概述

### 1.1 产品背景

行动任务详情是ABM-LLM会话系统的核心功能，提供智能体在特定行动空间中的完整任务记录和分析能力。行动任务详情页展示了智能体行为、环境变量变化、规则触发和监督干预的全过程，为用户提供深入了解和分析任务执行情况的完整视图。

### 1.2 产品目标

- 提供直观高效的行动任务详情展示界面
- 实现任务记录的多维度展示和搜索
- 追踪环境变量的实时状态变化和历史轨迹
- 记录规则触发情况及其对任务的影响，清晰区分自然语言规则与逻辑规则
- 支持监督者对任务的全方位观察、评估与适度干预
- 提供任务执行结果的分析和总结功能

### 1.3 用户群体

- 业务分析师：分析智能体在特定业务场景中的行为和决策
- 企业用户：观察和评估智能体解决实际问题的过程和效果
- 研究人员：研究智能体交互模式和环境变量影响
- 系统管理员：监控任务执行状态和系统性能
- 监督专家：引导任务执行方向并在关键节点进行专业干预

### 1.4 系统核心设计原则

#### 1.4.1 实例化模型

系统采用"模板-实例"设计模式，确保资源的灵活复用和隔离：

1. **角色与智能体关系**：
   - 角色(Role)作为模板，定义通用能力、知识和工具
   - 创建行动任务时，角色被实例化为特定的智能体(Agent)
   - 智能体记录自己的name、id以及所属行动任务ID和关联角色ID
   - 智能体可以在任务执行过程中积累独立的经验和记忆

2. **环境变量实例化**：
   - 环境变量在行动任务中也是实例化的
   - 每个行动任务拥有独立的环境变量集合
   - 环境变量可在任务执行过程中独立变化和演化

3. **规则共享机制**：
   - 规则定义在行动空间级别，无需实例化
   - 多个行动任务可共享相同的规则集
   - 规则集可以在不同任务中产生不同的触发效果

这种设计确保了：
- 资源高效复用：相同角色可用于不同任务而不互相干扰
- 状态隔离：每个任务的智能体和环境变量状态独立维护
- 灵活配置：可以根据任务需求组合不同的角色、规则和环境变量

## 2. 功能需求

### 2.1 基本信息区域

#### 2.1.1 任务概览
- 展示任务标题、描述和当前状态
- 显示所属行动空间和规则集信息
- 显示创建时间、持续时间和最后活动时间
- 提供任务控制按钮（终止、返回列表）

#### 2.1.2 参与者信息
- 智能体列表及其基本信息（名称、角色、描述）
- 监督者角色显示及当前状态（在线/离线）
- 智能体状态指示器（活跃/非活跃）
- 监督者与智能体的关联指示（如监督者关注的特定智能体）

#### 2.1.3 任务统计指标
- 消息总数和类型分布
- 智能体参与度统计
- 规则触发次数统计（分别统计自然语言规则和逻辑规则）
- 环境变量变化频率
- 监督干预活动统计

### 2.2 任务记录（主Tab）

#### 2.2.1 消息时间线
- 按时间顺序显示所有交互消息
- 区分不同发送者（用户、智能体、系统、监督者）的消息样式
- 显示消息发送时间和发送者信息
- 支持加载更多历史消息
- 清晰标识监督者干预消息

#### 2.2.2 消息详情
- 消息内容的完整展示
- 针对智能体消息，可选择显示思考过程
- 消息关联的环境变量变化标记
- 消息触发的规则标记（区分自然语言规则和逻辑规则）
- 监督者评论和注解（如有）

#### 2.2.3 消息输入区
- 文本输入框用于发送新消息
- 消息目标选择（特定智能体或全体智能体）
- 支持富文本格式和代码格式
- 消息发送状态指示器
- 监督者消息特殊标记选项（如指导、干预、评估等类型）

#### 2.2.4 过滤和搜索工具
- 按发送者类型过滤消息（用户/智能体/监督者/系统）
- 按时间范围过滤消息
- 按关键词搜索消息内容
- 高亮显示搜索结果
- 按规则触发或环境变化过滤消息

#### 2.2.5 智能体交互可视化卡片
- 在任务记录页面集成智能体交互动态可视化卡片
- 实时展示智能体之间的交互网络和关系图谱
- 显示智能体发言频率、互动热度和影响关系
- 提供交互模式随时间变化的动态演变视图
- 支持筛选特定时间段的交互状态
- 突出显示关键交互节点和重要对话链
- 提供节点点击功能，快速定位到相关消息
- 根据交互模式（顺序/小组/辩论/协作）呈现不同的可视化效果

#### 2.2.6 参与度分析展示
- 实时统计各智能体的参与度指标（发言次数、回应率、影响力等）
- 显示智能体贡献度评分和变化趋势
- 提供智能体知识领域覆盖图
- 展示智能体互动质量评估（共识度、互补性、创新性等）
- 支持监督者对智能体表现进行标记和评注

### 2.3 环境状态（Tab）

#### 2.3.1 当前环境变量列表
- 显示所有环境变量及其当前值
- 按类别分组显示（公共变量/角色变量/动态变量）
- 变量值的可视化表示（进度条、仪表盘等）
- 变量重要程度指示
- 监督者可修改变量的权限指示

#### 2.3.2 变量历史追踪
- 环境变量值的历史变化记录
- 时间线形式的变化轨迹图表
- 变化点标记和关联消息链接
- 历史值比较功能
- 监督者干预导致的变化特殊标记

#### 2.3.3 变量状态修改
- 支持监督者手动修改变量值
- 修改原因记录（规则触发/监督干预/系统调整）
- 修改历史日志
- 修改后的影响预测
- 修改权限控制（基于监督者级别）

#### 2.3.4 变量关联分析
- 变量之间的影响关系图
- 变量变化的因果分析
- 关键变量识别和标记
- 变量敏感度分析
- 监督干预重点区域建议

### 2.4 规则触发记录（Tab）

#### 2.4.1 规则触发列表
- 显示任务执行过程中触发的所有规则
- 清晰区分自然语言规则和逻辑规则（使用不同颜色和图标）
- 提供规则类型分组视图（全部/自然语言规则/逻辑规则）
- 按时间顺序排列，并提供多种排序选项
- 显示规则名称、类型、触发时间、触发方式
- 规则触发条件和执行结果

#### 2.4.2 规则详情视图
- 根据规则类型（自然语言规则/逻辑规则）显示不同的详情视图
- 自然语言规则详情：显示自然语言描述、触发意图分析、置信度评分
- 逻辑规则详情：显示结构化条件表达式、执行路径、性能指标
- 规则执行对环境变量的影响（区分直接影响和间接影响）
- 相关交互消息链接
- 监督者对规则执行的评论（如有）

#### 2.4.3 规则统计分析
- 各规则的触发频率统计（分类显示自然语言规则和逻辑规则）
- 规则触发时间分布图
- 规则影响程度评估
- 规则冲突检测结果
- 规则效能分析（哪些规则产生了最显著的影响）

#### 2.4.4 规则过滤工具
- 按规则类型过滤（自然语言规则/逻辑规则）
- 按规则集过滤
- 按触发时间范围过滤
- 按影响变量过滤
- 按触发频率过滤（高频/低频规则）

#### 2.4.5 规则类型对比分析
- 自然语言规则与逻辑规则触发比例
- 两种规则类型的效果对比
- 规则协同工作情况分析
- 规则类型切换的时间点和原因分析
- 不同场景下规则类型适用性评估

### 2.5 监督记录（Tab）

#### 2.5.1 监督者角色定位
- 监督者：实时监控任务执行过程，不干扰自然交互
- 评估者：对智能体行为和决策进行专业评估
- 指导者：提供建议和方向性指导
- 干预者：在必要时进行有限干预调整
- 记录者：记录重要观察和发现
- 边界守护者：确保任务在预定义范围内执行

#### 2.5.2 监督评论列表
- 监督者添加的所有评论和笔记
- 按评论类型分类（观察/评估/指导/干预）
- 按时间顺序排列
- 显示评论类型、时间、内容、影响等级
- 评论相关的任务阶段标记
- 评论关联的智能体或规则标记

#### 2.5.3 监督干预记录
- 监督者的干预行为记录
- 干预类型详细分类：
  * 环境变量修改：调整任务环境参数
  * 规则调整：启用/禁用/修改规则
  * 指令发送：向智能体发送特定指令
  * 流程干预：改变任务执行流程
  * 问题引导：提出引导性问题
- 干预时间、原因和权限验证记录
- 干预前后状态对比
- 干预效果评估

#### 2.5.4 监督评论输入
- 新监督评论的输入界面
- 评论类型选择（包括多级分类）
- 评论影响级别设置（信息性/建议性/指导性/强制性）
- 关联对象选择（特定智能体/环境变量/规则）
- 评论可见性设置（仅监督者可见/所有参与者可见）
- 评论标签和分类功能

#### 2.5.5 监督统计分析
- 监督活动频率和类型分布
- 监督干预效果评估
- 干预前后状态对比分析
- 监督建议的采纳率分析
- 不同监督策略的效果对比
- 监督活动与任务成功率的相关性分析

#### 2.5.6 监督者权限管理
- 监督级别设置（观察级/评论级/指导级/干预级）
- 权限范围配置（全局监督/特定领域监督）
- 干预限制设置（可干预次数、干预深度）
- 监督行为审计记录
- 多监督者协作机制

### 2.6 任务分析与总结（Tab）

#### 2.6.1 问题解决路径分析
- 任务执行的关键阶段标记
- 智能体解决问题的方法论分析
- 决策点和转折点识别
- 解决方案形成过程可视化
- 监督干预对解决路径的影响分析

#### 2.6.2 关键结论提取
- 智能体得出的主要结论
- 结论形成过程追踪
- 结论证据链接
- 结论可信度评估
- 结论之间的关联和冲突
- 监督者对结论的评估

#### 2.6.3 资源建议分析
- 基于任务结论的资源分配建议
- 各领域优先级评估
- 时间线和预算建议
- 投资回报预测
- 风险评估和缓解策略
- 资源分配方案比较

#### 2.6.4 任务报告生成
- 自动生成任务执行摘要报告
- 可选报告内容组件
- 报告格式定制
- 导出为多种文件格式
- 报告中包含监督见解选项

## 3. 界面设计

### 3.1 总体布局

- 顶部：任务标题、返回按钮、控制按钮（暂停/继续、终止）
- 中上部：任务基本信息卡片（描述、行动空间、状态、时间信息）
- 中部：主要内容区域，使用选项卡组织不同功能
- 底部：消息输入框和发送按钮（在任务记录Tab中）
- 右侧：参与者信息面板（智能体和监督者列表及状态）
- 右上角：监督模式切换按钮（普通视图/监督视图）

### 3.2 功能Tab页设计

#### 3.2.1 任务记录Tab
- 左侧：可折叠的过滤和搜索工具栏
- 中央：消息时间线，包含不同类型消息的样式
- 底部：消息输入区（普通输入/监督指令输入模式切换）
- 右侧：当前环境状态快照和监督控制面板（可折叠）

#### 3.2.2 环境状态Tab
- 左侧：环境变量分类导航树
- 中央：选中变量的详细信息和历史图表
- 右侧：变量关联分析图
- 监督视图：额外显示变量修改控制面板

#### 3.2.3 规则触发Tab
- 左侧：规则过滤工具（包括规则类型过滤器）
- 中央：规则触发列表和详情（区分自然语言规则/逻辑规则）
- 右侧：规则统计图表和类型对比分析
- 监督视图：额外显示规则管理控制面板

#### 3.2.4 监督记录Tab
- 左侧：监督活动类型过滤
- 中央：监督记录列表和详情
- 底部：监督评论输入区
- 右侧：监督统计分析和权限状态面板

#### 3.2.5 任务分析Tab
- 上部：关键指标卡片组
- 中部：问题解决路径可视化
- 下部：关键结论和建议列表
- 右侧：报告生成工具
- 监督视图：额外显示监督见解和建议输入区

### 3.3 交互元素

- 变量值展示使用适合的可视化元素（进度条、仪表盘、趋势图）
- 规则触发使用高亮标记和图标，区分自然语言规则（蓝色）和逻辑规则（绿色）
- 消息类型使用颜色编码区分（用户/智能体/系统/监督者）
- 监督干预使用特殊标记（黄色警示图标）
- 关键节点和事件使用标记和锚点
- 时间线使用一致的设计风格贯穿所有功能Tab
- 监督控制元素使用权限敏感的显示逻辑（根据监督者级别显示/隐藏）

## 4. 数据需求

### 4.1 任务基本数据
- 任务ID、标题、描述
- 创建时间、最后活动时间、总持续时间
- 当前状态（活跃、暂停、完成、终止）
- 所属行动空间ID和名称
- 应用规则集ID和名称
- 交互模式（sequential, panel, debate, collaborative）
- 监督设置（监督级别、监督范围）

### 4.2 参与者数据
- 智能体ID、名称、角色、描述
- 智能体状态（活跃/非活跃）
- 智能体参与统计（发言次数、响应时间等）
- 监督者ID、名称、专业领域
- 监督者权限级别和范围
- 监督者活动统计

### 4.3 消息数据
- 消息ID、发送者ID、接收者ID
- 消息内容、类型、发送时间
- 消息发送者类型（用户/智能体/系统/监督者）
- 思考过程内容
- 相关环境变量变化
- 相关规则触发（包含规则类型）
- 监督标记（如为监督消息）

### 4.4 环境变量数据
- 变量ID、名称、描述、类型
- 当前值和历史值
- 变化时间点
- 变化原因（规则触发/监督修改/系统调整）
- 变量间关联关系
- 变量修改权限设置

### 4.5 规则触发数据
- 规则ID、名称、规则类型（自然语言规则/逻辑规则）
- 规则内容和描述
- 触发时间、触发条件、触发方式
- 执行结果和影响
- 相关消息和环境变化
- 规则优先级和冲突信息
- 规则性能指标（如适用）

### 4.6 监督记录数据
- 监督者ID和名称
- 监督活动类型（观察/评估/指导/干预）
- 监督活动详细分类
- 活动时间、内容、目标对象
- 干预对象（环境变量、规则等）
- 干预前后状态对比
- 干预效果评估
- 干预权限验证记录

## 5. 技术要求

### 5.1 实时数据更新
- 消息列表实时更新
- 环境变量状态实时刷新
- 规则触发实时显示（区分自然语言规则/逻辑规则）
- 监督活动实时通知和同步
- 监督评论实时同步

### 5.2 数据可视化
- 环境变量趋势图表
- 规则触发统计图表（按类型分类）
- 规则类型对比图表
- 解决路径可视化
- 变量关联网络图
- 监督活动影响分析图表

### 5.3 交互响应性
- 快速消息加载和分页
- 高效的搜索和过滤（支持规则类型过滤）
- 图表的交互式探索
- 平滑的Tab切换效果
- 监督控制的即时响应

### 5.4 数据导出
- 支持导出完整任务数据
- 支持导出特定部分（如消息记录、环境变量历史、规则触发记录）
- 支持多种导出格式（JSON、CSV、PDF）
- 支持自定义导出内容
- 监督报告专用导出格式

### 5.5 权限控制
- 基于角色的监督权限管理
- 监督操作的多级审批（适用于高影响操作）
- 敏感操作的日志记录
- 监督权限的动态调整
- 紧急干预模式（特殊情况下的临时权限提升）

### 5.6 系统鲁棒性与异常处理

#### 5.6.1 异常检测机制
- 智能体异常行为检测（循环回答、离题、停止响应等）
- 系统资源监控（内存使用、响应时延、连接状态）
- 规则冲突自动识别与预警
- 环境变量异常变化检测
- 任务执行超时与进度停滞检测

#### 5.6.2 故障恢复能力
- 任务执行状态定期自动保存（增量快照）
- 断点恢复机制，支持从最近快照恢复任务
- 智能体会话上下文缓存与恢复
- 环境变量历史状态回溯能力
- 规则执行状态重建
- 网络中断后的自动重连与会话恢复
- 异常崩溃后的数据一致性修复

#### 5.6.3 降级服务策略
- 高负载情况下的功能优先级排序
- 核心功能与扩展功能分离，确保核心功能稳定性
- 复杂计算的异步处理机制
- 部分服务不可用时的替代方案
- 大模型服务中断时的逻辑规则回退策略
- 监督功能降级方案（确保基本监控与干预能力）

#### 5.6.4 错误处理流程
- 用户可见错误的友好提示与建议
- 系统级错误的详细日志记录
- 关键错误的实时告警机制
- 常见错误的自动恢复尝试
- 严重错误时的安全保护措施（数据保护、任务暂停等）
- 错误报告生成与提交机制
- 错误统计与趋势分析，支持持续改进

#### 5.6.5 边界情况处理
- 超长任务执行的性能优化与资源管理
- 海量消息场景下的高效加载与渲染
- 极端环境变量值处理策略
- 大量规则同时触发的冲突解决机制
- 多智能体密集互动的调度优化
- 极端用户操作（如高频干预）的节流与保护
- 跨时区、大范围时间跨度的处理方案

## 6. 验收标准

### 6.1 功能验收
- 所有规定功能正常工作
- 数据正确显示和更新
- 规则类型的准确区分与可视化
- 监督功能的正确实现
- 图表正确渲染
- 导出功能工作正常

### 6.2 性能验收
- 页面加载时间不超过3秒
- 消息列表滚动流畅
- 图表渲染不卡顿
- 大量数据处理不影响系统响应
- 监督操作的响应时间不超过1秒

### 6.3 用户体验验收
- 界面美观一致
- 导航清晰直观
- 规则类型区分直观明确
- 监督控制元素符合人体工程学
- 操作路径简洁明了
- 反馈机制及时有效

### 6.4 鲁棒性验收标准

#### 6.4.1 稳定性测试
- 系统连续运行72小时无功能降级
- 大量并发任务情况下保持响应时间
- 网络条件波动下的正常运行能力
- 随机故障注入后的恢复能力
- 长期任务（持续数周）的稳定执行

#### 6.4.2 异常恢复测试
- 模拟服务中断后恢复正常所需时间不超过30秒
- 断电/强制关闭后数据丢失率不超过1分钟内的变更
- 智能体服务中断后的会话连贯性保持
- 用户会话异常中断后的恢复体验
- 90%以上的非致命错误能实现自动恢复

#### 6.4.3 负载测试
- 单任务支持最少10个智能体同时参与
- 系统能同时处理不少于100个活跃任务
- 单任务历史消息支持加载不少于10,000条
- 大量环境变量变化（每秒10次以上）时系统保持稳定
- 高频规则触发（每秒5次以上）时保持正常运行

## 7. 开发优先级

### 7.1 第一阶段（核心功能）
- 任务基本信息展示
- 任务记录Tab完整实现
- 环境状态Tab基础功能
- 规则触发基础记录（包含类型区分）
- 基本监督观察功能
- 基本控制功能（暂停/继续、终止）

### 7.2 第二阶段（重要扩展）
- 规则触发Tab完整实现（包含类型对比分析）
- 环境状态Tab高级功能
- 监督记录Tab基础功能
- 监督干预功能实现
- 搜索和过滤工具完善

### 7.3 第三阶段（完善功能）
- 任务分析Tab完整实现
- 监督记录Tab高级功能
- 监督权限管理系统
- 数据可视化完善
- 导出功能实现

### 7.4 最终阶段（优化和完善）
- 性能优化
- 用户界面细节完善
- 监督工作流程优化
- 兼容性测试和调整
- 文档和帮助系统完善

## 8. 交互模式详细说明

根据 `models.py` 中的定义，行动任务支持四种主要交互模式：

### 8.1 顺序模式（Sequential）
- 智能体按照预定义的顺序依次发言
- 每轮对话中每个智能体只能发言一次
- 适合结构化问题分析和逐步推理场景
- 交互示例：营销团队成员依次提出营销计划的不同方面
- 监督角色：主要作为流程协调者，确保讨论有序进行

### 8.2 小组模式（Panel）
- 智能体组成专家小组，共同讨论问题
- 没有严格的发言顺序，但由主持人协调
- 适合多角度问题分析和意见汇总
- 交互示例：医疗专家小组讨论复杂病例的诊断方案
- 监督角色：主要作为主持人，引导讨论方向，确保各方面得到充分考虑

### 8.3 辩论模式（Debate）
- 智能体分为正反两方进行结构化辩论
- 包含开场陈述、反驳、总结等环节
- 适合有争议性话题的分析和决策
- 交互示例：技术团队就不同技术架构方案进行辩论
- 监督角色：主要作为裁判，确保辩论规则遵守，适时提供平衡视角

### 8.4 协作模式（Collaborative）
- 智能体共同协作解决一个复杂问题
- 可以自由发言，共同构建解决方案
- 适合创造性问题和复杂方案设计
- 交互示例：产品团队协作设计新产品功能并评估可行性
- 监督角色：主要作为顾问，在关键节点提供指导，确保协作高效进行

每种交互模式下，监督者的角色定位和干预方式应有所不同，界面也应相应调整以反映其独特的交互特点和适用场景。 