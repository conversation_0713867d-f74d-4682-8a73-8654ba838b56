# ABM-LLM-V2 统一容器镜像
FROM node:18-alpine AS frontend-builder

# 设置工作目录
WORKDIR /app/frontend

# 安装pnpm
RUN npm install -g pnpm

# 复制前端package文件
COPY frontend/package.json frontend/pnpm-lock.yaml* ./

# 安装前端依赖
RUN pnpm install --frozen-lockfile

# 复制前端源代码
COPY frontend/ .

# 主镜像：基于Python
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONIOENCODING=utf-8
ENV LC_ALL=C.UTF-8
ENV LANG=C.UTF-8
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# 安装Node.js和npm
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# 安装pnpm
RUN npm install -g pnpm

# 复制Python requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装uv（用于MCP服务器）
RUN pip install uv

# 复制后端应用代码
COPY . .

# 从前端构建阶段复制前端代码
COPY --from=frontend-builder /app/frontend /app/frontend

# 创建必要的目录
RUN mkdir -p data/conversations data/agents data/worlds logs

# 复制supervisor配置
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 设置权限
RUN chmod +x run_app.py

# 暴露端口
EXPOSE 8080 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/api/health || exit 1

# 使用supervisor启动多个服务
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
