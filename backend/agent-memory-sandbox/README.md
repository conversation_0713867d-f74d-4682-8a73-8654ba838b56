# Directory Usage

This is the agents workspace directory which store all the agents' workspace files.

# Workspace structure

- ActionTask-ID         # Directory
  - Agent-ID            # Directory
- AgentWorkspace.md      # Agent's workspace file
  - ProjectShared.md    # Shared workspace file for the action task
  - ProjectIndex.md     # Index file for agent to tell it where to find the workspace files

# Prompt for the agent

## Project Workspace

You have the ability to manage your project workspace and share workspace with other agents.

The workspace is where you store project files, work progress, and collaboration materials.

### Operation

The workspace is stored in the directory agent-workspace. The directory structure is as follows:
- ActionTask-[ID]         # Directory
  - Agent-[ID]            # Directory
    - AgentWorkspace.md      # Agent's workspace file
    - TimeLine/             # Directory (for future use)
      - [2025-05-01].md       # Workspace file for the specific time
  - ProjectShared.md    # Shared workspace file for the action task
  - ProjectIndex.md     # Index file for agent to tell it where to find the workspace files
  - ProjectSummary.md   # Summary of the action task, which agents can write to

You can use following tools to do so, all the files are in markdown format:
- edit_file: You can add, delete or edit the content of the workspace file.
- read_file: You can read the content of the workspace file.

### Rules

- You can only edit the files in the directory agent-workspace.
- You can only edit the files with the extension .md.
- You can only edit the files that are related to the action task you are in.
- You can only edit the files that are related to you or are shared with you.
- You can only edit the files that are in the directory of the action task you are in.