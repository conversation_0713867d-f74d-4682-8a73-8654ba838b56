import React, { useState, useEffect } from 'react';
import { Table, Tag, Button, Empty, Tooltip, Tabs, Space, Typography, Divider } from 'antd';
import { LineChartOutlined, GlobalOutlined, RobotOutlined } from '@ant-design/icons';

const { TabPane } = Tabs;
const { Text } = Typography;

const ActionTaskEnvironment = ({ task, showGlobalOnly = false }) => {
  // 环境变量状态
  const [environmentVariables, setEnvironmentVariables] = useState([]);
  const [globalVariables, setGlobalVariables] = useState([]);

  // 初始化时设置环境变量
  useEffect(() => {
    console.log('ActionTaskEnvironment组件接收到任务数据:', task);
    if (task && task.environment_variables) {
      // 所有环境变量
      const allVars = task.environment_variables;

      // 严格过滤环境变量，确保有正确的格式且来源为task
      const validVars = allVars.filter(variable =>
        // 必须有name、label和value属性才是有效的环境变量
        variable &&
        variable.name &&
        (variable.label !== undefined) &&
        (variable.value !== undefined) &&
        // 排除明显不是环境变量的数据
        typeof variable !== 'string' &&
        // 确保只是任务全局变量，不包含智能体变量
        (variable.source === 'task' || !variable.agent_id)
      );

      // 确保每个变量都有唯一的key属性，用于React列表渲染
      const varsWithKeys = validVars.map((variable, index) => ({
        ...variable,
        key: variable.id || `${variable.name}-${index}`,
        // 保留_hasChanged和_isNew标记，用于闪烁效果
        _hasChanged: variable._hasChanged === true,
        _isNew: variable._isNew === true
      }));

      // 打印变化的变量和新变量，便于调试
      const changedVars = varsWithKeys.filter(v => v._hasChanged && !v._isNew);
      if (changedVars.length > 0) {
        console.log('ActionTaskEnvironment组件检测到变化的环境变量:', changedVars.map(v => v.name));
      }

      const newVars = varsWithKeys.filter(v => v._isNew);
      if (newVars.length > 0) {
        console.log('ActionTaskEnvironment组件检测到新的环境变量:', newVars.map(v => v.name));
      }

      setEnvironmentVariables(varsWithKeys);
      setGlobalVariables(varsWithKeys);
    }
  }, [task]);

  // 环境变量表格列定义 - 精简版
  const columns = [
    {
      title: '变量名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      width: '30%',
    },
    {
      title: '变量标签',
      dataIndex: 'label',
      key: 'label',
      ellipsis: true,
      width: '30%',
    },
    {
      title: '当前值',
      dataIndex: 'value',
      key: 'value',
      width: '40%',
      render: (value, record) => {
        // 如果值为空，不显示任何内容
        if (value === undefined || value === null || value === '') {
          return (
            <div>
              <span></span>
            </div>
          );
        }

        let displayValue = value;
        let color = 'blue';

        // 直接显示文本值
        displayValue = String(value);

        // 检查变量是否有变化标记或是新变量，如果是则添加闪烁效果的类名
        const hasChanged = record._hasChanged === true;
        const isNew = record._isNew === true;

        return (
          <div className={hasChanged ? 'variable-flash' : ''}>
            <Tag color={color}>
              {displayValue}
              {isNew && <span style={{ marginLeft: 4, color: '#52c41a', fontWeight: 'bold' }}>(新)</span>}
            </Tag>
          </div>
        );
      },
    }
  ];

  // 渲染任务环境变量
  const renderTaskVariables = () => {
    if (globalVariables.length === 0) {
      return <Empty description="暂无任务环境变量" image={Empty.PRESENTED_IMAGE_SIMPLE} />;
    }

    // 注意: React要求列表渲染时每项必须有唯一的key属性
    // 这里通过rowKey函数确保即使某些记录缺少key属性时也能生成唯一标识
    // 优先使用: 1.记录中已有的key属性 2.记录ID 3.由name和label组合生成的唯一字符串
    return (
      <Table
        dataSource={globalVariables}
        columns={columns}
        rowKey={record => record.key || record.id || `${record.name}-${record.label}`}
        pagination={false}
        size="small"
        bordered={false}
      />
    );
  };

  // 该组件现在只显示任务环境变量
  return (
    <div>
      {renderTaskVariables()}
    </div>
  );
};

export default ActionTaskEnvironment;