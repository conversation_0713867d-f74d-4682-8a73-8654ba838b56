import { useState } from 'react';
import { Tabs, Card, Typography } from 'antd';
import {
  ClockCircleOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import ConversationMemoryTab from './ConversationMemoryTab';
import KnowledgeBaseTab from './KnowledgeBaseTab';
import WorkspaceTemplateModal from './WorkspaceTemplateModal';
import DeleteWorkspaceModal from './DeleteWorkspaceModal';

const { Title, Text } = Typography;

/**
 * 项目空间管理主组件
 * 整合所有项目空间类型的标签页，包括分区项目空间功能
 */
const WorkspaceManagement = () => {
  const [activeTab, setActiveTab] = useState('conversation');
  const [selectedWorkspace, setSelectedWorkspace] = useState(null);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [isTemplateModalVisible, setIsTemplateModalVisible] = useState(false);

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    setSelectedWorkspace(null); // 切换标签页时清空选中的项目文件
  };

  // 处理选择项目文件
  const handleSelectWorkspace = (workspace) => {
    setSelectedWorkspace(workspace);
  };

  // 显示删除确认对话框
  const showDeleteModal = () => {
    if (!selectedWorkspace) {
      return;
    }
    setIsDeleteModalVisible(true);
  };

  // 显示创建模板对话框
  const showTemplateModal = () => {
    if (!selectedWorkspace) {
      return;
    }
    setIsTemplateModalVisible(true);
  };

  // 确认删除项目文件
  const confirmDelete = () => {
    if (selectedWorkspace) {
      // 这里应该调用API删除项目文件
      console.log('删除项目文件:', selectedWorkspace);
      setSelectedWorkspace(null);
      setIsDeleteModalVisible(false);
    }
  };

  // 创建模板
  const handleCreateTemplate = (templateData) => {
    // 这里应该调用API创建模板
    console.log('创建模板:', templateData);
    setIsTemplateModalVisible(false);
  };

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 20
        }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>分区项目空间管理</Title>
            <Text type="secondary">
              管理不同类型的记忆，包括会话记忆、工作空间模板、专业知识记忆
            </Text>
          </div>
        </div>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={[
          {
            key: 'conversation',
            label: (
              <span>
                <ClockCircleOutlined style={{ color: '#52c41a' }} />
                会话记忆
              </span>
            ),
            children: (
              <Card
                variant="borderless"
                style={{
                  borderRadius: '12px',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
                }}
              >
                <ConversationMemoryTab />
              </Card>
            )
          },

          {
            key: 'knowledge-base',
            label: (
              <span>
                <DatabaseOutlined style={{ color: '#13c2c2' }} />
                专业知识记忆（知识库）
              </span>
            ),
            children: (
              <Card
                variant="borderless"
                style={{
                  borderRadius: '12px',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
                }}
              >
                <KnowledgeBaseTab />
              </Card>
            )
          }
        ]}
      />

      {/* 删除确认对话框 */}
      <DeleteWorkspaceModal
        visible={isDeleteModalVisible}
        onCancel={() => setIsDeleteModalVisible(false)}
        onConfirm={confirmDelete}
      />

      {/* 创建模板对话框 */}
      <WorkspaceTemplateModal
        visible={isTemplateModalVisible}
        onCancel={() => setIsTemplateModalVisible(false)}
        onSubmit={handleCreateTemplate}
        workspace={selectedWorkspace}
      />
    </div>
  );
};

export default WorkspaceManagement;
