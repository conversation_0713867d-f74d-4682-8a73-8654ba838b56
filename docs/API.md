# API 参考文档

本文档列出了后端提供的所有API接口，用于前端开发和调试。所有API路由都以`/api`为前缀。

## 后端 REST API

### 健康检查

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/health` | GET | API服务健康检查 |

### 认证管理 (Authentication)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/auth/login` | POST | 用户登录 |
| `/auth/logout` | POST | 用户登出 |
| `/auth/verify` | GET | 验证JWT令牌 |
| `/auth/user` | GET | 获取当前用户信息 |
| `/auth/change-password` | POST | 修改用户密码 |

### 行动任务管理 (Action Tasks)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/action-tasks` | GET | 获取所有行动任务列表，支持`include_agents=true`参数 |
| `/action-tasks/<task_id>` | GET | 获取特定行动任务详情 |
| `/action-tasks` | POST | 创建新行动任务 |
| `/action-tasks/<task_id>` | PUT | 更新行动任务信息 |
| `/action-tasks/<task_id>` | DELETE | 删除行动任务 |
| `/action-tasks/<task_id>/agents` | GET | 获取行动任务的智能体列表 |
| `/action-tasks/<task_id>/agents` | POST | 为行动任务添加智能体 |
| `/action-tasks/<task_id>/agents/<agent_id>` | DELETE | 从行动任务中移除智能体 |
| `/action-tasks/<task_id>/agents/<agent_id>/default` | PUT | 设置智能体为默认智能体 |
| `/action-tasks/<task_id>/direct-agents` | GET | 获取任务直接关联的智能体列表 |
| `/action-tasks/<task_id>/direct-agents` | POST | 为任务添加直接智能体 |
| `/action-tasks/<task_id>/default` | PUT | 设置默认规则集 |
| `/action-tasks/<task_id>/environment` | GET | 获取任务环境信息 |
| `/action-tasks/<task_id>/environment-variables` | GET | 获取任务的环境变量列表 |
| `/action-tasks/<task_id>/environment-variables` | POST | 创建任务的环境变量 |
| `/action-tasks/<task_id>/environment-variables/<variable_id>` | PUT | 更新任务的环境变量 |
| `/action-tasks/<task_id>/environment-variables/<variable_id>` | DELETE | 删除任务的环境变量 |
| `/action-tasks/<task_id>/start` | POST | 启动行动任务 |
| `/action-tasks/<task_id>/stop` | POST | 停止行动任务 |
| `/action-tasks/<task_id>/pause` | POST | 暂停行动任务 |
| `/action-tasks/<task_id>/resume` | POST | 恢复行动任务 |
| `/action-tasks/<task_id>/reset` | POST | 重置行动任务 |
| `/action-tasks/<task_id>/status` | GET | 获取行动任务状态 |
| `/action-tasks/<task_id>/conversations` | GET | 获取行动任务的会话列表 |
| `/action-tasks/<task_id>/conversations/<conversation_id>` | GET | 获取行动任务的特定会话 |
| `/action-tasks/<task_id>/conversations/<conversation_id>/messages` | GET | 获取会话的消息列表 |
| `/action-tasks/<task_id>/conversations/<conversation_id>/messages` | POST | 发送消息，支持`stream=1`参数启用流式响应 |
| `/action-tasks/<task_id>/conversations` | POST | 创建新会话 |
| `/action-tasks/<task_id>/conversations/<conversation_id>` | PUT | 更新会话信息 |
| `/action-tasks/<task_id>/conversations/<conversation_id>` | DELETE | 删除会话 |
| `/action-tasks/<task_id>/auto-discussion` | POST | 启动自动讨论 |
| `/action-tasks/<task_id>/auto-discussion/stop` | POST | 停止自动讨论 |
| `/action-tasks/<task_id>/cancel` | POST | 取消任务执行 |
| `/action-tasks/<task_id>/workspace-files` | GET | 获取行动任务的项目文件列表 |

### 会话管理 (Conversations)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/conversations` | GET | 获取所有会话列表 |
| `/conversations/<conversation_id>` | GET | 获取特定会话详情 |
| `/conversations` | POST | 创建新会话 |
| `/conversations/<conversation_id>` | PUT | 更新会话信息 |
| `/conversations/<conversation_id>` | DELETE | 删除会话 |
| `/conversations/<conversation_id>/agents` | GET | 获取会话的智能体列表 |
| `/conversations/<conversation_id>/agents` | POST | 为会话添加智能体 |
| `/conversations/<conversation_id>/agents/<agent_id>` | DELETE | 从会话中移除智能体 |
| `/conversations/<conversation_id>/messages` | GET | 获取会话的消息列表 |
| `/conversations/<conversation_id>/messages` | POST | 在会话中发送新消息，支持`stream=1`参数启用流式响应 |
| `/conversations/<conversation_id>/start` | POST | 启动会话对话 |
| `/conversations/<conversation_id>/stop` | POST | 停止会话对话 |
| `/conversations/<conversation_id>/pause` | POST | 暂停会话对话 |
| `/conversations/<conversation_id>/resume` | POST | 恢复会话对话 |
| `/conversations/<conversation_id>/status` | GET | 获取会话状态 |
| `/conversations/<conversation_id>/autonomous-tasks` | GET | 获取会话的自主任务列表 |
| `/conversations/<conversation_id>/autonomous-tasks` | POST | 创建会话的自主任务 |
| `/conversations/<conversation_id>/autonomous-tasks/<task_id>` | GET | 获取特定自主任务详情 |
| `/conversations/<conversation_id>/autonomous-tasks/<task_id>` | PUT | 更新自主任务 |
| `/conversations/<conversation_id>/autonomous-tasks/<task_id>` | DELETE | 删除自主任务 |
| `/conversations/<conversation_id>/autonomous-tasks/<task_id>/start` | POST | 启动自主任务 |
| `/conversations/<conversation_id>/autonomous-tasks/<task_id>/stop` | POST | 停止自主任务 |

### 消息管理 (Messages)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/messages/<message_id>` | GET | 获取特定消息详情 |
| `/messages/<message_id>` | PUT | 更新消息内容 |
| `/messages/<message_id>` | DELETE | 删除消息 |

### 智能体管理 (Agents)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/agents` | GET | 获取所有智能体列表，支持`status=active`参数 |
| `/agents/<agent_id>` | GET | 获取特定智能体详情 |
| `/agents` | POST | 创建新智能体 |
| `/agents/<agent_id>` | PUT | 更新智能体信息 |
| `/agents/<agent_id>` | DELETE | 删除智能体 |
| `/agents/from-role/<role_id>` | POST | 从角色创建智能体 |
| `/agents/<agent_id>/start` | POST | 启动智能体 |
| `/agents/<agent_id>/stop` | POST | 停止智能体 |
| `/agents/<agent_id>/status` | GET | 获取智能体状态 |
| `/agents/<agent_id>/status` | PUT | 更新智能体状态 |
| `/agents/<agent_id>/test` | POST | 测试智能体响应 |
| `/agents/<agent_id>/memories` | GET | 获取智能体记忆 |
| `/agents/<agent_id>/increment-usage` | POST | 增加智能体使用次数 |
| `/agents/most-used` | GET | 获取最常用的智能体，支持`limit`参数 |
| `/agents/recent` | GET | 获取最近创建的智能体，支持`limit`参数 |
| `/agents/model-configs` | GET | 获取智能体可用的模型配置 |
| `/agents/roles` | GET | 获取预定义角色列表 |

### 智能体变量管理 (Agent Variables)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/agents/<agent_id>/variables` | GET | 获取智能体的所有变量 |
| `/agents/<agent_id>/variables/<name>` | GET | 获取智能体的指定变量 |
| `/agents/<agent_id>/variables` | POST | 创建智能体变量 |
| `/agents/<agent_id>/variables/<name>` | PUT, PATCH | 更新智能体变量 |
| `/agents/<agent_id>/variables/<name>` | DELETE | 删除智能体变量 |
| `/agents/<agent_id>/variables/<name>/history` | GET | 获取智能体变量的历史记录 |

### 角色管理 (Roles)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/roles` | GET | 获取所有角色列表 |
| `/roles/<role_id>` | GET | 获取特定角色详情 |
| `/roles` | POST | 创建新角色 |
| `/roles/<role_id>` | PUT | 更新角色信息 |
| `/roles/<role_id>` | DELETE | 删除角色 |
| `/roles/<role_id>/tools` | GET | 获取角色的工具列表 |
| `/roles/<role_id>/knowledges` | GET | 获取角色的知识库列表 |
| `/roles/model-configs` | GET | 获取角色可用的模型配置 |
| `/roles/predefined` | GET | 获取预定义角色列表 |
| `/roles/recent` | GET | 获取最近使用的角色列表 |
| `/roles/most-used` | GET | 获取最常用的角色列表 |
| `/roles/<role_id>/duplicate` | POST | 复制角色 |
| `/roles/<role_id>/test` | POST | 测试角色响应，支持流式响应 |
| `/roles/<role_id>/increment-usage` | POST | 增加角色使用次数 |
| `/roles/from-predefined/<predefined_id>` | POST | 从预定义角色创建角色 |
| `/roles/<role_id>/agents` | GET | 获取角色创建的智能体 |

### 行动空间管理 (Action Spaces)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/action-spaces` | GET | 获取所有行动空间列表 |
| `/action-spaces/<space_id>` | GET | 获取特定行动空间详情 |
| `/action-spaces` | POST | 创建新行动空间 |
| `/action-spaces/<space_id>` | PUT | 更新行动空间信息 |
| `/action-spaces/<space_id>` | DELETE | 删除行动空间 |
| `/action-spaces/<space_id>/rule-sets` | GET | 获取行动空间的规则集列表 |
| `/action-spaces/<space_id>/rule-sets` | POST | 创建行动空间规则集 |
| `/action-spaces/from-template/<template_id>` | POST | 从模板创建行动空间 |
| `/action-spaces/<space_id>/roles` | GET | 获取行动空间的角色列表 |
| `/action-spaces/<space_id>/roles` | POST | 为行动空间添加角色 |
| `/action-spaces/<space_id>/roles/<role_id>` | PUT | 更新行动空间中的角色设置 |
| `/action-spaces/<space_id>/roles/<role_id>` | DELETE | 从行动空间中移除角色 |
| `/action-spaces/<space_id>/detail` | GET | 获取行动空间的详细信息 |
| `/action-spaces/<space_id>/rule-sets/<rule_set_id>/rules` | GET | 获取行动空间规则集的规则列表 |
| `/action-spaces/<space_id>/rule-sets/<rule_set_id>/rules` | POST | 向行动空间规则集添加规则 |
| `/action-spaces/<space_id>/rule-sets/<rule_set_id>/rules/<rule_id>` | DELETE | 从行动空间规则集移除规则 |
| `/action-spaces/<space_id>/rule-sets/<rule_set_id>/rules/<rule_id>/priority` | PUT | 更新行动空间规则集中规则的优先级 |
| `/action-spaces/<space_id>/available-rules` | GET | 获取行动空间可添加的规则列表 |
| `/action-spaces/<space_id>/environment-variables` | GET | 获取行动空间的环境变量列表 |
| `/action-spaces/<space_id>/environment-variables` | POST | 创建行动空间环境变量 |
| `/action-spaces/<space_id>/environment-variables/<variable_id>` | PUT | 更新行动空间环境变量 |
| `/action-spaces/<space_id>/environment-variables/<variable_id>` | DELETE | 删除行动空间环境变量 |

### 标签管理 (Tags)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/tags` | GET | 获取所有标签列表 |

### 规则管理 (Rules)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/rules` | GET | 获取所有规则列表 |
| `/rules/<rule_id>` | GET | 获取特定规则详情 |
| `/rules` | POST | 创建新规则 |
| `/rules/<rule_id>` | PUT | 更新规则信息 |
| `/rules/<rule_id>` | DELETE | 删除规则 |
| `/rules/test` | POST | 测试规则 |
| `/rule-sets` | GET | 获取所有规则集列表 |
| `/rule-sets/<rule_set_id>` | GET | 获取特定规则集详情 |
| `/rule-sets` | POST | 创建新规则集 |
| `/rule-sets/<rule_set_id>` | PUT | 更新规则集信息 |
| `/rule-sets/<rule_set_id>` | DELETE | 删除规则集 |
| `/rule-sets/<rule_set_id>/rules` | GET | 获取规则集中的所有规则 |
| `/rule-sets/<rule_set_id>/rules` | POST | 向规则集添加规则 |
| `/rule-sets/<rule_set_id>/rules/<rule_id>` | DELETE | 从规则集移除规则 |
| `/rule-sets/<rule_set_id>/rules/<rule_id>/priority` | PUT | 更新规则在规则集中的优先级 |

### 能力管理 (Capabilities)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/capabilities` | GET | 获取所有能力列表 |
| `/capabilities/<capability_id>` | GET | 获取特定能力详情 |
| `/capabilities` | POST | 创建新能力 |
| `/capabilities/<capability_id>` | PUT | 更新能力信息 |
| `/capabilities/<capability_id>` | DELETE | 删除能力 |
| `/capabilities/with_roles` | GET | 获取所有能力及其关联角色的信息 |
| `/capabilities/role/<role_id>` | GET | 获取指定角色的所有能力 |
| `/capabilities/role/<role_id>` | POST | 为角色添加能力 |
| `/capabilities/role/<role_id>/<capability_id>` | DELETE | 从角色中移除能力 |

### 工具管理 (Tools)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/tools` | GET | 获取所有工具列表 |
| `/tools/<tool_id>` | GET | 获取特定工具详情 |
| `/tools` | POST | 创建新工具 |
| `/tools/<tool_id>` | PUT | 更新工具信息 |
| `/tools/<tool_id>` | DELETE | 删除工具 |
| `/tools/role/<role_id>` | GET | 获取指定角色的所有工具 |
| `/tools/role/<role_id>` | POST | 为角色添加工具 |
| `/tools/role/<role_id>/<tool_id>` | DELETE | 从角色中移除工具 |

### 模型配置管理 (Model Configs)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/model-configs` | GET | 获取所有模型配置列表 |
| `/model-configs/<config_id>` | GET | 获取特定模型配置详情 |
| `/model-configs` | POST | 创建新模型配置 |
| `/model-configs/<config_id>` | PUT | 更新模型配置信息 |
| `/model-configs/<config_id>` | DELETE | 删除模型配置 |
| `/model-configs/<config_id>/set-default` | POST | 设置默认模型配置 |
| `/model-configs/<config_id>/test-stream` | POST | 流式测试模型配置 |
| `/model-configs/<config_id>/test` | POST | 测试模型配置 |
| `/model-configs/<config_id>/has-api-key` | GET | 检查模型配置是否有API密钥 |
| `/model-configs/detect-provider` | POST | 检测模型提供商 |
| `/model-configs/provider/<provider>/models` | GET | 获取指定提供商的模型列表 |
| `/model-configs/default` | GET | 获取默认模型配置 |
| `/model-configs/verify-api-key` | POST | 验证API密钥 |
| `/model-configs/openai/models` | POST | 获取OpenAI模型列表 |
| `/model-configs/anthropic/models` | POST | 获取Anthropic模型列表 |
| `/model-configs/google/models` | POST | 获取Google模型列表 |
| `/model-configs/azure/models` | POST | 获取Azure模型列表 |
| `/model-configs/ollama/models` | POST | 获取Ollama模型列表 |
| `/model-configs/deepseek/models` | POST | 获取DeepSeek模型列表 |
| `/model-configs/zhipu/models` | POST | 获取智谱模型列表 |
| `/model-configs/moonshot/models` | POST | 获取Moonshot模型列表 |
| `/model-configs/baichuan/models` | POST | 获取百川模型列表 |
| `/model-configs/qwen/models` | POST | 获取通义千问模型列表 |
| `/model-configs/xai/models` | POST | 获取X.ai模型列表 |
| `/model-configs/test-connection` | POST | 测试模型服务连接 |

### 系统设置管理 (Settings)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/settings` | GET | 获取所有系统设置 |
| `/settings/<key>` | GET | 获取特定系统设置 |
| `/settings` | POST | 创建或更新系统设置 |
| `/settings/<key>` | DELETE | 删除系统设置 |
| `/settings/category/<category>` | GET | 获取特定分类的系统设置 |
| `/settings/prompt-templates` | GET | 获取提示词模板 |
| `/settings/prompt-templates` | POST | 更新提示词模板 |

### 环境变量管理 (Environment Variables)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/environment-variables/tasks/<task_id>` | GET | 获取任务的所有环境变量 |
| `/action-tasks/<task_id>/environment-variables` | GET | 获取任务的所有环境变量 |
| `/action-tasks/<task_id>/environment-variables/<variable_id>` | GET | 获取任务的特定环境变量 |
| `/action-tasks/<task_id>/environment-variables/<variable_id>` | PUT | 设置任务的环境变量 |

### 外部变量管理 (External Variables)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/external-variables` | GET | 获取所有外部环境变量 |
| `/external-variables` | POST | 创建外部环境变量 |
| `/external-variables/<variable_id>` | PUT | 更新外部环境变量 |
| `/external-variables/<variable_id>` | DELETE | 删除外部环境变量 |
| `/external-variables/<variable_id>/sync` | POST | 手动同步外部环境变量 |

### 日志管理 (Logs)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/logs` | GET | 获取系统日志文件内容 |
| `/logs/tail` | GET | 获取日志文件的最后几行 |

### 统计信息 (Statistics)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/statistics/dashboard` | GET | 获取仪表盘统计数据 |
| `/statistics/system-overview` | GET | 获取系统概览统计 |
| `/statistics/task-statistics` | GET | 获取任务统计数据 |
| `/statistics/role-statistics` | GET | 获取角色统计数据 |
| `/statistics/action-space-statistics` | GET | 获取行动空间统计数据 |
| `/statistics/activity-trends` | GET | 获取活动趋势统计 |
| `/statistics/interaction-statistics` | GET | 获取交互统计数据 |
| `/statistics/ecosystem-statistics` | GET | 获取生态系统统计 |
| `/statistics/system-resources` | GET | 获取系统资源统计 |
| `/statistics/user-statistics` | GET | 获取用户统计数据 |
| `/statistics/autonomous-task-statistics` | GET | 获取自主任务统计数据 |

### 记忆管理 (Memory)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/action-tasks/<task_id>/workspace-files` | GET | 获取行动任务的所有项目文件列表 |
| `/action-tasks/<task_id>/workspace-files/<file_path>` | GET | 获取特定项目文件内容 |
| `/action-tasks/<task_id>/workspace-files/<file_path>` | PUT | 更新项目文件内容 |
| `/workspace-management/task/<task_id>/workspaces` | GET | 获取指定任务的所有项目空间信息 |
| `/workspace-management/task/<task_id>/shared-workspace` | GET | 获取任务的共享工作区 |
| `/workspace-management/task/<task_id>/shared-workspace` | PUT | 更新任务的共享工作区 |
| `/workspace-management/task/<task_id>/workspace-index` | GET | 获取任务的项目索引 |
| `/workspace-management/task/<task_id>/workspace-index` | PUT | 更新任务的项目索引 |
| `/workspace-management/task/<task_id>/project-summary` | GET | 获取项目总结 |
| `/workspace-management/task/<task_id>/project-summary` | PUT | 更新项目总结 |
| `/workspace-management/task/<task_id>/agent/<agent_id>/workspace` | GET | 获取智能体工作空间 |
| `/workspace-management/task/<task_id>/agent/<agent_id>/workspace` | PUT | 更新智能体工作空间 |
| `/workspace-management/tasks-with-agents` | GET | 获取所有行动任务及其智能体信息 |

### 外部知识库管理 (External Knowledge)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/external-kb/connections` | GET | 获取所有外部知识库连接 |
| `/external-kb/connections` | POST | 创建外部知识库连接 |
| `/external-kb/connections/<connection_id>` | GET | 获取特定外部知识库连接详情 |
| `/external-kb/connections/<connection_id>` | PUT | 更新外部知识库连接 |
| `/external-kb/connections/<connection_id>` | DELETE | 删除外部知识库连接 |
| `/external-kb/connections/<connection_id>/test` | POST | 测试外部知识库连接 |
| `/external-kb/connections/<connection_id>/sync` | POST | 同步外部知识库 |
| `/external-kb/connections/<connection_id>/documents` | GET | 获取外部知识库文档列表 |
| `/external-kb/query` | POST | 查询外部知识库 |
| `/external-kb/stats` | GET | 获取外部知识库使用统计 |
| `/external-kb/query-logs` | GET | 获取查询日志 |

### 向量数据库管理 (Vector Database)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/vector-db/providers` | GET | 获取所有向量数据库提供商 |
| `/vector-db/databases` | GET | 获取所有向量数据库 |
| `/vector-db/databases` | POST | 创建向量数据库 |
| `/vector-db/databases/<db_id>` | GET | 获取特定向量数据库详情 |
| `/vector-db/databases/<db_id>` | PUT | 更新向量数据库 |
| `/vector-db/databases/<db_id>` | DELETE | 删除向量数据库 |
| `/vector-db/databases/<db_id>/test` | POST | 测试向量数据库连接 |

### 许可证管理 (License)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/license` | GET | 获取当前许可证信息 |
| `/license/activate` | POST | 通过密钥激活许可证 |
| `/license/upload` | POST | 上传许可证文件 |
| `/license/validate` | POST | 验证许可证 |
| `/license/check-limit` | GET | 检查资源限制 |

### 工具模式缓存 (Tool Schema Cache)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/tool-schema-cache` | GET | 获取所有已缓存的服务器ID列表 |
| `/tool-schema-cache/<server_id>` | GET | 获取指定服务器的工具模式缓存 |
| `/tool-schema-cache/<server_id>` | DELETE | 移除指定服务器的工具模式缓存 |
| `/tool-schema-cache` | DELETE | 清空所有工具模式缓存 |
| `/tool-schema-cache/<server_id>/refresh` | POST | 刷新指定服务器的工具模式缓存 |
| `/tool-schema-cache/refresh-all` | POST | 刷新所有服务器的工具模式缓存 |

### MCP协议接口 (Model Context Protocol)

| 路径 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/mcp/variables` | POST | MCP变量服务端点，支持环境变量和智能体变量操作 |
| `/mcp/env-vars` | POST | MCP环境变量服务端点，支持任务环境变量操作 |
| `/mcp/agent-vars` | POST | MCP智能体变量服务端点，支持智能体变量操作 |

## 前端 API

前端主要通过以上后端REST API和WebSocket API进行数据交互。前端的主要功能模块包括：

### 页面路由

| 路径 | 组件 | 描述 |
| ---- | ---- | ---- |
| `/` | Dashboard | 系统仪表盘 |
| `/action-tasks` | ActionTaskList | 行动任务列表 |
| `/action-tasks/:id` | ActionTaskDetail | 行动任务详情 |
| `/action-tasks/:id/conversation/:conversationId` | ConversationView | 会话界面 |
| `/action-spaces` | ActionSpaceList | 行动空间列表 |
| `/action-spaces/:id` | ActionSpaceDetail | 行动空间详情 |
| `/roles` | RoleList | 角色列表 |
| `/roles/:id` | RoleDetail | 角色详情 |
| `/agents` | AgentList | 智能体列表 |
| `/agents/:id` | AgentDetail | 智能体详情 |
| `/model-configs` | ModelConfigList | 模型配置列表 |
| `/settings` | Settings | 系统设置 |
| `/logs` | LogViewer | 日志查看器 |
| `/statistics` | Statistics | 统计信息 |
| `/workspace-management` | WorkspaceManagement | 项目空间管理 |
| `/knowledgebase` | KnowledgeBase | 知识库管理 |
| `/knowledgebase/external` | ExternalIntegration | 外部知识库集成 |
| `/action-tasks/:id/conversations/:conversationId` | ConversationView | 会话界面 |
| `/agents` | AgentList | 智能体列表 |
| `/agents/:id` | AgentDetail | 智能体详情 |
| `/roles` | RoleList | 角色列表 |
| `/roles/:id` | RoleDetail | 角色详情 |
| `/action-spaces` | ActionSpaceList | 行动空间列表 |
| `/action-spaces/:id` | ActionSpaceDetail | 行动空间详情 |
| `/rules` | RuleList | 规则列表 |
| `/rules/:id` | RuleDetail | 规则详情 |
| `/model-configs` | ModelConfigList | 模型配置列表 |
| `/settings` | Settings | 系统设置 |
| `/logs` | LogViewer | 日志查看器 |
| `/workspace/browser` | PartitionWorkspaceTab | 工作空间浏览器 |
| `/roles/memories` | WorkspaceManagement | 分区记忆管理 |
| `/statistics` | Statistics | 统计信息 |

### 主要组件

| 组件名称 | 功能描述 |
| ---- | ---- |
| `ConversationView` | 会话界面，支持实时消息流 |
| `MessageList` | 消息列表组件 |
| `AgentSelector` | 智能体选择器 |
| `ModelConfigSelector` | 模型配置选择器 |
| `RuleEditor` | 规则编辑器 |
| `MemoryViewer` | 记忆文件查看器 |
| `StatisticsChart` | 统计图表组件 |
| `LogViewer` | 日志查看器 |

## 流式响应 API (Server-Sent Events)

系统支持流式响应，用于实时传输智能体消息和模型响应。流式响应通过Server-Sent Events (SSE)实现。

### 流式消息传输

| 端点 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/action-tasks/<task_id>/conversations/<conversation_id>/messages?stream=1` | POST | 发送消息并接收流式响应 |
| `/conversations/<conversation_id>/messages?stream=1` | POST | 在会话中发送消息并接收流式响应 |
| `/roles/<role_id>/test?stream=1` | POST | 测试角色并接收流式响应 |

### 流式响应格式

流式响应使用SSE格式，每个事件包含以下字段：

```
data: {"type": "content", "content": "消息内容片段"}

data: {"type": "meta", "agentId": "123", "agentName": "智能体名称", "status": "processing"}

data: {"type": "done", "message": "响应完成"}
```

### 连接管理

系统提供连接管理功能，支持取消正在进行的流式请求：

| 端点 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/cancel-stream/<request_id>` | POST | 取消指定的流式请求 |

## WebSocket API

系统还提供了一组WebSocket事件，用于支持实时通信功能，如流式消息传输和智能体对话。

### 连接事件

| 事件名称 | 方向 | 描述 |
| ------- | ---- | ---- |
| `connect` | 客户端→服务器 | 客户端连接到WebSocket服务 |
| `disconnect` | 客户端→服务器 | 客户端断开WebSocket连接 |

### 会话消息事件

| 事件名称 | 方向 | 描述 | 参数 |
| ------- | ---- | ---- | ---- |
| `conversation_message` | 客户端→服务器 | 发送会话消息 | `{conversation_id, content, model_config, message_history}` |
| `conversation_message_chunk` | 服务器→客户端 | 接收流式消息片段 | `{content, status}` |
| `conversation_message_complete` | 服务器→客户端 | 消息流完成 | `{success}` |
| `conversation_message_error` | 服务器→客户端 | 消息处理错误 | `{error}` |
| `stop_conversation_message` | 客户端→服务器 | 停止当前消息流 | 无参数 |

### 模型测试事件

| 事件名称 | 方向 | 描述 | 参数 |
| ------- | ---- | ---- | ---- |
| `model_test` | 客户端→服务器 | 测试模型API | `{model_config, content}` |
| `stop_model_test` | 客户端→服务器 | 停止模型测试 | 无参数 |

### 会话对话事件

| 事件名称 | 方向 | 描述 | 参数 |
| ------- | ---- | ---- | ---- |
| `start_conversation` | 客户端→服务器 | 启动智能体对话 | `{conversation_id, agent_ids, topic, num_turns, mode}` |
| `stop_conversation` | 客户端→服务器 | 停止智能体对话 | 无参数 |
| `pause_conversation` | 客户端→服务器 | 暂停智能体对话 | 无参数 |
| `resume_conversation` | 客户端→服务器 | 恢复智能体对话 | 无参数 |
| `conversation_status` | 服务器→客户端 | 会话状态更新 | `{status, current_agent, turn_count}` |

### 自主任务事件

| 事件名称 | 方向 | 描述 | 参数 |
| ------- | ---- | ---- | ---- |
| `start_autonomous_task` | 客户端→服务器 | 启动自主任务 | `{conversation_id, task_config}` |
| `stop_autonomous_task` | 客户端→服务器 | 停止自主任务 | `{task_id}` |
| `autonomous_task_status` | 服务器→客户端 | 自主任务状态更新 | `{task_id, status, progress}` |

### 系统事件

| 事件名称 | 方向 | 描述 | 参数 |
| ------- | ---- | ---- | ---- |
| `system_status` | 服务器→客户端 | 系统状态更新 | `{cpu_usage, memory_usage, active_tasks}` |
| `error_notification` | 服务器→客户端 | 错误通知 | `{error_type, message, details}` |
| `log_update` | 服务器→客户端 | 日志更新通知 | `{log_level, message, timestamp}` |

## 前后端API对接指南

### API请求配置

1. **API前缀**: 所有API都以`/api`为前缀
   ```javascript
   // 正确的请求URL示例
   const response = await fetch('/api/model-configs');

   // 使用axios时，确保路径中包含/api前缀
   const response = await axios.get('/api/model-configs');
   ```

2. **Content-Type**: 所有请求和响应均使用JSON格式
   ```javascript
   // 请求头设置
   headers: {
     'Content-Type': 'application/json'
   }
   ```

3. **认证**: 需要认证的API请求需要包含JWT令牌
   ```javascript
   // 在请求头中包含JWT令牌
   headers: {
     'Content-Type': 'application/json',
     'Authorization': `Bearer ${token}`
   }
   ```

4. **基础URL配置**: 建议在环境变量中配置API基础URL
   ```javascript
   // 环境变量配置
   const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api';

   // 使用配置
   const response = await fetch(`${API_BASE_URL}/action-tasks`);
   ```

### WebSocket连接

使用socket.io客户端建立WebSocket连接：

```javascript
// 引入配置变量（或从环境变量中获取）
// 注意：在实际代码中应该使用环境变量或配置文件中的值
const BACKEND_URL = process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:8080'; 

// 建立WebSocket连接
const socket = io(BACKEND_URL);

// 监听连接事件
socket.on('connect', () => {
  console.log('已连接到WebSocket服务');
});

// 监听消息片段
socket.on('conversation_message_chunk', (data) => {
  console.log('收到消息片段：', data.content);
  // 将片段添加到UI
});

// 监听消息完成
socket.on('conversation_message_complete', () => {
  console.log('消息流完成');
});

// 发送会话消息
socket.emit('conversation_message', {
  conversation_id: 123,
  content: '你好，请介绍一下自己',
  model_config: {
    model_id: 'gpt-3.5-turbo',
    provider: 'openai',
    api_key: 'sk-...'
  }
});
```

### 错误处理策略

#### 处理404错误

当API请求返回404错误时，可能有以下原因：

1. **API路径错误**: 请检查请求URL是否正确，特别是前缀和路径拼写
   ```javascript
   // 错误示例：缺少/api前缀
   fetch('/action-tasks')  // 错误
   
   // 正确示例
   fetch('/api/action-tasks')  // 正确
   ```

2. **后端未实现该API**: 对于未实现的API，前端应实现回退到模拟数据的机制
   ```javascript
   try {
     const response = await api.get('/api/action-tasks');
     return response.data;
   } catch (error) {
     if (error.response && error.response.status === 404) {
       console.warn('API未实现，使用模拟数据');
       return mockData;
     }
     throw error;
   }
   ```

3. **资源不存在**: 如请求不存在的ID
   ```javascript
   try {
     const response = await api.get(`/api/model-configs/${id}`);
     return response.data;
   } catch (error) {
     if (error.response && error.response.status === 404) {
       message.error('请求的资源不存在');
     }
     throw error;
   }
   ```

### 常见API响应状态码

| 状态码 | 描述 | 处理建议 |
|------|------|---------|
| 200 | 成功 | 正常处理返回数据 |
| 201 | 创建成功 | 资源创建成功，可获取新资源ID |
| 400 | 请求错误 | 检查请求参数是否完整/正确 |
| 401 | 未授权 | 用户未登录或token已过期，需要重新登录 |
| 403 | 禁止访问 | 用户无权访问该资源，检查权限设置 |
| 404 | 资源不存在 | 检查请求路径和资源ID |
| 422 | 数据验证失败 | 检查提交的数据格式和内容 |
| 429 | 请求过于频繁 | 实施请求限流，稍后重试 |
| 500 | 服务器错误 | 后端处理出错，可查看服务器日志 |
| 502 | 网关错误 | 服务器暂时不可用 |
| 503 | 服务不可用 | 服务器维护中或过载 |

### API响应格式规范

#### 成功响应格式
```javascript
// 单个资源
{
  "id": 1,
  "name": "示例资源",
  "created_at": "2023-12-01T10:00:00Z"
}

// 资源列表
{
  "items": [...],
  "total": 100,
  "page": 1,
  "per_page": 10
}

// 操作成功
{
  "success": true,
  "message": "操作成功",
  "data": {...}
}
```

#### 错误响应格式
```javascript
// 一般错误
{
  "error": "错误描述",
  "code": "ERROR_CODE",
  "details": {...}
}

// 验证错误
{
  "error": "数据验证失败",
  "validation_errors": {
    "field_name": ["错误信息1", "错误信息2"]
  }
}
```

### 分页请求规范

对于支持分页的接口，前端应传递以下参数：

```javascript
// 分页请求示例
const response = await api.get('/api/action-tasks', {
  params: {
    page: 1,        // 当前页码，从1开始
    per_page: 10,   // 每页记录数
    sort_by: 'created_at',  // 排序字段
    sort_dir: 'desc'        // 排序方向：asc或desc
  }
});

// 后端返回的分页数据结构
{
  "items": [...],  // 当前页的数据项
  "total": 100,    // 总记录数
  "page": 1,       // 当前页码
  "per_page": 10,  // 每页记录数
  "pages": 10      // 总页数
}
```

### 过滤和搜索规范

```javascript
// 过滤和搜索请求示例
const response = await api.get('/api/action-tasks', {
  params: {
    name: '搜索关键词',     // 名称搜索
    status: 'active',      // 状态过滤
    created_after: '2023-01-01',  // 创建时间过滤
    include_agents: 'true' // 包含关联数据
  }
});
```

### 批量操作规范

```javascript
// 批量删除示例
const response = await api.delete('/api/action-tasks/batch', {
  data: {
    ids: [1, 2, 3, 4, 5]
  }
});

// 批量更新示例
const response = await api.put('/api/agents/batch', {
  data: {
    updates: [
      { id: 1, status: 'active' },
      { id: 2, status: 'inactive' }
    ]
  }
});
```

## MCP工具列表

系统提供符合Model Context Protocol (MCP)规范的工具集，可以通过MCP客户端（如Claude、Cursor等）使用。

### 环境变量管理工具

| 工具名称 | 描述 | 参数 | 返回值 |
| ---- | ---- | ---- | ---- |
| `get_task_var` | 获取任务环境变量的值 | `{task_id, var_name}` | 变量值（字符串） |
| `set_task_var` | 设置任务环境变量的值 | `{task_id, var_name, var_value}` | 设置后的变量值 |
| `list_task_vars` | 列出任务的所有环境变量 | `{task_id}` | 变量名值对象 |

### 智能体变量管理工具

| 工具名称 | 描述 | 参数 | 返回值 |
| ---- | ---- | ---- | ---- |
| `get_agent_var` | 获取智能体变量的值 | `{agent_id, var_name}` | 变量值（字符串） |
| `set_agent_var` | 设置智能体变量的值 | `{agent_id, var_name, var_value}` | 设置后的变量值 |
| `list_agent_vars` | 列出智能体的所有变量 | `{agent_id}` | 变量名值对象 |

### MCP服务器配置

#### 连接信息
- **服务器地址**: `http://localhost:8080`
- **环境变量端点**: `/mcp/env-vars`
- **智能体变量端点**: `/mcp/agent-vars`
- **变量服务端点**: `/api/mcp/variables`

#### 使用示例
```json
// MCP工具调用示例
{
  "name": "get_task_var",
  "input": {
    "task_id": 1,
    "var_name": "current_status"
  }
}

// MCP工具响应示例
{
  "type": "tool_result",
  "tool_use_id": "call_123",
  "content": "active",
  "is_error": false
}
```

### 错误处理

MCP工具调用可能返回以下错误：

| 错误类型 | 描述 | 处理建议 |
| ---- | ---- | ---- |
| `TASK_NOT_FOUND` | 任务不存在 | 检查任务ID是否正确 |
| `AGENT_NOT_FOUND` | 智能体不存在 | 检查智能体ID是否正确 |
| `VARIABLE_NOT_FOUND` | 变量不存在 | 检查变量名是否正确 |
| `PERMISSION_DENIED` | 权限不足 | 检查访问权限设置 |
| `VALIDATION_ERROR` | 数据验证失败 | 检查输入参数格式 |

## API更新日志

### 最新更新 (2024-12-19)

#### 新增API模块
- **认证管理**: 用户登录、登出、令牌验证
- **日志管理**: 系统日志查看和尾部读取
- **统计信息**: 系统各模块统计数据
- **记忆管理**: 行动任务记忆文件管理
- **许可证管理**: 许可证激活和验证
- **外部变量管理**: 外部API数据同步
- **工具模式缓存**: MCP工具模式缓存管理

#### 增强的API功能
- **行动任务**: 添加启动、停止、暂停、恢复等状态控制
- **会话管理**: 添加自主任务管理功能
- **智能体管理**: 添加状态控制和测试功能
- **角色管理**: 添加预定义角色、最近使用、测试等功能
- **规则管理**: 添加规则测试功能
- **系统设置**: 添加提示词模板管理

#### WebSocket事件扩展
- 添加自主任务事件
- 添加系统状态事件
- 增强会话对话事件

#### MCP协议支持
- 完整的环境变量和智能体变量管理工具
- 标准化的MCP工具调用格式
- 详细的错误处理机制

## 开发指南

### 前端开发建议

1. **状态管理**: 使用Redux或Zustand管理全局状态
2. **API封装**: 创建统一的API客户端，处理认证和错误
3. **实时更新**: 利用WebSocket实现实时数据更新
4. **错误处理**: 实现统一的错误处理和用户提示机制
5. **缓存策略**: 合理使用缓存减少不必要的API请求

### 后端开发建议

1. **API版本控制**: 为重大更新预留版本控制机制
2. **数据验证**: 严格验证所有输入数据
3. **权限控制**: 实现细粒度的权限控制
4. **日志记录**: 记录所有重要操作和错误
5. **性能优化**: 优化数据库查询和API响应时间

### 测试建议

1. **单元测试**: 为所有API端点编写单元测试
2. **集成测试**: 测试前后端集成功能
3. **性能测试**: 测试高并发场景下的系统性能
4. **安全测试**: 验证认证和权限控制机制

---

**文档版本**: v2.0
**最后更新**: 2024-12-19
**维护者**: 开发团队