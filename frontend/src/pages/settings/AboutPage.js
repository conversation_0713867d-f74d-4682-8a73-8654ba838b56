import React, { useState, useEffect } from 'react';
import { Card, Typography, Space, Divider, Tag, Spin, Alert, Button, Form, Input, Tabs, Upload, Modal, message } from 'antd';
import { TeamOutlined, SafetyCertificateOutlined, KeyOutlined, UploadOutlined, CopyOutlined } from '@ant-design/icons';
import { licenseAPI } from '../../services/api/license';

// 功能名称映射
const featureNameMap = {
  'basic_agents': '基础智能体',
  'basic_roles': '基础角色',
  'basic_action_spaces': '基础行动空间',
  'advanced_agents': '高级智能体',
  'advanced_roles': '高级角色',
  'knowledge_base': '知识库',
  'custom_tools': '自定义工具',
  'advanced_analytics': '高级分析',
  'unlimited_memory': '无限记忆'
};

// 获取功能的可读名称
const getFeatureName = (featureCode) => {
  return featureNameMap[featureCode] || featureCode;
};

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * 关于页面组件
 * 显示系统的基本信息、版本和版权声明
 */
const AboutPage = () => {
  const [form] = Form.useForm();
  const [license, setLicense] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activationModalVisible, setActivationModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('key'); // 'key' 或 'file'
  const [fileList, setFileList] = useState([]);
  const [activating, setActivating] = useState(false);
  const [fileUploading, setFileUploading] = useState(false);
  const [activationError, setActivationError] = useState(null);
  const [systemKey, setSystemKey] = useState('');
  const [loadingSystemKey, setLoadingSystemKey] = useState(false);
  const [showFullLicenseKey, setShowFullLicenseKey] = useState(false);

  // 获取许可证信息
  const fetchLicense = async () => {
    setLoading(true);
    setError(null);
    try {
      const licenseData = await licenseAPI.getCurrentLicense();
      setLicense(licenseData);
    } catch (error) {
      console.error('获取许可证信息失败:', error);

      // 检查是否是许可证过期错误
      if (error.response?.data?.code === 'LICENSE_EXPIRED') {
        // 尝试获取过期的许可证信息
        try {
          const expiredLicense = await licenseAPI.getExpiredLicense();
          if (expiredLicense) {
            setLicense(expiredLicense);
            setError('许可证已过期，请重新激活');
          } else {
            setError('无法获取许可证信息，请检查系统是否已激活');
          }
        } catch (innerError) {
          console.error('获取过期许可证信息也失败:', innerError);
          if (innerError.isLicenseError && innerError.code === 'LICENSE_NOT_FOUND') {
            setError('系统未激活，请激活许可证以使用所有功能');
          } else {
            setError('无法获取许可证信息，请检查系统是否已激活');
          }
        }
      } else if (error.response?.status === 404) {
        // 直接的404错误，说明许可证API不存在或系统未激活
        setError('系统未激活，请激活许可证以使用所有功能');
      } else {
        setError('无法获取许可证信息，请检查系统是否已激活');
      }
    } finally {
      setLoading(false);
    }
  };

  // 获取系统密钥
  const fetchSystemKey = async () => {
    setLoadingSystemKey(true);
    try {
      const key = await licenseAPI.getSystemKey();
      setSystemKey(key);
    } catch (error) {
      console.error('获取系统密钥失败:', error);
      message.error('获取系统密钥失败，请检查系统配置');
      setSystemKey('无法获取系统密钥');
    } finally {
      setLoadingSystemKey(false);
    }
  };

  useEffect(() => {
    fetchLicense();
  }, []);

  // 当激活对话框打开时获取系统密钥
  useEffect(() => {
    if (activationModalVisible) {
      fetchSystemKey();
    }
  }, [activationModalVisible]);

  // 显示激活对话框
  const showActivationModal = () => {
    setActivationModalVisible(true);
    setActivationError(null);
    form.resetFields();
    setFileList([]);
  };

  // 关闭激活对话框
  const closeActivationModal = () => {
    setActivationModalVisible(false);
  };

  // 通过密钥激活
  const handleActivate = async (values) => {
    setActivating(true);
    setActivationError(null);

    try {
      await licenseAPI.activateLicense(values.licenseKey);
      message.success('许可证激活成功');
      closeActivationModal();
      // 重新获取许可证信息
      fetchLicense();
    } catch (error) {
      setActivationError(error.response?.data?.message || '激活失败，请检查许可证密钥是否正确');
    } finally {
      setActivating(false);
    }
  };

  // 通过文件激活
  const handleFileActivate = async () => {
    if (fileList.length === 0) {
      message.error('请先上传许可证文件');
      return;
    }

    setFileUploading(true);
    setActivationError(null);

    try {
      const file = fileList[0].originFileObj;
      await licenseAPI.activateLicenseFile(file);
      message.success('许可证激活成功');
      closeActivationModal();
      // 重新获取许可证信息
      fetchLicense();
    } catch (error) {
      setActivationError(error.response?.data?.message || '激活失败，请检查许可证文件是否有效');
    } finally {
      setFileUploading(false);
    }
  };

  // 文件上传前检查
  const beforeUpload = (file) => {
    const isJSON = file.type === 'application/json' || file.name.endsWith('.json');
    if (!isJSON) {
      message.error('只能上传JSON格式的许可证文件!');
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('许可证文件必须小于2MB!');
    }

    return isJSON && isLt2M;
  };

  // 文件上传状态变化
  const handleFileChange = (info) => {
    let fileList = [...info.fileList];

    // 限制只能上传一个文件
    fileList = fileList.slice(-1);

    // 更新文件状态
    fileList = fileList.map(file => {
      if (file.response) {
        file.url = file.response.url;
      }
      return file;
    });

    setFileList(fileList);
  };

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>关于系统</Title>
            <Text type="secondary">
              系统信息、版本和版权声明
            </Text>
          </div>
        </div>
      </div>

      <Card
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
          border: 'none'
        }}
      >
        <div style={{ padding: '20px 0 40px' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
              <TeamOutlined style={{ fontSize: '48px', color: '#1677ff' }} />
              <div>
                <Title level={2} style={{ marginBottom: '8px', color: '#1677ff' }}>
                  多智能体专家决策与执行系统
                </Title>
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <Title level={4} style={{ fontWeight: 'normal', margin: '0' }}>
                    版本: v0.8
                  </Title>
                </div>
              </div>
            </div>

            <Divider />

            <div>
              <Paragraph>
                <Text strong style={{ fontSize: '16px' }}>系统简介</Text>
              </Paragraph>
              <Paragraph>
                多智能体专家决策与执行系统是一个基于大型语言模型的智能决策平台，
                支持多智能体协作、角色扮演和任务执行，为企业提供智能化决策支持。
              </Paragraph>
            </div>

            <Divider />

            <div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                <SafetyCertificateOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
                <Text strong style={{ fontSize: '16px' }}>许可证信息</Text>
              </div>

              {loading ? (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <Spin tip="加载许可证信息..." />
                </div>
              ) : error && !license ? (
                <div>
                  <Alert
                    message="许可证信息获取失败"
                    description={error}
                    type="error"
                    showIcon
                    style={{ marginBottom: '16px' }}
                  />
                  <Button
                    type="primary"
                    icon={<SafetyCertificateOutlined />}
                    onClick={showActivationModal}
                  >
                    激活许可证
                  </Button>
                </div>
              ) : license ? (
                <>
                  {error && (
                    <Alert
                      message="许可证已过期"
                      description="您的许可证已过期，请重新激活以继续使用所有功能"
                      type="warning"
                      showIcon
                      style={{ marginBottom: '16px' }}
                    />
                  )}

                  <Paragraph>
                    <Text>许可证类型: </Text>
                    <Tag color={license.license_type === 'enterprise' ? '#f50' : license.license_type === 'professional' ? '#108ee9' : '#87d068'}>
                      {license.license_name || '未知类型'}
                    </Tag>
                    {license.is_expired && (
                      <Tag color="red" style={{ marginLeft: '8px' }}>已过期</Tag>
                    )}
                  </Paragraph>
                  <Paragraph>
                    <Text>授权单位: {license.customer_name || '上海同悦信息科技有限公司'}</Text>
                  </Paragraph>
                  <Paragraph>
                    <Text>授权期限: </Text>
                    {license.expiry_date ? (
                      <>
                        <span>{new Date(license.expiry_date).toLocaleString()}</span>
                        {license.is_expired && (
                          <Tag color="red" style={{ marginLeft: '8px' }}>已过期</Tag>
                        )}
                      </>
                    ) : (
                      '永久授权'
                    )}
                  </Paragraph>
                  <Paragraph>
                    <Text>激活日期: {license.activation_date ? new Date(license.activation_date).toLocaleString() : '未知'}</Text>
                  </Paragraph>
                  <Paragraph>
                    <Text>许可证密钥: </Text>
                    <span style={{ fontFamily: 'monospace' }}>
                      {license.license_key
                        ? (showFullLicenseKey
                            ? license.license_key
                            : license.license_key.substring(0, 8) + '...')
                        : '未知'}
                    </span>
                    {license.license_key && (
                      <>
                        <Button
                          type="link"
                          size="small"
                          onClick={() => setShowFullLicenseKey(!showFullLicenseKey)}
                          style={{ marginLeft: '8px' }}
                        >
                          {showFullLicenseKey ? '隐藏' : '显示完整密钥'}
                        </Button>
                        {showFullLicenseKey && (
                          <Button
                            type="link"
                            size="small"
                            icon={<CopyOutlined />}
                            onClick={() => {
                              navigator.clipboard.writeText(license.license_key);
                              message.success('许可证密钥已复制到剪贴板');
                            }}
                          >
                            复制
                          </Button>
                        )}
                      </>
                    )}
                  </Paragraph>
                  <Paragraph>
                    <Text>包含功能: </Text>
                    {license.features && license.features.length > 0 ? (
                      <div style={{ margin: '8px 0 0 0' }}>
                        {license.features.map((feature, index) => (
                          <Tag key={index} color="blue" style={{ margin: '0 8px 8px 0' }}>
                            {getFeatureName(feature)}
                          </Tag>
                        ))}
                      </div>
                    ) : (
                      <Text type="secondary">无可用功能</Text>
                    )}
                  </Paragraph>

                  <Paragraph>
                    <Text>资源限制: </Text>
                    <ul style={{ margin: '8px 0 0 20px', padding: 0 }}>
                      <li>最大智能体数量: {license.max_agents || '无限制'}</li>
                      <li>最大行动空间数量: {license.max_action_spaces || '无限制'}</li>
                      <li>最大角色数量: {license.max_roles || '无限制'}</li>
                    </ul>
                  </Paragraph>

                  <div style={{ marginTop: '16px' }}>
                    <Button
                      type="primary"
                      icon={<SafetyCertificateOutlined />}
                      onClick={showActivationModal}
                      danger={license.is_expired}
                    >
                      {license.is_expired ? '重新激活许可证' : '更新许可证'}
                    </Button>
                  </div>
                </>
              ) : (
                <div>
                  <Alert
                    message="未激活"
                    description="系统未激活或许可证无效，请激活系统以解锁所有功能"
                    type="warning"
                    showIcon
                    style={{ marginBottom: '16px' }}
                  />
                  <Button
                    type="primary"
                    icon={<SafetyCertificateOutlined />}
                    onClick={showActivationModal}
                  >
                    激活许可证
                  </Button>
                </div>
              )}
            </div>

            <Divider />

            <div>
              <Text strong style={{ fontSize: '16px', display: 'block', marginBottom: '8px' }}>上海同悦信息科技有限公司</Text>
              <Text type="secondary">© 2025 Copyright. All Rights Reserved.</Text>
            </div>
          </Space>
        </div>
      </Card>

      {/* 许可证激活对话框 */}
      <Modal
        title={license ? "重新激活许可证" : "激活许可证"}
        open={activationModalVisible}
        onCancel={closeActivationModal}
        footer={null}
        width={700}
      >
        <div style={{ padding: '20px 0' }}>
          {activationError && (
            <Alert
              message="激活失败"
              description={activationError}
              type="error"
              showIcon
              style={{ marginBottom: '24px' }}
            />
          )}

          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            centered
            type="card"
          >
            <TabPane
              tab={
                <span>
                  <KeyOutlined />
                  通过密钥激活
                </span>
              }
              key="key"
            >
              <div style={{ padding: '20px 0' }}>
                <Paragraph style={{ marginBottom: '20px' }}>
                  请输入您的许可证密钥进行激活。许可证密钥是一个32位的字符串，由系统管理员提供。
                </Paragraph>

                <Alert
                  message="系统密钥信息"
                  description={
                    <div>
                      <p>请将以下系统密钥提供给厂商，用于生成与系统匹配的许可证：</p>
                      {loadingSystemKey ? (
                        <div style={{ textAlign: 'center', padding: '10px 0' }}>
                          <Spin size="small" tip="加载系统密钥..." />
                        </div>
                      ) : (
                        <div style={{
                          background: '#f5f5f5',
                          padding: '10px',
                          borderRadius: '4px',
                          fontFamily: 'monospace',
                          marginBottom: '10px',
                          position: 'relative'
                        }}>
                          {systemKey}
                          <Button
                            type="text"
                            size="small"
                            icon={<CopyOutlined />}
                            style={{ position: 'absolute', right: '5px', top: '5px' }}
                            onClick={() => {
                              navigator.clipboard.writeText(systemKey);
                              message.success('系统密钥已复制到剪贴板');
                            }}
                          />
                        </div>
                      )}
                    </div>
                  }
                  type="info"
                  showIcon
                  style={{ marginBottom: '20px' }}
                />

                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleActivate}
                >
                  <Form.Item
                    name="licenseKey"
                    label="许可证密钥"
                    rules={[
                      { required: true, message: '请输入许可证密钥' },
                      { min: 16, message: '许可证密钥长度不正确' }
                    ]}
                  >
                    <Input.Password
                      prefix={<SafetyCertificateOutlined />}
                      placeholder="请输入您的许可证密钥"
                      size="large"
                    />
                  </Form.Item>

                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={activating}
                      size="large"
                      block
                    >
                      {license ? "重新激活许可证" : "激活许可证"}
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <UploadOutlined />
                  通过文件激活
                </span>
              }
              key="file"
            >
              <div style={{ padding: '20px 0' }}>
                <Paragraph style={{ marginBottom: '20px' }}>
                  请上传您的许可证文件进行激活。许可证文件是一个JSON格式的文件，由系统管理员提供。
                </Paragraph>

                <Alert
                  message="系统密钥信息"
                  description={
                    <div>
                      <p>请将以下系统密钥提供给厂商，用于生成与系统匹配的许可证：</p>
                      {loadingSystemKey ? (
                        <div style={{ textAlign: 'center', padding: '10px 0' }}>
                          <Spin size="small" tip="加载系统密钥..." />
                        </div>
                      ) : (
                        <div style={{
                          background: '#f5f5f5',
                          padding: '10px',
                          borderRadius: '4px',
                          fontFamily: 'monospace',
                          marginBottom: '10px',
                          position: 'relative'
                        }}>
                          {systemKey}
                          <Button
                            type="text"
                            size="small"
                            icon={<CopyOutlined />}
                            style={{ position: 'absolute', right: '5px', top: '5px' }}
                            onClick={() => {
                              navigator.clipboard.writeText(systemKey);
                              message.success('系统密钥已复制到剪贴板');
                            }}
                          />
                        </div>
                      )}
                    </div>
                  }
                  type="info"
                  showIcon
                  style={{ marginBottom: '20px' }}
                />

                <div style={{ marginBottom: '24px' }}>
                  <Upload
                    name="license"
                    listType="text"
                    fileList={fileList}
                    beforeUpload={beforeUpload}
                    onChange={handleFileChange}
                    customRequest={({ onSuccess }) => {
                      setTimeout(() => {
                        onSuccess("ok");
                      }, 0);
                    }}
                  >
                    <Button icon={<UploadOutlined />} size="large">
                      选择许可证文件
                    </Button>
                  </Upload>
                </div>

                <Button
                  type="primary"
                  onClick={handleFileActivate}
                  loading={fileUploading}
                  disabled={fileList.length === 0}
                  size="large"
                  block
                >
                  {license ? "上传并重新激活" : "上传并激活"}
                </Button>
              </div>
            </TabPane>
          </Tabs>

          <Divider />

          <div style={{ textAlign: 'center' }}>
            <Text type="secondary">
              如果您没有许可证密钥或文件，请联系上海同悦信息科技有限公司获取
            </Text>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AboutPage;
