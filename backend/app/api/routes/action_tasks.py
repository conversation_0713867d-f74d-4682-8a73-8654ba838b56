"""
行动任务API路由

处理与行动任务相关的所有API请求
"""
from flask import Blueprint, request, jsonify
from app.models import ActionTask, ActionTaskAgent, Agent, ActionSpace, RuleSet, Rule, RuleSetRule, db, Role, Message, ActionTaskEnvironmentVariable, ActionSpaceEnvironmentVariable, AgentVariable, RoleVariable, ActionSpaceRole, Conversation, ConversationAgent, ActionSpaceObserver, AutonomousTask, RuleTriggerLog
from app.services.action_task_service import ActionTaskService
from datetime import datetime
from app.services.agent_variable_service import AgentVariableService
from app.services.workspace_service import workspace_service

# 创建Blueprint
action_task_bp = Blueprint('action_task_api', __name__)

# 创建服务实例
action_task_service = ActionTaskService(agents=[], initial_message="欢迎使用多智能体行动任务系统")

@action_task_bp.route('/action-tasks', methods=['GET'])
def get_action_tasks():
    """获取所有行动任务列表"""
    include_agents = request.args.get('include_agents', 'false').lower() == 'true'

    action_tasks = ActionTask.query.all()
    result = []

    for task in action_tasks:
        # 获取任务的智能体数量
        agent_count = ActionTaskAgent.query.filter_by(action_task_id=task.id).count()

        # 获取任务的消息数量
        message_count = Message.query.filter_by(action_task_id=task.id).count()

        # 获取任务的会话数量
        conversation_count = Conversation.query.filter_by(action_task_id=task.id).count()

        # 获取任务的自主行动数量
        # 通过任务的所有会话来统计自主行动任务总数和活动数量
        total_autonomous_task_count = 0
        active_autonomous_task_count = 0
        conversations = Conversation.query.filter_by(action_task_id=task.id).all()
        for conversation in conversations:
            # 统计总数
            total_autonomous_task_count += AutonomousTask.query.filter_by(conversation_id=conversation.id).count()
            # 统计活动状态的数量
            active_autonomous_task_count += AutonomousTask.query.filter_by(
                conversation_id=conversation.id,
                status='active'
            ).count()

        # 获取行动空间信息
        action_space = None
        if task.action_space_id:
            action_space = ActionSpace.query.get(task.action_space_id)

        task_data = {
            'id': task.id,
            'title': task.title,
            'description': task.description,
            'status': task.status,
            'mode': task.mode,
            'created_at': task.created_at.isoformat() if task.created_at else None,
            'updated_at': task.updated_at.isoformat() if task.updated_at else None,
            'agent_count': agent_count,
            'message_count': message_count,
            'conversation_count': conversation_count,
            'autonomous_task_count': total_autonomous_task_count,  # 保持向后兼容
            'total_autonomous_task_count': total_autonomous_task_count,
            'active_autonomous_task_count': active_autonomous_task_count,
            'action_space_id': task.action_space_id,
            'action_space_name': action_space.name if action_space else None
        }

        # 如果需要包含智能体信息
        if include_agents:
            task_agents = ActionTaskAgent.query.filter_by(action_task_id=task.id).all()
            agents = []
            for ta in task_agents:
                agent = Agent.query.get(ta.agent_id)
                if agent:
                    agents.append({
                        'id': agent.id,
                        'name': agent.name,
                        'description': agent.description,
                        'avatar': agent.avatar,
                        'is_default': ta.is_default,
                        'is_observer': agent.is_observer,
                        'type': agent.type
                    })
            task_data['agents'] = agents

        result.append(task_data)

    return jsonify({'action_tasks': result})

@action_task_bp.route('/action-tasks/<int:task_id>', methods=['GET'])
def get_action_task(task_id):
    """获取特定行动任务详情"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    # 获取行动任务的智能体
    task_agents = ActionTaskAgent.query.filter_by(action_task_id=task_id).all()
    agents = []

    for ta in task_agents:
        agent = Agent.query.get(ta.agent_id)
        if agent:
            # 获取智能体的角色信息
            agent_role = None
            if hasattr(agent, 'role_id') and agent.role_id:
                agent_role = Role.query.get(agent.role_id)

            # 构建包含角色名称的智能体名称
            role_name = agent_role.name if agent_role else "智能助手"

            agents.append({
                'id': agent.id,
                'name': agent.name,
                'role_name': role_name,
                'description': agent.description,
                'avatar': agent.avatar,
                'is_default': ta.is_default,
                'is_observer': agent.is_observer,  # 添加是否为监督者标记
                'type': agent.type  # 添加智能体类型
            })

    # 获取行动空间信息
    action_space = ActionSpace.query.get(task.action_space_id) if task.action_space_id else None
    rule_set = RuleSet.query.get(task.rule_set_id) if task.rule_set_id else None

    result = {
        'id': task.id,
        'title': task.title,
        'description': task.description,
        'status': task.status,
        'mode': task.mode,
        'created_at': task.created_at.isoformat() if task.created_at else None,
        'updated_at': task.updated_at.isoformat() if task.updated_at else None,
        'agents': agents,
        'action_space': {
            'id': action_space.id,
            'name': action_space.name,
            'description': action_space.description
        } if action_space else None,
        'rule_set': {
            'id': rule_set.id,
            'name': rule_set.name,
            'description': rule_set.description
        } if rule_set else None
    }

    return jsonify(result)

@action_task_bp.route('/action-tasks', methods=['POST'])
def create_action_task():
    """创建新行动任务"""
    data = request.get_json()

    # 验证必填字段
    if not data.get('title'):
        return jsonify({'error': '缺少必填字段: title'}), 400

    # 创建新行动任务
    new_task = ActionTask(
        title=data.get('title'),
        description=data.get('description'),
        mode=data.get('mode', 'sequential'),
        status='active',
        rule_set_id=data.get('rule_set_id'),
        action_space_id=data.get('action_space_id'),
        user_id=data.get('user_id')
    )

    # 保存行动任务
    db.session.add(new_task)
    db.session.flush()  # 获取ID但不提交事务

    # 如果有关联的行动空间，从行动空间初始化环境变量
    if new_task.action_space_id:
        action_space = ActionSpace.query.get(new_task.action_space_id)
        if action_space:
            try:
                # 检查请求中是否已经包含了环境变量数据
                has_space_vars_in_request = False
                if data.get('environment_variables'):
                    # 查看是否有标记为space来源的环境变量
                    for var_data in data.get('environment_variables'):
                        if var_data.get('source') == 'space':
                            has_space_vars_in_request = True
                            break

                # 只有在请求中没有行动空间环境变量时才从行动空间获取
                if not has_space_vars_in_request:
                    # 从行动空间获取环境变量定义
                    space_env_vars = ActionSpaceEnvironmentVariable.query.filter_by(
                        action_space_id=new_task.action_space_id
                    ).all()

                    # 为每个行动空间环境变量创建对应的任务环境变量
                    for space_var in space_env_vars:
                        env_var = ActionTaskEnvironmentVariable(
                            name=space_var.name,
                            label=space_var.label,
                            value=space_var.default_value,
                            type=space_var.type,
                            action_task_id=new_task.id
                        )
                        db.session.add(env_var)

                # 如果请求中包含环境变量，创建它们
                if data.get('environment_variables'):
                    for var_data in data.get('environment_variables'):
                        env_var = ActionTaskEnvironmentVariable(
                            name=var_data.get('name'),
                            label=var_data.get('label', var_data.get('name', '').replace('_', ' ').title()),
                            value=var_data.get('value'),
                            type=var_data.get('type', 'text'),
                            action_task_id=new_task.id
                        )
                        db.session.add(env_var)

                # 环境变量初始化部分不再创建监督者智能体，移到单独的部分
            except Exception as e:
                print(f"初始化环境变量或监督者失败: {str(e)}")
                # 继续处理，不中断任务创建

    # 从行动空间角色创建参与智能体
    if new_task.action_space_id:
        action_space = ActionSpace.query.get(new_task.action_space_id)
        if action_space:
            try:
                # 获取行动空间中的所有普通角色
                space_roles = ActionSpaceRole.query.filter_by(
                    action_space_id=new_task.action_space_id
                ).all()

                # 为每个普通角色创建智能体实例
                participant_count = 0
                for space_role in space_roles:
                    role = Role.query.get(space_role.role_id)
                    if role:
                        # 创建智能体实例
                        agent = Agent(
                            name=role.name,
                            description=role.description,
                            avatar=role.avatar,
                            settings=role.settings,
                            action_task_id=new_task.id,
                            role_id=role.id,
                            type='agent',  # 标记为普通智能体类型
                            is_observer=False,  # 标记为非监督者
                            additional_prompt=space_role.additional_prompt  # 设置额外提示词
                        )
                        db.session.add(agent)
                        db.session.flush()  # 获取agent.id

                        participant_count += 1

                        # 第一个参与者智能体设为默认
                        is_default = (participant_count == 1)

                        # 创建行动任务-智能体关联
                        task_agent = ActionTaskAgent(
                            action_task_id=new_task.id,
                            agent_id=agent.id,
                            is_default=is_default
                        )
                        db.session.add(task_agent)

                        # 查找该角色在当前行动空间的环境变量
                        role_vars = RoleVariable.query.filter_by(
                            role_id=role.id,
                            action_space_id=new_task.action_space_id
                        ).all()

                        # 为智能体创建这些环境变量
                        for role_var in role_vars:
                            try:
                                # 将角色变量转换为智能体变量
                                AgentVariableService.create_variable(
                                    agent_id=agent.id,
                                    name=role_var.name,
                                    value=role_var.default_value,
                                    type=role_var.type,
                                    is_public=True,
                                    label=role_var.label
                                )
                                print(f"已为智能体 {agent.id} 创建环境变量: {role_var.name}")
                            except Exception as e:
                                print(f"为智能体 {agent.id} 创建环境变量 {role_var.name} 失败: {str(e)}")

                print(f"已从行动空间角色创建参与智能体")
            except Exception as e:
                print(f"从行动空间角色创建参与智能体失败: {str(e)}")
                # 继续处理，不中断任务创建

            # 从行动空间监督者角色创建监督者智能体
            try:
                if data.get('include_observers', True):  # 默认包含监督者
                    # 获取行动空间中的所有监督者角色
                    space_observers = ActionSpaceObserver.query.filter_by(
                        action_space_id=new_task.action_space_id
                    ).all()

                    # 为每个监督者角色创建智能体实例
                    observer_count = 0
                    for space_observer in space_observers:
                        role = Role.query.get(space_observer.role_id)
                        if role:
                            # 创建监督者智能体实例
                            observer_agent = Agent(
                                name=role.name,
                                description=role.description,
                                avatar=role.avatar,
                                settings=role.settings,
                                action_task_id=new_task.id,
                                role_id=role.id,
                                type='observer',  # 标记为监督者类型
                                is_observer=True,  # 标记为监督者
                                additional_prompt=space_observer.additional_prompt  # 设置额外提示词
                            )
                            db.session.add(observer_agent)
                            db.session.flush()  # 获取observer_agent.id

                            observer_count += 1

                            # 创建行动任务-智能体关联（监督者不设为默认智能体）
                            task_agent = ActionTaskAgent(
                                action_task_id=new_task.id,
                                agent_id=observer_agent.id,
                                is_default=False
                            )
                            db.session.add(task_agent)

                            # 查找该角色在当前行动空间的环境变量
                            role_vars = RoleVariable.query.filter_by(
                                role_id=role.id,
                                action_space_id=new_task.action_space_id
                            ).all()

                            # 为监督者智能体创建这些环境变量
                            for role_var in role_vars:
                                try:
                                    # 将角色变量转换为智能体变量
                                    AgentVariableService.create_variable(
                                        agent_id=observer_agent.id,
                                        name=role_var.name,
                                        value=role_var.default_value,
                                        type=role_var.type,
                                        is_public=True,
                                        label=role_var.label
                                    )
                                    print(f"已为监督者智能体 {observer_agent.id} 创建环境变量: {role_var.name}")
                                except Exception as e:
                                    print(f"为监督者智能体 {observer_agent.id} 创建环境变量 {role_var.name} 失败: {str(e)}")

                    print(f"已从行动空间监督者角色创建监督者智能体，共 {observer_count} 个")
            except Exception as e:
                print(f"从行动空间监督者角色创建监督者智能体失败: {str(e)}")
                # 继续处理，不中断任务创建

    # 兼容旧版本：如果提供了agent_ids，也添加这些智能体
    if data.get('agent_ids'):
        for idx, agent_id in enumerate(data.get('agent_ids')):
            # 获取智能体并设置 action_task_id
            agent = Agent.query.get(agent_id)
            if agent:
                agent.action_task_id = new_task.id

                # 获取行动空间中角色的额外提示词
                if agent.role_id and new_task.action_space_id:
                    action_space_role = ActionSpaceRole.query.filter_by(
                        action_space_id=new_task.action_space_id,
                        role_id=agent.role_id
                    ).first()

                    if action_space_role and action_space_role.additional_prompt:
                        agent.additional_prompt = action_space_role.additional_prompt

                # 获取智能体对应的角色
                if agent.role_id and new_task.action_space_id:
                    # 查找该角色在当前行动空间的环境变量
                    role_vars = RoleVariable.query.filter_by(
                        role_id=agent.role_id,
                        action_space_id=new_task.action_space_id
                    ).all()

                    # 为智能体创建这些环境变量
                    for role_var in role_vars:
                        try:
                            # 将角色变量转换为智能体变量
                            AgentVariableService.create_variable(
                                agent_id=agent.id,
                                name=role_var.name,
                                value=role_var.default_value,
                                type=role_var.type,
                                is_public=True,
                                label=role_var.label
                            )
                            print(f"已为智能体 {agent.id} 创建环境变量: {role_var.name}")
                        except Exception as e:
                            print(f"为智能体 {agent.id} 创建环境变量 {role_var.name} 失败: {str(e)}")

                # 检查是否已经有关联
                existing = ActionTaskAgent.query.filter_by(
                    action_task_id=new_task.id,
                    agent_id=agent_id
                ).first()

                if not existing:
                    # 创建行动任务-智能体关联
                    task_agent = ActionTaskAgent(
                        action_task_id=new_task.id,
                        agent_id=agent_id,
                        is_default=False  # 从行动空间角色创建的智能体已经设置了默认
                    )
                    db.session.add(task_agent)

        db.session.commit()

    # 创建默认会话
    from app.services.conversation_service import ConversationService
    default_conversation = ConversationService.create_conversation_for_action_task(new_task)

    # 获取所有智能体ID和信息
    task_agents = ActionTaskAgent.query.filter_by(action_task_id=new_task.id).all()
    agent_ids = []
    agent_info = []

    for ta in task_agents:
        agent = Agent.query.get(ta.agent_id)
        if agent:
            agent_ids.append(agent.id)

            # 获取角色信息
            role_name = None
            if hasattr(agent, 'role_id') and agent.role_id:
                role = Role.query.get(agent.role_id)
                if role:
                    role_name = role.name

            # 添加智能体信息
            agent_info.append({
                'id': agent.id,
                'name': agent.name,
                'role_name': role_name
            })

    # 初始化项目空间文件结构
    try:
        workspace_service.initialize_workspace_for_action_task(
            task_id=new_task.id,
            agent_ids=agent_ids,
            task_title=new_task.title,
            agent_info=agent_info
        )
        print(f"已为行动任务 {new_task.id} 初始化项目空间文件结构")
    except Exception as e:
        print(f"初始化项目空间文件结构失败: {str(e)}")
        # 继续处理，不中断任务创建

    # 使用任务详情的API获取结果
    task_detail = get_action_task(new_task.id)
    return task_detail

@action_task_bp.route('/action-tasks/<int:task_id>', methods=['PUT'])
def update_action_task(task_id):
    """更新行动任务信息"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    data = request.get_json()

    # 更新基本信息
    if 'title' in data:
        task.title = data['title']
    if 'description' in data:
        task.description = data['description']
    if 'mode' in data:
        task.mode = data['mode']
    if 'status' in data:
        task.status = data['status']
    if 'rule_set_id' in data:
        task.rule_set_id = data['rule_set_id']
    if 'action_space_id' in data:
        task.action_space_id = data['action_space_id']

    db.session.commit()

    return jsonify({
        'id': task.id,
        'title': task.title,
        'message': '行动任务更新成功'
    })

@action_task_bp.route('/action-tasks/<int:task_id>', methods=['DELETE'])
def delete_action_task(task_id):
    """删除行动任务"""
    from app.models import Conversation, ConversationAgent, Message, Agent, AutonomousTask, AutonomousTaskExecution

    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    # 获取级联删除参数
    cascade = request.args.get('cascade', 'false').lower() == 'true'
    # 获取强制清理参数
    force_cleanup = request.args.get('force_cleanup', 'false').lower() == 'true'

    try:
        if cascade:
            print(f"开始级联删除行动任务 {task_id}，强制清理: {force_cleanup}")
            # 1. 停止并删除与任务关联的自主行动
            conversations = Conversation.query.filter_by(action_task_id=task_id).all()
            stopped_autonomous_tasks = 0
            for conversation in conversations:
                # 获取该会话中的所有自主任务
                autonomous_tasks = AutonomousTask.query.filter_by(conversation_id=conversation.id).all()
                for autonomous_task in autonomous_tasks:
                    # 如果自主任务正在运行，尝试停止它
                    if autonomous_task.status == 'active':
                        try:
                            if autonomous_task.type == 'discussion':
                                # 停止讨论模式的自主任务
                                from app.services.conversation.auto_conversation import stop_auto_discussion
                                stop_success = stop_auto_discussion(task_id, conversation.id)
                                if stop_success:
                                    print(f"已停止自主讨论任务: {autonomous_task.id}")
                                else:
                                    print(f"停止自主讨论任务失败，但将继续删除: {autonomous_task.id}")
                            else:
                                # 对于其他类型的自主任务，直接更新状态
                                autonomous_task.status = 'stopped'
                                print(f"已停止自主任务: {autonomous_task.id} (类型: {autonomous_task.type})")

                            stopped_autonomous_tasks += 1
                        except Exception as e:
                            print(f"停止自主任务 {autonomous_task.id} 时出错: {str(e)}")
                            # 继续处理，不中断删除流程

                    # 删除自主任务的执行记录
                    AutonomousTaskExecution.query.filter_by(autonomous_task_id=autonomous_task.id).delete()

                # 删除该会话的所有自主任务
                AutonomousTask.query.filter_by(conversation_id=conversation.id).delete()

            print(f"已停止并删除 {stopped_autonomous_tasks} 个自主任务")

            # 2. 删除与任务关联的会话及其数据
            for conversation in conversations:
                # 删除会话中的消息
                Message.query.filter_by(conversation_id=conversation.id).delete()
                # 删除会话中的智能体关联
                ConversationAgent.query.filter_by(conversation_id=conversation.id).delete()

            # 删除所有会话
            Conversation.query.filter_by(action_task_id=task_id).delete()

            # 2. 删除任务环境变量
            ActionTaskEnvironmentVariable.query.filter_by(action_task_id=task_id).delete()

            # 3. 删除与任务直接关联的智能体及其变量
            # 首先获取所有与任务关联的智能体
            task_agents = Agent.query.filter_by(action_task_id=task_id).all()
            for agent in task_agents:
                # 删除智能体的所有变量
                from app.models import AgentVariable
                deleted_vars = AgentVariable.query.filter_by(agent_id=agent.id).delete()
                print(f"已删除智能体 {agent.id} 的 {deleted_vars} 个变量")

            # 提交变量删除
            db.session.flush()

            # 然后删除智能体本身
            Agent.query.filter_by(action_task_id=task_id).delete()

        # 4. 删除智能体与任务的关联关系
        ActionTaskAgent.query.filter_by(action_task_id=task_id).delete()

        # 5. 删除项目空间文件
        try:
            workspace_service.delete_workspace_for_action_task(task_id)
            print(f"已删除行动任务 {task_id} 的项目空间文件")
        except Exception as e:
            print(f"删除行动任务 {task_id} 的项目空间文件失败: {str(e)}")
            # 继续处理，不中断任务删除

        # 6. 如果启用强制清理，执行额外的清理操作
        if force_cleanup:
            print("执行强制清理操作...")
            from sqlalchemy import text

            # 清理可能的孤立记录
            orphaned_executions = db.session.execute(text("""
                DELETE FROM autonomous_task_executions
                WHERE autonomous_task_id NOT IN (SELECT id FROM autonomous_tasks)
            """))
            print(f"清理了 {orphaned_executions.rowcount} 条孤立的执行记录")

            orphaned_autonomous_tasks = db.session.execute(text("""
                DELETE FROM autonomous_tasks
                WHERE conversation_id NOT IN (SELECT id FROM conversations)
            """))
            print(f"清理了 {orphaned_autonomous_tasks.rowcount} 条孤立的自主任务记录")

            orphaned_conv_agents = db.session.execute(text("""
                DELETE FROM conversation_agents
                WHERE conversation_id NOT IN (SELECT id FROM conversations)
                OR agent_id NOT IN (SELECT id FROM agents)
            """))
            print(f"清理了 {orphaned_conv_agents.rowcount} 条孤立的会话智能体关联记录")

            orphaned_messages = db.session.execute(text("""
                DELETE FROM messages
                WHERE conversation_id IS NOT NULL
                AND conversation_id NOT IN (SELECT id FROM conversations)
            """))
            print(f"清理了 {orphaned_messages.rowcount} 条孤立的消息记录")

            print("强制清理完成")

        # 7. 最后删除行动任务本身
        db.session.delete(task)
        db.session.commit()

        print(f"行动任务 {task_id} 删除完成")

        # 构建删除成功消息
        message_parts = []
        if stopped_autonomous_tasks > 0:
            message_parts.append(f'{stopped_autonomous_tasks} 个自主行动')
        message_parts.extend(['智能体', '环境变量', '会话记录', '长期记忆文件等'])

        return jsonify({
            'success': True,
            'message': f'行动任务及其所有关联数据已删除（包括{"、".join(message_parts)}）',
            'cascade': cascade,
            'force_cleanup': force_cleanup,
            'stopped_autonomous_tasks': stopped_autonomous_tasks
        })

    except Exception as e:
        db.session.rollback()
        print(f"删除行动任务错误: {str(e)}")
        return jsonify({
            'error': f'删除行动任务失败: {str(e)}',
            'details': str(e)
        }), 500

@action_task_bp.route('/action-tasks/<int:task_id>/agents', methods=['GET'])
def get_task_agents(task_id):
    """获取行动任务的所有智能体"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    # 获取查询参数
    is_observer = request.args.get('is_observer')

    # 获取行动任务的智能体
    task_agents = ActionTaskAgent.query.filter_by(action_task_id=task_id).all()
    agents = []

    for ta in task_agents:
        agent = Agent.query.get(ta.agent_id)
        if agent:
            # 如果指定了is_observer参数，进行筛选
            if is_observer is not None:
                is_observer_bool = is_observer.lower() == 'true'
                if agent.is_observer != is_observer_bool:
                    continue

            # 获取智能体的角色信息
            agent_role = None
            if hasattr(agent, 'role_id') and agent.role_id:
                agent_role = Role.query.get(agent.role_id)

            role_name = agent_role.name if agent_role else "智能助手"

            agents.append({
                'id': agent.id,
                'name': agent.name,
                'role_name': role_name,
                'description': agent.description,
                'avatar': agent.avatar,
                'is_default': ta.is_default,
                'is_observer': agent.is_observer,  # 添加是否为监督者标记
                'type': agent.type  # 添加智能体类型
            })

    return jsonify({'agents': agents})

@action_task_bp.route('/action-tasks/<int:task_id>/agents', methods=['POST'])
def add_agent_to_task(task_id):
    """向行动任务添加智能体"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    data = request.get_json()

    # 验证必填字段
    if not data.get('agent_id'):
        return jsonify({'error': '缺少必填字段: agent_id'}), 400

    agent_id = data.get('agent_id')
    agent = Agent.query.get(agent_id)
    if not agent:
        return jsonify({'error': '智能体未找到'}), 404

    # 检查智能体是否已经关联到任务
    existing = ActionTaskAgent.query.filter_by(
        action_task_id=task_id,
        agent_id=agent_id
    ).first()

    if existing:
        return jsonify({'error': '该智能体已经添加到任务中'}), 400

    # 检查是否有其他智能体，决定是否设为默认
    has_other_agents = ActionTaskAgent.query.filter_by(action_task_id=task_id).count() > 0

    # 创建关联
    task_agent = ActionTaskAgent(
        action_task_id=task_id,
        agent_id=agent_id,
        is_default=not has_other_agents  # 如果没有其他智能体，则设为默认
    )

    db.session.add(task_agent)
    db.session.commit()

    return jsonify({
        'success': True,
        'message': '智能体已添加到任务',
        'agent': {
            'id': agent.id,
            'name': agent.name,
            'description': agent.description,
            'avatar': agent.avatar,
            'is_default': task_agent.is_default
        }
    }), 201

@action_task_bp.route('/action-tasks/<int:task_id>/agents/<int:agent_id>', methods=['DELETE'])
def remove_agent_from_task(task_id, agent_id):
    """从行动任务中移除智能体"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    # 查找关联记录
    task_agent = ActionTaskAgent.query.filter_by(
        action_task_id=task_id,
        agent_id=agent_id
    ).first()

    if not task_agent:
        return jsonify({'error': '该智能体不在任务中'}), 404

    # 如果是默认智能体且不是唯一的智能体，需要指定新的默认智能体
    if task_agent.is_default:
        other_agent = ActionTaskAgent.query.filter(
            ActionTaskAgent.action_task_id == task_id,
            ActionTaskAgent.agent_id != agent_id
        ).first()

        if other_agent:
            other_agent.is_default = True

    # 删除关联
    db.session.delete(task_agent)
    db.session.commit()

    return jsonify({
        'success': True,
        'message': '智能体已从任务中移除'
    })

@action_task_bp.route('/action-tasks/<int:task_id>/agents/<int:agent_id>/default', methods=['PUT'])
def set_default_agent(task_id, agent_id):
    """设置行动任务的默认智能体"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    # 确保智能体存在并且与任务关联
    task_agent = ActionTaskAgent.query.filter_by(
        action_task_id=task_id,
        agent_id=agent_id
    ).first()

    if not task_agent:
        return jsonify({'error': '该智能体不在任务中'}), 404

    # 清除其他默认智能体
    ActionTaskAgent.query.filter_by(
        action_task_id=task_id,
        is_default=True
    ).update({'is_default': False})

    # 设置新的默认智能体
    task_agent.is_default = True
    db.session.commit()

    return jsonify({
        'success': True,
        'message': '已设置默认智能体'
    })

@action_task_bp.route('/action-tasks/<int:task_id>/direct-agents', methods=['GET'])
def get_task_direct_agents(task_id):
    """获取行动任务的直接关联智能体"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    # 获取行动任务的智能体
    agents = Agent.query.filter_by(action_task_id=task_id).all()
    result = []

    for agent in agents:
        # 查找是否为默认智能体
        is_default = False
        task_agent = ActionTaskAgent.query.filter_by(
            action_task_id=task_id,
            agent_id=agent.id
        ).first()
        if task_agent:
            is_default = task_agent.is_default

        result.append({
            'id': agent.id,
            'name': agent.name,
            'description': agent.description,
            'avatar': agent.avatar,
            'role_id': agent.role_id,
            'type': agent.type,
            'is_default': is_default
        })

    return jsonify({'agents': result})

@action_task_bp.route('/action-tasks/<int:task_id>/direct-agents', methods=['POST'])
def add_direct_agent_to_task(task_id):
    """向行动任务添加智能体实例"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    data = request.get_json()

    # 验证必填字段
    if not data.get('role_id'):
        return jsonify({'error': '缺少必填字段: role_id'}), 400

    role_id = data.get('role_id')
    role = Role.query.get(role_id)
    if not role:
        return jsonify({'error': '角色未找到'}), 404

    # 获取行动空间中角色的额外提示词
    additional_prompt = ''
    if task.action_space_id:
        action_space_role = ActionSpaceRole.query.filter_by(
            action_space_id=task.action_space_id,
            role_id=role_id
        ).first()

        if action_space_role and action_space_role.additional_prompt:
            additional_prompt = action_space_role.additional_prompt

    # 创建智能体实例
    agent = Agent(
        name=data.get('name', role.name),
        description=data.get('description', role.description),
        avatar=data.get('avatar', role.avatar),
        settings=data.get('settings', role.settings),
        action_task_id=task_id,
        role_id=role_id,
        type=data.get('type', 'agent'),
        additional_prompt=additional_prompt  # 设置额外提示词
    )
    db.session.add(agent)
    db.session.flush()  # 获取agent.id

    # 如果角色有环境变量且关联了行动空间，为智能体创建环境变量
    if role_id and task.action_space_id:
        # 查找该角色在当前行动空间的环境变量
        role_vars = RoleVariable.query.filter_by(
            role_id=role_id,
            action_space_id=task.action_space_id
        ).all()

        # 为智能体创建这些环境变量
        for role_var in role_vars:
            try:
                # 将角色变量转换为智能体变量
                AgentVariableService.create_variable(
                    agent_id=agent.id,
                    name=role_var.name,
                    value=role_var.default_value,
                    type=role_var.type,
                    is_public=True,
                    label=role_var.label
                )
                print(f"已为智能体 {agent.id} 创建环境变量: {role_var.name}")
            except Exception as e:
                print(f"为智能体 {agent.id} 创建环境变量 {role_var.name} 失败: {str(e)}")

    # 检查是否有其他智能体，决定是否设为默认
    has_default_agent = ActionTaskAgent.query.filter_by(
        action_task_id=task_id,
        is_default=True
    ).first() is not None

    # 创建关联
    task_agent = ActionTaskAgent(
        action_task_id=task_id,
        agent_id=agent.id,
        is_default=not has_default_agent  # 如果没有默认智能体，则设为默认
    )

    db.session.add(task_agent)
    db.session.commit()

    return jsonify({
        'success': True,
        'message': '智能体已添加到任务',
        'agent': {
            'id': agent.id,
            'name': agent.name,
            'description': agent.description,
            'avatar': agent.avatar,
            'role_id': role.id,
            'type': agent.type,
            'is_default': task_agent.is_default
        }
    }), 201

@action_task_bp.route('/action-tasks/<int:task_id>/default', methods=['PUT'])
def set_default_rule_set(task_id):
    """设置行动任务的默认规则集"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    # 确保规则集存在并且与任务关联
    rule_set = RuleSet.query.get(task.rule_set_id)
    if not rule_set:
        return jsonify({'error': '该任务没有关联的规则集'}), 404

    # 清除其他默认规则集
    ActionTask.query.filter_by(
        id=task_id,
        rule_set_id=task.rule_set_id
    ).update({'is_default': False})

    # 设置新的默认规则集
    task.is_default = True
    db.session.commit()

    return jsonify({
        'success': True,
        'message': '已设置默认规则集'
    })

@action_task_bp.route('/action-tasks/<int:task_id>/environment', methods=['GET'])
def get_task_environment(task_id):
    """获取行动任务的环境变量"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    # 获取行动任务的环境变量
    variables = ActionTaskEnvironmentVariable.query.filter_by(action_task_id=task_id).all()
    result = []

    for var in variables:
        result.append({
            'id': var.id,
            'name': var.name,  # 使用实际变量名
            'label': getattr(var, 'label', var.name.replace('_', ' ').title()),  # 优先使用label字段
            'value': var.value,
            'type': var.type,
            'history': var.history if var.history else [],
            'source': 'task'  # 标记来源为任务
        })

    # 获取任务相关的智能体变量
    # 先获取所有相关的智能体
    task_agents = ActionTaskAgent.query.filter_by(action_task_id=task_id).all()

    # 如果前端请求包含agent_variables参数，则也返回智能体变量
    if 'agent_variables' in request.args and request.args.get('agent_variables').lower() == 'true':
        for task_agent in task_agents:
            agent = Agent.query.get(task_agent.agent_id)
            if agent:
                # 获取该智能体的变量
                agent_vars = AgentVariable.query.filter_by(agent_id=agent.id).all()
                for var in agent_vars:
                    result.append({
                        'id': var.id,
                        'name': var.name,
                        'label': getattr(var, 'label', var.name.replace('_', ' ').title()),  # 优先使用label字段
                        'value': var.value,
                        'type': var.type,
                        'is_public': var.is_public,
                        'agent_id': agent.id,
                        'agent_name': agent.name,
                        'role_id': agent.role_id,
                        'history': var.history if var.history else [],
                        'source': 'agent'
                    })

    return jsonify({'variables': result})

@action_task_bp.route('/action-tasks/<int:task_id>/environment/variables', methods=['PUT'])
def update_task_environment_variable(task_id):
    """更新行动任务的环境变量"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    data = request.get_json()
    if not data.get('name'):
        return jsonify({'error': '缺少必填字段: name'}), 400

    # 使用实际变量名称（name字段）
    var_name = data.get('name')
    var_value = data.get('value')

    # 查找环境变量
    variable = ActionTaskEnvironmentVariable.query.filter_by(
        action_task_id=task_id,
        name=var_name
    ).first()

    if not variable:
        # 如果不存在，创建新的环境变量
        variable = ActionTaskEnvironmentVariable(
            name=var_name,
            value=var_value,
            type=data.get('type', 'text'),
            action_task_id=task_id
        )
        db.session.add(variable)
    else:
        # 更新历史记录
        if not variable.history:
            variable.history = []

        # 添加当前值到历史记录
        history_entry = {
            'value': variable.value,
            'timestamp': datetime.utcnow().isoformat()
        }
        variable.history.append(history_entry)

        # 更新当前值
        variable.value = var_value

    db.session.commit()

    return jsonify({
        'id': variable.id,
        'name': variable.name,
        'label': variable.name.replace('_', ' ').title(),  # 生成一个显示标签
        'value': variable.value,
        'type': variable.type,
        'history': variable.history,
        'message': '环境变量更新成功'
    })

@action_task_bp.route('/action-tasks/<int:task_id>/agents/<int:agent_id>/variables', methods=['GET'])
def get_agent_variables(task_id, agent_id):
    """获取特定智能体的环境变量"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    # 验证智能体属于这个任务
    task_agent = ActionTaskAgent.query.filter_by(
        action_task_id=task_id,
        agent_id=agent_id
    ).first()

    if not task_agent:
        return jsonify({'error': '智能体不属于该任务'}), 404

    agent = Agent.query.get(agent_id)
    if not agent:
        return jsonify({'error': '智能体未找到'}), 404

    # 获取智能体的变量
    variables = AgentVariable.query.filter_by(agent_id=agent_id).all()
    result = []

    for var in variables:
        result.append({
            'id': var.id,
            'name': var.name,
            'label': getattr(var, 'label', var.name.replace('_', ' ').title()),  # 优先使用label字段
            'value': var.value,
            'type': var.type,
            'is_public': var.is_public,
            'agent_id': agent_id,
            'agent_name': agent.name,
            'role_id': agent.role_id,
            'history': var.history if var.history else [],
            'source': 'agent'
        })

    return jsonify({
        'agent_id': agent_id,
        'agent_name': agent.name,
        'variables': result
    })

@action_task_bp.route('/action-tasks/<int:task_id>/observers', methods=['GET'])
def get_task_observers(task_id):
    """获取行动任务的所有监督者智能体"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    # 获取行动任务的监督者智能体
    observers = Agent.query.filter_by(action_task_id=task_id, is_observer=True).all()
    result = []

    for observer in observers:
        # 查找是否有关联
        task_agent = ActionTaskAgent.query.filter_by(
            action_task_id=task_id,
            agent_id=observer.id
        ).first()

        # 获取角色信息
        role = Role.query.get(observer.role_id) if observer.role_id else None

        result.append({
            'id': observer.id,
            'name': observer.name,
            'description': observer.description,
            'avatar': observer.avatar,
            'role_id': observer.role_id,
            'role_name': role.name if role else None,
            'type': observer.type,
            'is_observer': observer.is_observer,
            'additional_prompt': observer.additional_prompt
        })

    return jsonify({'observers': result})

@action_task_bp.route('/action-tasks/<int:task_id>/observers', methods=['POST'])
def add_observer_to_task(task_id):
    """向行动任务添加监督者智能体实例"""
    task = ActionTask.query.get(task_id)
    if not task:
        return jsonify({'error': '行动任务未找到'}), 404

    data = request.get_json()

    # 验证必填字段
    if not data.get('role_id'):
        return jsonify({'error': '缺少必填字段: role_id'}), 400

    role_id = data.get('role_id')
    role = Role.query.get(role_id)
    if not role:
        return jsonify({'error': '角色未找到'}), 404

    # 获取行动空间中监督者的额外提示词
    additional_prompt = ''
    if task.action_space_id:
        space_observer = ActionSpaceObserver.query.filter_by(
            action_space_id=task.action_space_id,
            role_id=role_id
        ).first()

        if space_observer:
            additional_prompt = space_observer.additional_prompt

    # 创建监督者智能体实例
    observer_agent = Agent(
        name=data.get('name', role.name),
        description=data.get('description', role.description),
        avatar=data.get('avatar', role.avatar),
        settings=data.get('settings', role.settings),
        action_task_id=task_id,
        role_id=role_id,
        type='observer',  # 标记为监督者类型
        is_observer=True,  # 标记为监督者
        additional_prompt=additional_prompt  # 设置额外提示词
    )
    db.session.add(observer_agent)
    db.session.flush()  # 获取observer_agent.id

    # 创建行动任务-智能体关联（监督者不设为默认智能体）
    task_agent = ActionTaskAgent(
        action_task_id=task_id,
        agent_id=observer_agent.id,
        is_default=False
    )
    db.session.add(task_agent)
    db.session.commit()

    return jsonify({
        'success': True,
        'message': '监督者已添加到任务',
        'observer': {
            'id': observer_agent.id,
            'name': observer_agent.name,
            'description': observer_agent.description,
            'avatar': observer_agent.avatar,
            'role_id': role.id,
            'type': observer_agent.type,
            'is_observer': observer_agent.is_observer,
            'additional_prompt': observer_agent.additional_prompt
        }
    }), 201

@action_task_bp.route('/action-tasks/<int:task_id>/batch-variables', methods=['GET'])
def get_batch_variables(task_id):
    """批量获取任务的所有变量（环境变量和所有智能体变量）"""
    try:
        # 获取任务信息
        task = ActionTask.query.get(task_id)
        if not task:
            return jsonify({'error': '行动任务未找到'}), 404

        # 获取环境变量
        env_vars = ActionTaskEnvironmentVariable.query.filter_by(action_task_id=task_id).all()
        environment_variables = []

        for var in env_vars:
            environment_variables.append({
                'id': var.id,
                'name': var.name,
                'label': getattr(var, 'label', var.name.replace('_', ' ').title()),
                'value': var.value,
                'type': var.type,
                'history': var.history if var.history else [],
                'source': 'task'
            })

        # 获取所有智能体
        task_agents = ActionTaskAgent.query.filter_by(action_task_id=task_id).all()
        agent_variables = []

        # 一次性获取所有智能体变量
        agent_ids = [ta.agent_id for ta in task_agents]
        agents_dict = {agent.id: agent for agent in Agent.query.filter(Agent.id.in_(agent_ids)).all()}

        # 批量查询所有智能体变量
        all_agent_vars = AgentVariable.query.filter(AgentVariable.agent_id.in_(agent_ids)).all()

        for var in all_agent_vars:
            agent = agents_dict.get(var.agent_id)
            if agent:
                agent_variables.append({
                    'id': var.id,
                    'name': var.name,
                    'label': getattr(var, 'label', var.name.replace('_', ' ').title()),
                    'value': var.value,
                    'type': var.type,
                    'is_public': var.is_public,
                    'agent_id': agent.id,
                    'agent_name': agent.name,
                    'role_id': agent.role_id if hasattr(agent, 'role_id') else None,
                    'history': var.history if var.history else [],
                    'source': 'agent'
                })

        # 返回结果
        return jsonify({
            'environment_variables': environment_variables,
            'agent_variables': agent_variables,
            'last_updated': datetime.utcnow().isoformat()
        })
    except Exception as e:
        return jsonify({'error': f'获取变量失败: {str(e)}'}), 500

@action_task_bp.route('/action-tasks/<int:task_id>/rules', methods=['GET'])
def get_task_rules(task_id):
    """获取任务关联的所有规则"""
    task = ActionTask.query.get_or_404(task_id)

    # 通过行动空间获取规则集，再获取规则
    action_space = task.action_space
    if not action_space:
        return jsonify({'rules': []})

    rules = []
    for rule_set_relation in action_space.rule_sets:
        rule_set = rule_set_relation.rule_set
        for rule_relation in rule_set.rules_relation:
            rule = rule_relation.rule
            rules.append({
                'id': rule.id,
                'name': rule.name,
                'description': rule.description,
                'content': rule.content,
                'type': rule.type,
                'category': rule.category,
                'is_active': rule.is_active,
                'rule_set_name': rule_set.name,
                'interpreter': rule.settings.get('interpreter', 'javascript') if rule.settings else 'javascript'
            })

    return jsonify({'rules': rules})

@action_task_bp.route('/action-tasks/<int:task_id>/rule-variables', methods=['GET'])
def get_task_rule_variables(task_id):
    """获取任务的规则测试变量上下文

    返回所有可用于规则测试的变量，包括：
    1. 任务环境变量
    2. 智能体变量（公开的）
    3. 外部环境变量
    """
    task = ActionTask.query.get_or_404(task_id)

    try:
        variables = {}

        # 1. 获取任务环境变量
        task_env_vars = ActionTaskEnvironmentVariable.query.filter_by(action_task_id=task_id).all()
        for var in task_env_vars:
            variables[var.name] = var.value

        # 2. 获取智能体的公开变量
        from app.models import Agent, AgentVariable
        agents = Agent.query.filter_by(action_task_id=task_id).all()
        for agent in agents:
            agent_vars = AgentVariable.query.filter_by(
                agent_id=agent.id,
                is_public=True  # 只获取公开变量
            ).all()
            for var in agent_vars:
                # 使用 agent_name.variable_name 格式避免冲突
                var_key = f"{agent.name}.{var.name}"
                variables[var_key] = var.value

        # 3. 获取外部环境变量
        from app.models import ExternalEnvironmentVariable
        external_vars = ExternalEnvironmentVariable.query.filter_by(
            status='active'  # 只获取活跃的外部变量
        ).all()
        for var in external_vars:
            if var.current_value is not None:
                variables[var.name] = var.current_value

        return jsonify({
            'variables': variables,
            'variable_count': len(variables),
            'last_updated': datetime.utcnow().isoformat()
        })

    except Exception as e:
        return jsonify({'error': f'获取规则变量失败: {str(e)}'}), 500

@action_task_bp.route('/action-tasks/<int:task_id>/rule-triggers', methods=['GET'])
def get_task_rule_triggers(task_id):
    """获取任务的规则触发记录

    支持分页和过滤参数：
    - page: 页码，默认1
    - per_page: 每页数量，默认20
    - rule_id: 过滤特定规则
    - trigger_type: 过滤触发类型 (manual, automatic, scheduled)
    - passed: 过滤通过状态 (true, false)
    """
    task = ActionTask.query.get_or_404(task_id)

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)  # 限制最大每页数量
    rule_id = request.args.get('rule_id', type=int)
    trigger_type = request.args.get('trigger_type')
    passed_filter = request.args.get('passed')

    try:
        # 构建查询
        query = RuleTriggerLog.query.filter_by(action_task_id=task_id)

        # 应用过滤条件
        if rule_id:
            query = query.filter(RuleTriggerLog.rule_id == rule_id)

        if trigger_type:
            query = query.filter(RuleTriggerLog.trigger_type == trigger_type)

        if passed_filter is not None:
            passed_bool = passed_filter.lower() == 'true'
            query = query.filter(RuleTriggerLog.passed == passed_bool)

        # 按创建时间倒序排列
        query = query.order_by(RuleTriggerLog.created_at.desc())

        # 分页
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # 构建结果
        triggers = []
        for trigger in pagination.items:
            # 获取关联的规则信息
            rule = Rule.query.get(trigger.rule_id)

            trigger_data = {
                'id': trigger.id,
                'rule_id': trigger.rule_id,
                'rule_name': rule.name if rule else f'规则{trigger.rule_id}',
                'rule_type': rule.type if rule else 'unknown',
                'action_task_id': trigger.action_task_id,
                'conversation_id': trigger.conversation_id,
                'trigger_type': trigger.trigger_type,
                'trigger_source': trigger.trigger_source,
                'context': trigger.context,
                'variables': trigger.variables,
                'result': trigger.result,
                'passed': trigger.passed,
                'message': trigger.message,
                'details': trigger.details,
                'execution_time': trigger.execution_time,
                'created_at': trigger.created_at.isoformat() if trigger.created_at else None,
                'updated_at': trigger.updated_at.isoformat() if trigger.updated_at else None
            }
            triggers.append(trigger_data)

        return jsonify({
            'triggers': triggers,
            'pagination': {
                'page': pagination.page,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })

    except Exception as e:
        return jsonify({'error': f'获取规则触发记录失败: {str(e)}'}), 500

@action_task_bp.route('/action-tasks/<int:task_id>/rule-triggers', methods=['POST'])
def create_rule_trigger_log(task_id):
    """创建规则触发记录

    请求体示例：
    {
        "rule_id": 1,
        "conversation_id": 1,
        "trigger_type": "manual",
        "trigger_source": "user",
        "context": "测试场景描述",
        "variables": {"var1": "value1"},
        "result": {"raw_result": true},
        "passed": true,
        "message": "规则检查通过",
        "details": "详细信息",
        "execution_time": 0.5
    }
    """
    task = ActionTask.query.get_or_404(task_id)
    data = request.get_json()

    if not data:
        return jsonify({'error': '缺少请求数据'}), 400

    # 验证必填字段
    required_fields = ['rule_id']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'缺少必填字段: {field}'}), 400

    try:
        # 验证规则是否存在
        rule = Rule.query.get(data['rule_id'])
        if not rule:
            return jsonify({'error': f'规则ID {data["rule_id"]} 不存在'}), 404

        # 创建触发记录
        trigger_log = RuleTriggerLog(
            rule_id=data['rule_id'],
            action_task_id=task_id,
            conversation_id=data.get('conversation_id'),
            trigger_type=data.get('trigger_type', 'manual'),
            trigger_source=data.get('trigger_source'),
            context=data.get('context'),
            variables=data.get('variables', {}),
            result=data.get('result'),
            passed=data.get('passed'),
            message=data.get('message'),
            details=data.get('details'),
            execution_time=data.get('execution_time')
        )

        db.session.add(trigger_log)
        db.session.commit()

        return jsonify({
            'id': trigger_log.id,
            'message': '规则触发记录创建成功'
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建规则触发记录失败: {str(e)}'}), 500