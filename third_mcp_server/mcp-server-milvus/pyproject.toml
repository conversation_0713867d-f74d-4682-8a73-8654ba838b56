[project]
name = "mcp-server-milvus"
version = "0.1.1"
description = "MCP server for Milvus"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "mcp[cli]>=1.1.2",
    "pymilvus>=2.5.1",
    "click>=8.0.0",
    "ruff>=0.11.0",
    "dotenv>=0.9.9",
]

[project.scripts]
mcp-server-milvus = "mcp_server_milvus.server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_server_milvus"]
