#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP服务器API路由

本模块定义MCP服务器的API路由，处理MCP服务器的请求，包括：
- 内部MCP服务器的请求处理
- 目前只包含变量MCP服务器的请求处理
"""

import json
import logging
from flask import Blueprint, request, jsonify, current_app
from app.services.mcp_server_manager import mcp_manager
from app.mcp_servers.variables_server import get_tools, handle_request
from app.mcp_servers.knowledge_base_server import get_tools as get_kb_tools, handle_request as handle_kb_request

# 设置日志
logger = logging.getLogger(__name__)

# 创建蓝图
bp = Blueprint('mcp_api', __name__, url_prefix='/api/mcp')

@bp.route('/variables', methods=['POST'])
def mcp_variables():
    """处理变量MCP服务器的请求"""
    try:
        # 检查是否是初始连接请求（空POST请求）
        if not request.data:
            # 返回工具定义
            tools = get_tools()
            return jsonify(tools), 200
        
        # 解析工具调用请求
        try:
            request_data = request.json
        except:
            return jsonify({
                "is_error": True,
                "error": "无效的JSON请求数据"
            }), 400
        
        # 处理请求
        response = handle_request(request_data)
        
        return jsonify(response)
    
    except Exception as e:
        logger.error(f"处理MCP变量服务器请求时出错: {str(e)}")
        return jsonify({
            "is_error": True,
            "error": f"服务器错误: {str(e)}"
        }), 500

@bp.route('/knowledge-base', methods=['POST'])
def mcp_knowledge_base():
    """处理知识库MCP服务器的请求"""
    try:
        # 检查是否是初始连接请求（空POST请求）
        if not request.data:
            # 返回工具定义
            tools = get_kb_tools()
            return jsonify(tools), 200

        # 解析工具调用请求
        try:
            request_data = request.json
        except:
            return jsonify({
                "is_error": True,
                "error": "无效的JSON请求数据"
            }), 400

        # 处理请求
        response = handle_kb_request(request_data)

        return jsonify(response)

    except Exception as e:
        logger.error(f"处理MCP知识库服务器请求时出错: {str(e)}")
        return jsonify({
            "is_error": True,
            "error": f"服务器错误: {str(e)}"
        }), 500

def register_blueprint(app):
    """注册MCP服务器API蓝图到Flask应用"""
    app.register_blueprint(bp)