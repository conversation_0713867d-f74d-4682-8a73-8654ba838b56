import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Tabs,
  Select,
  Button,
  Row,
  Col,
  Space,
  Divider,
  Empty,
  Spin,
  message,
  Form,
  Input,
  Checkbox,
  Radio,
  Tag,
  Table,
  Timeline,
  Modal,
  List
} from 'antd';
import {
  Area<PERSON>hartOutlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON>hartOutlined,
  FileTextOutlined,
  DownloadOutlined,
  FilterOutlined,
  SearchOutlined,
  SwapOutlined,
  FundOutlined,
  ColumnHeightOutlined,
  BranchesOutlined,
  ApartmentOutlined,
  NodeIndexOutlined,
  FileSearchOutlined,
  EnvironmentOutlined,
  GlobalOutlined,
  HistoryOutlined,
  CheckCircleOutlined,
  AimOutlined,
  HeatMapOutlined
} from '@ant-design/icons';
import { actionTaskAPI } from '../../services/api/actionTask';
import { agentAPI } from '../../services/api/agent';
import { roleAPI } from '../../services/api/role';
import { actionSpaceAPI } from '../../services/api/actionspace';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const ActionTaskAnalysis = () => {
  const [activeTab, setActiveTab] = useState('singleTask');
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedTask, setSelectedTask] = useState(null);
  const [selectedTasks, setSelectedTasks] = useState([]);
  const [analysisType, setAnalysisType] = useState('problem_solving');
  const [visualizationType, setVisualizationType] = useState('environment_timeline');
  const [reportType, setReportType] = useState('summary');

  // 模拟环境变量数据
  const [environmentData, setEnvironmentData] = useState({
    variables: [
      { name: 'temperature', label: '温度参数', history: [0.7, 0.8, 0.75, 0.9, 0.85] },
      { name: 'creativity', label: '创造力', history: [0.6, 0.7, 0.8, 0.75, 0.85] },
      { name: 'knowledge_depth', label: '知识深度', history: [0.5, 0.6, 0.65, 0.75, 0.8] },
      { name: 'focus', label: '专注度', history: [0.9, 0.85, 0.75, 0.8, 0.85] }
    ],
    timestamps: [
      new Date(Date.now() - 3600000 * 5).toISOString(),
      new Date(Date.now() - 3600000 * 4).toISOString(),
      new Date(Date.now() - 3600000 * 3).toISOString(),
      new Date(Date.now() - 3600000 * 2).toISOString(),
      new Date(Date.now() - 3600000 * 1).toISOString()
    ]
  });

  // 模拟规则触发数据
  const [ruleData, setRuleData] = useState([
    { id: 'rule-001', name: '复杂问题规则', triggers: 5, impact: 0.8 },
    { id: 'rule-002', name: '专业知识规则', triggers: 3, impact: 0.6 },
    { id: 'rule-003', name: '创意回答规则', triggers: 7, impact: 0.7 },
    { id: 'rule-004', name: '时间约束规则', triggers: 2, impact: 0.4 }
  ]);

  // 模拟交互模式数据
  const [interactionData, setInteractionData] = useState({
    patterns: [
      { type: '问题-回答', count: 12 },
      { type: '指示-执行', count: 8 },
      { type: '讨论-讨论', count: 5 },
      { type: '建议-反馈', count: 3 }
    ],
    agents: [
      { id: 'agent-001', name: '专家A', interactions: 15 },
      { id: 'agent-002', name: '专家B', interactions: 10 },
      { id: 'agent-003', name: '专家C', interactions: 8 }
    ]
  });

  // 模拟关键事件数据
  const [keyEventsData, setKeyEventsData] = useState([
    {
      id: 'event-001',
      timestamp: new Date(Date.now() - 3600000 * 4).toISOString(),
      title: '问题识别',
      description: '智能体成功识别了关键问题点',
      impact: 'high'
    },
    {
      id: 'event-002',
      timestamp: new Date(Date.now() - 3600000 * 3).toISOString(),
      title: '方案生成',
      description: '智能体提出了创新解决方案',
      impact: 'high'
    },
    {
      id: 'event-003',
      timestamp: new Date(Date.now() - 3600000 * 2).toISOString(),
      title: '方案评估',
      description: '方案可行性分析完成',
      impact: 'medium'
    },
    {
      id: 'event-004',
      timestamp: new Date(Date.now() - 3600000 * 1).toISOString(),
      title: '解决方案确认',
      description: '最终解决方案被采纳',
      impact: 'high'
    }
  ]);

  // 获取任务列表
  useEffect(() => {
    fetchTasks();
  }, []);

  const fetchTasks = async () => {
    setLoading(true);
    try {
      // 使用行动任务API
      const data = await actionTaskAPI.getAll();

      // 转换数据格式
      const transformedData = data.map(item => ({
        ...item,
        action_space_id: item.action_space_id || item.world_id,
        action_space_name: item.action_space_name || item.world_name || '未知行动空间',
      }));

      setTasks(transformedData);

      // 默认选择第一个任务
      if (transformedData.length > 0) {
        setSelectedTask(transformedData[0]);
      }
    } catch (error) {
      message.error('获取任务列表失败');
      console.error('获取任务失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理任务选择变更
  const handleTaskChange = (taskId) => {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      setSelectedTask(task);
    }
  };

  // 处理多任务选择变更
  const handleMultiTaskChange = (taskIds) => {
    setSelectedTasks(taskIds);
  };

  // 渲染单任务分析
  const renderSingleTaskAnalysis = () => {
    if (!selectedTask) {
      return (
        <Empty description="请选择要分析的任务" />
      );
    }

    return (
      <div>
        <Row gutter={16}>
          <Col span={24}>
            <Card
              title="解决方案路径"
              style={{ marginBottom: 16 }}
              extra={
                <Select
                  style={{ width: 180 }}
                  value={analysisType}
                  onChange={value => setAnalysisType(value)}
                >
                  <Option value="problem_solving">问题解决路径</Option>
                  <Option value="decision_making">决策流程分析</Option>
                  <Option value="knowledge_utilization">知识利用分析</Option>
                </Select>
              }
            >
              <div style={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <NodeIndexOutlined style={{ fontSize: 120, color: '#d9d9d9' }} />
                <div style={{ marginLeft: 16, maxWidth: 400 }}>
                  <Paragraph>
                    图表展示智能体解决问题的关键路径，包括初始问题识别、方案生成、方案评估和最终解决方案。
                  </Paragraph>
                  <Button type="primary">生成路径图</Button>
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Card
              title="关键事件时间线"
              style={{ marginBottom: 16 }}
            >
              <Timeline mode="left">
                {keyEventsData.map(event => (
                  <Timeline.Item
                    key={event.id}
                    color={
                      event.impact === 'high' ? 'green' :
                      event.impact === 'medium' ? 'blue' : 'gray'
                    }
                    label={new Date(event.timestamp).toLocaleString('zh-CN')}
                  >
                    <div>
                      <Text strong>{event.title}</Text>
                      <Tag
                        color={
                          event.impact === 'high' ? 'green' :
                          event.impact === 'medium' ? 'blue' : 'gray'
                        }
                        style={{ marginLeft: 8 }}
                      >
                        {event.impact === 'high' ? '高影响' :
                         event.impact === 'medium' ? '中等影响' : '低影响'}
                      </Tag>
                    </div>
                    <div>
                      <Text>{event.description}</Text>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </Card>
          </Col>

          <Col span={12}>
            <Card
              title="环境变量与行为关联"
              style={{ marginBottom: 16 }}
            >
              <div style={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <BranchesOutlined style={{ fontSize: 120, color: '#d9d9d9' }} />
                <div style={{ marginLeft: 16 }}>
                  <Paragraph>
                    图表展示环境变量与智能体行为之间的关联关系。
                  </Paragraph>
                  <Button type="primary">生成关联图</Button>
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Card
              title="规则影响评估"
              style={{ marginBottom: 16 }}
            >
              <Table
                dataSource={ruleData}
                columns={[
                  {
                    title: '规则名称',
                    dataIndex: 'name',
                    key: 'name',
                  },
                  {
                    title: '触发次数',
                    dataIndex: 'triggers',
                    key: 'triggers',
                    sorter: (a, b) => a.triggers - b.triggers,
                  },
                  {
                    title: '影响评分',
                    dataIndex: 'impact',
                    key: 'impact',
                    render: (impact) => (
                      <div style={{ width: 200 }}>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <div
                            style={{
                              width: `${impact * 100}%`,
                              height: 16,
                              background: impact > 0.7 ? '#52c41a' : impact > 0.5 ? '#1890ff' : '#faad14',
                              borderRadius: 2
                            }}
                          />
                          <span style={{ marginLeft: 8 }}>{(impact * 10).toFixed(1)}</span>
                        </div>
                      </div>
                    ),
                    sorter: (a, b) => a.impact - b.impact,
                  },
                  {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                      <Button type="link" size="small">详细分析</Button>
                    ),
                  },
                ]}
                pagination={false}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染多任务比较
  const renderMultiTaskComparison = () => {
    if (selectedTasks.length < 2) {
      return (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column', padding: 48 }}>
          <SwapOutlined style={{ fontSize: 120, color: '#d9d9d9' }} />
          <div style={{ marginTop: 24, textAlign: 'center' }}>
            <Title level={4}>请选择至少两个任务进行比较</Title>
            <Paragraph>
              在任务下拉列表中选择多个任务，然后使用比较工具分析不同任务之间的异同。
            </Paragraph>
            <Button
              type="primary"
              onClick={() => setActiveTab('singleTask')}
              style={{ marginTop: 16 }}
            >
              返回选择任务
            </Button>
          </div>
        </div>
      );
    }

    const selectedTaskObjects = tasks.filter(task => selectedTasks.includes(task.id));

    return (
      <div>
        <Card title="解决方案效果对比" style={{ marginBottom: 16 }}>
          <div style={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <SwapOutlined style={{ fontSize: 120, color: '#d9d9d9' }} />
            <div style={{ marginLeft: 16, maxWidth: 400 }}>
              <Paragraph>
                比较不同任务在解决相似问题时的效果，包括解决时间、解决质量和资源利用等指标。
              </Paragraph>
              <Button type="primary">生成对比图</Button>
            </div>
          </div>
        </Card>

        <Row gutter={16}>
          <Col span={24}>
            <Card title="环境变量影响分析" style={{ marginBottom: 16 }}>
              <Table
                dataSource={environmentData.variables}
                columns={[
                  {
                    title: '环境变量',
                    dataIndex: 'label',
                    key: 'label',
                  },
                  ...selectedTaskObjects.map((task, index) => ({
                    title: task.title,
                    key: `task_${index}`,
                    render: (_, record) => {
                      // 模拟不同任务的环境变量最终值
                      const finalValues = [0.85, 0.9, 0.75, 0.8];
                      return (
                        <div>
                          <Tag color={index === 0 ? 'blue' : index === 1 ? 'green' : 'orange'}>
                            {finalValues[index % finalValues.length]}
                          </Tag>
                        </div>
                      );
                    }
                  })),
                  {
                    title: '差异分析',
                    key: 'difference',
                    render: () => (
                      <Button type="link" size="small">查看详情</Button>
                    ),
                  }
                ]}
                pagination={false}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Card title="规则调整效果评估" style={{ marginBottom: 16 }}>
              <div style={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <ApartmentOutlined style={{ fontSize: 120, color: '#d9d9d9' }} />
                <div style={{ marginLeft: 16, maxWidth: 400 }}>
                  <Paragraph>
                    分析不同任务中规则调整对解决方案质量的影响，识别最有效的规则组合。
                  </Paragraph>
                  <Button type="primary">生成评估图表</Button>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染数据可视化
  const renderDataVisualization = () => {
    return (
      <div>
        <Card
          title="可视化图表"
          style={{ marginBottom: 16 }}
          extra={
            <Select
              style={{ width: 180 }}
              value={visualizationType}
              onChange={value => setVisualizationType(value)}
            >
              <Option value="environment_timeline">环境变量时间线</Option>
              <Option value="behavior_frequency">行为频率分布</Option>
              <Option value="rule_triggers">规则触发热力图</Option>
              <Option value="interaction_network">交互模式网络图</Option>
            </Select>
          }
        >
          <div style={{ padding: 24, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <div style={{ height: 300, width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              {visualizationType === 'environment_timeline' && <LineChartOutlined style={{ fontSize: 120, color: '#d9d9d9' }} />}
              {visualizationType === 'behavior_frequency' && <BarChartOutlined style={{ fontSize: 120, color: '#d9d9d9' }} />}
              {visualizationType === 'rule_triggers' && <HeatMapOutlined style={{ fontSize: 120, color: '#d9d9d9' }} />}
              {visualizationType === 'interaction_network' && <ApartmentOutlined style={{ fontSize: 120, color: '#d9d9d9' }} />}
            </div>
            <Paragraph style={{ marginTop: 16, textAlign: 'center' }}>
              {visualizationType === 'environment_timeline' && '环境变量随时间变化图表，展示任务过程中各环境参数的动态变化趋势。'}
              {visualizationType === 'behavior_frequency' && '行为频率分布图，显示不同类型行为的出现频率和分布情况。'}
              {visualizationType === 'rule_triggers' && '规则触发热力图，展示不同规则在任务过程中的触发频率和时间分布。'}
              {visualizationType === 'interaction_network' && '交互模式网络图，展示智能体之间的交互关系和模式。'}
            </Paragraph>
            <Space style={{ marginTop: 16 }}>
              <Button type="primary">生成图表</Button>
              <Button>导出数据</Button>
            </Space>
          </div>
        </Card>

        <Row gutter={16}>
          <Col span={12}>
            <Card title="数据筛选" style={{ marginBottom: 16 }}>
              <Form layout="vertical">
                <Form.Item label="时间范围">
                  <Select default_value="all">
                    <Option value="all">全部时间</Option>
                    <Option value="today">今天</Option>
                    <Option value="week">本周</Option>
                    <Option value="month">本月</Option>
                    <Option value="custom">自定义</Option>
                  </Select>
                </Form.Item>

                <Form.Item label="数据类型">
                  <Select default_value="all" mode="multiple">
                    <Option value="all">全部数据</Option>
                    <Option value="environment">环境变量</Option>
                    <Option value="message">消息数据</Option>
                    <Option value="rule">规则数据</Option>
                    <Option value="agent">智能体数据</Option>
                  </Select>
                </Form.Item>

                <Form.Item label="数据聚合">
                  <Select default_value="none">
                    <Option value="none">不聚合</Option>
                    <Option value="hour">按小时</Option>
                    <Option value="day">按天</Option>
                    <Option value="week">按周</Option>
                    <Option value="month">按月</Option>
                  </Select>
                </Form.Item>

                <Form.Item>
                  <Button type="primary" icon={<FilterOutlined />}>应用筛选</Button>
                </Form.Item>
              </Form>
            </Card>
          </Col>

          <Col span={12}>
            <Card title="图表配置" style={{ marginBottom: 16 }}>
              <Form layout="vertical">
                <Form.Item label="图表类型">
                  <Radio.Group default_value="line">
                    <Radio value="line">折线图</Radio>
                    <Radio value="bar">柱状图</Radio>
                    <Radio value="pie">饼图</Radio>
                    <Radio value="radar">雷达图</Radio>
                    <Radio value="scatter">散点图</Radio>
                    <Radio value="heatmap">热力图</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item label="显示设置">
                  <Checkbox.Group style={{ width: '100%' }}>
                    <Row>
                      <Col span={12}><Checkbox value="legend">显示图例</Checkbox></Col>
                      <Col span={12}><Checkbox value="tooltip">显示提示</Checkbox></Col>
                      <Col span={12}><Checkbox value="grid">显示网格</Checkbox></Col>
                      <Col span={12}><Checkbox value="animation">启用动画</Checkbox></Col>
                    </Row>
                  </Checkbox.Group>
                </Form.Item>

                <Form.Item label="颜色方案">
                  <Select default_value="default">
                    <Option value="default">默认配色</Option>
                    <Option value="light">浅色主题</Option>
                    <Option value="dark">深色主题</Option>
                    <Option value="colorful">多彩主题</Option>
                  </Select>
                </Form.Item>

                <Form.Item>
                  <Button type="primary">应用配置</Button>
                </Form.Item>
              </Form>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染报告生成
  const renderReportGeneration = () => {
    return (
      <div>
        <Row gutter={16}>
          <Col span={16}>
            <Card
              title="报告生成"
              style={{ marginBottom: 16 }}
              extra={
                <Select
                  style={{ width: 180 }}
                  value={reportType}
                  onChange={value => setReportType(value)}
                >
                  <Option value="summary">任务摘要报告</Option>
                  <Option value="detailed">详细分析报告</Option>
                  <Option value="comparative">多任务对比报告</Option>
                  <Option value="executive">执行摘要报告</Option>
                </Select>
              }
            >
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column', padding: 48 }}>
                <FileTextOutlined style={{ fontSize: 120, color: '#d9d9d9' }} />
                <div style={{ marginTop: 24, textAlign: 'center' }}>
                  <Title level={4}>报告预览</Title>
                  <Paragraph>
                    选择报告类型并配置内容，点击"生成报告"按钮生成报告预览。
                  </Paragraph>
                  <Space size="large" style={{ marginTop: 16 }}>
                    <Button
                      type="primary"
                      icon={<FileTextOutlined />}
                      size="large"
                    >
                      生成报告
                    </Button>
                    <Button
                      icon={<DownloadOutlined />}
                      size="large"
                      disabled
                    >
                      导出报告
                    </Button>
                  </Space>
                </div>
              </div>
            </Card>
          </Col>

          <Col span={8}>
            <Card title="报告配置" style={{ marginBottom: 16 }}>
              <Form layout="vertical">
                <Form.Item label="报告标题">
                  <Input placeholder="输入报告标题" />
                </Form.Item>

                <Form.Item label="包含内容">
                  <Checkbox.Group style={{ width: '100%' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                      <Checkbox value="basic_info">基本信息</Checkbox>
                      <Checkbox value="task_summary">任务概述</Checkbox>
                      <Checkbox value="key_events">关键事件分析</Checkbox>
                      <Checkbox value="environment_changes">环境变量变化</Checkbox>
                      <Checkbox value="rule_analysis">规则触发分析</Checkbox>
                      <Checkbox value="agent_performance">智能体表现</Checkbox>
                      <Checkbox value="solution_evaluation">解决方案评估</Checkbox>
                      <Checkbox value="recommendations">建议与改进</Checkbox>
                    </div>
                  </Checkbox.Group>
                </Form.Item>

                <Form.Item label="图表包含">
                  <Checkbox.Group style={{ width: '100%' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                      <Checkbox value="timeline_chart">时间线图表</Checkbox>
                      <Checkbox value="variable_charts">环境变量图表</Checkbox>
                      <Checkbox value="rule_charts">规则触发图表</Checkbox>
                      <Checkbox value="interaction_network">交互网络图</Checkbox>
                    </div>
                  </Checkbox.Group>
                </Form.Item>

                <Form.Item label="导出格式">
                  <Radio.Group default_value="pdf">
                    <Radio value="pdf">PDF文档</Radio>
                    <Radio value="docx">Word文档</Radio>
                    <Radio value="html">HTML页面</Radio>
                    <Radio value="markdown">Markdown</Radio>
                  </Radio.Group>
                </Form.Item>
              </Form>
            </Card>
          </Col>
        </Row>

        <Card title="报告模板库" style={{ marginBottom: 16 }}>
          <List
            grid={{ gutter: 16, column: 4 }}
            dataSource={[
              { id: 'tpl-1', name: '标准任务摘要', type: 'summary', usage: 65 },
              { id: 'tpl-2', name: '详细分析报告', type: 'detailed', usage: 42 },
              { id: 'tpl-3', name: '多任务对比报告', type: 'comparative', usage: 28 },
              { id: 'tpl-4', name: '执行摘要报告', type: 'executive', usage: 37 }
            ]}
            renderItem={item => (
              <List.Item>
                <Card
                  hoverable
                  style={{ textAlign: 'center' }}
                  actions={[
                    <Button type="link" key="use">使用模板</Button>,
                    <Button type="link" key="preview">预览</Button>
                  ]}
                >
                  <FileTextOutlined style={{ fontSize: 42, color: '#1890ff', display: 'block', margin: '0 auto 16px' }} />
                  <div>
                    <Text strong>{item.name}</Text>
                    <div><Tag color="blue">{item.type === 'summary' ? '摘要型' : item.type === 'detailed' ? '详细型' : item.type === 'comparative' ? '对比型' : '执行型'}</Tag></div>
                    <div style={{ marginTop: 8 }}><Text type="secondary">使用次数: {item.usage}</Text></div>
                  </div>
                </Card>
              </List.Item>
            )}
          />
        </Card>
      </div>
    );
  };

  return (
    <div>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 24
      }}>
        <div>
          <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>任务分析</Title>
          <Text type="secondary">
            分析行动任务的执行过程、环境变量影响和解决方案效果
          </Text>
        </div>
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'singleTask',
              label: (
                <span>
                  <FileSearchOutlined />
                  单任务分析
                </span>
              ),
              children: (
                <>
                  <div style={{ marginBottom: 16 }}>
                    <Select
                      style={{ width: 300 }}
                      placeholder="选择要分析的任务"
                      loading={loading}
                      value={selectedTask?.id}
                      onChange={handleTaskChange}
                    >
                      {tasks.map(task => (
                        <Option key={task.id} value={task.id}>
                          {task.title} - {task.action_space_name}
                        </Option>
                      ))}
                    </Select>
                  </div>
                  {renderSingleTaskAnalysis()}
                </>
              )
            },
            {
              key: 'multiTask',
              label: (
                <span>
                  <SwapOutlined />
                  多任务比较
                </span>
              ),
              children: (
                <>
                  <div style={{ marginBottom: 16 }}>
                    <Select
                      mode="multiple"
                      style={{ width: '100%' }}
                      placeholder="选择要比较的任务（至少选择两个）"
                      loading={loading}
                      value={selectedTasks}
                      onChange={handleMultiTaskChange}
                    >
                      {tasks.map(task => (
                        <Option key={task.id} value={task.id}>
                          {task.title} - {task.action_space_name}
                        </Option>
                      ))}
                    </Select>
                  </div>
                  {renderMultiTaskComparison()}
                </>
              )
            },
            {
              key: 'visualization',
              label: (
                <span>
                  <BarChartOutlined />
                  数据可视化
                </span>
              ),
              children: renderDataVisualization()
            },
            {
              key: 'report',
              label: (
                <span>
                  <FileTextOutlined />
                  报告生成
                </span>
              ),
              children: renderReportGeneration()
            }
          ]}
        />
      </Card>
    </div>
  );
};

export default ActionTaskAnalysis;