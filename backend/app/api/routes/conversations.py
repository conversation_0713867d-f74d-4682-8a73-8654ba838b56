"""
会话API路由

处理与会话相关的所有API请求
"""
from flask import Blueprint, request, jsonify, current_app
from app.models import Conversation, ConversationAgent, Agent, Message, AutonomousTask, AutonomousTaskExecution, ActionTask, db
from datetime import datetime
from app.services.conversation_service import ConversationService
import queue
import logging
import threading

logger = logging.getLogger(__name__)

# 创建Blueprint
conversation_bp = Blueprint('conversation_api', __name__)

@conversation_bp.route('/action-tasks/<int:task_id>/conversations', methods=['GET'])
def get_action_task_conversations(task_id):
    """获取行动任务的会话列表"""
    try:
        # 使用合并后的服务类获取会话列表
        conversations = ConversationService.get_conversations(task_id)
        return jsonify({'conversations': conversations})
    except ValueError as e:
        return jsonify({'error': str(e)}), 404
    except Exception as e:
        return jsonify({'error': f'获取会话列表失败: {str(e)}'}), 500

@conversation_bp.route('/action-tasks/<int:task_id>/conversations', methods=['POST'])
def create_action_task_conversation(task_id):
    """创建新的行动任务会话"""
    data = request.get_json()

    # 验证必填字段
    if 'title' not in data:
        return jsonify({'error': '缺少必填字段: title'}), 400

    try:
        # 使用合并后的服务类创建会话
        result = ConversationService.create_conversation(task_id, data)
        return jsonify(result), 201
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        return jsonify({'error': f'创建会话失败: {str(e)}'}), 500

@conversation_bp.route('/action-tasks/<int:task_id>/conversations/<int:conversation_id>', methods=['GET'])
def get_action_task_conversation(task_id, conversation_id):
    """获取行动任务的特定会话"""
    try:
        # 检查会话是否存在且属于该行动任务
        conversation = Conversation.query.get(conversation_id)
        if not conversation or conversation.action_task_id != task_id:
            return jsonify({'error': '会话未找到或不属于该行动任务'}), 404

        # 获取会话的智能体
        conv_agents = ConversationAgent.query.filter_by(conversation_id=conversation_id).all()
        agents = []

        for ca in conv_agents:
            agent = Agent.query.get(ca.agent_id)
            if agent:
                agents.append({
                    'id': agent.id,
                    'name': agent.name,
                    'description': agent.description,
                    'avatar': agent.avatar if hasattr(agent, 'avatar') else None,
                    'is_default': ca.is_default
                })

        # 获取会话的智能体数量和消息数量
        agent_count = len(agents)
        message_count = Message.query.filter_by(conversation_id=conversation_id).count()

        result = {
            'id': conversation.id,
            'title': conversation.title,
            'description': conversation.description,
            'status': conversation.status,
            'mode': conversation.mode,
            'created_at': conversation.created_at.isoformat() if conversation.created_at else None,
            'updated_at': conversation.updated_at.isoformat() if conversation.updated_at else None,
            'agent_count': agent_count,
            'message_count': message_count,
            'agents': agents
        }

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'获取会话详情失败: {str(e)}'}), 500

@conversation_bp.route('/action-tasks/<int:task_id>/conversations/<int:conversation_id>/messages', methods=['GET'])
def get_conversation_messages(task_id, conversation_id):
    """获取会话的消息列表"""
    try:
        # 使用合并后的服务类获取消息
        messages = ConversationService.get_conversation_messages(task_id, conversation_id)
        return jsonify({'messages': messages})
    except ValueError as e:
        return jsonify({'error': str(e)}), 404
    except Exception as e:
        return jsonify({'error': f'获取消息失败: {str(e)}'}), 500

@conversation_bp.route('/action-tasks/<int:task_id>/conversations/<int:conversation_id>/messages', methods=['POST'])
def create_conversation_message(task_id, conversation_id):
    """在会话中发送新消息

    支持普通和流式两种返回方式：
    - 普通方式：直接返回JSON响应
    - 流式方式：使用SSE方式流式返回响应，通过?stream=1指定

    参数：
    - content: 消息内容（必填）
    - target_agent_id: 目标智能体ID（可选，单个智能体）
    - target_agent_ids: 目标智能体ID列表（可选，多个智能体，优先级高于target_agent_id）
    - user_id: 用户ID（可选）
    """
    try:
        # 检查流式模式标志
        is_stream = request.args.get('stream', '0') == '1'

        # 检查会话是否存在且属于该行动任务
        conversation = Conversation.query.get(conversation_id)
        if not conversation or conversation.action_task_id != task_id:
            return jsonify({'error': '会话未找到或不属于该行动任务'}), 404

        # 获取请求数据
        data = request.get_json()
        content = data.get('content')

        # 验证必填字段
        if not content:
            return jsonify({'error': '缺少必填字段: content'}), 400

        logger.debug(f"[会话路由] 接收到发送消息请求: conversation_id={conversation_id}, content={content}, stream={is_stream}")

        # 准备消息数据
        message_data = {
            'content': content,
            'user_id': data.get('user_id')
        }

        # 处理目标智能体ID（支持单个或多个）
        if 'target_agent_ids' in data:
            message_data['target_agent_ids'] = data.get('target_agent_ids')
            logger.debug(f"[会话路由] 使用多个目标智能体: {message_data['target_agent_ids']}")
        elif 'target_agent_id' in data:
            message_data['target_agent_id'] = data.get('target_agent_id')
            logger.debug(f"[会话路由] 使用单个目标智能体: {message_data['target_agent_id']}")

        # 处理发送目标（监督者功能）
        send_target = data.get('send_target', 'task')  # 默认为任务会话
        message_data['send_target'] = send_target
        logger.debug(f"[会话路由] 发送目标: {send_target}")

        # 根据模式处理消息
        if not is_stream:
            # 普通模式：直接调用服务处理
            human_message, agent_message = ConversationService.add_message_to_conversation(
                conversation_id, message_data
            )

            if not human_message:
                error_detail = "会话服务未能创建人类消息"
                logger.debug(f"错误: {error_detail}")
                return jsonify({
                    'error': '消息添加失败',
                    'detail': error_detail,
                    'response': {
                        'id': 'error',
                        'content': '发生错误，无法处理消息',
                        'role': 'system',
                        'agent_id': None,
                        'created_at': None
                    }
                }), 500

            # 组装响应
            result = {
                'message': '消息添加成功',
                'id': human_message.id,
                'human_message': {
                    'id': human_message.id,
                    'content': human_message.content,
                    'role': human_message.role,
                    'source': getattr(human_message, 'source', 'taskConversation'),
                    'agent_id': human_message.agent_id,
                    'created_at': human_message.created_at.isoformat() if human_message.created_at else None
                }
            }

            if agent_message:
                # 如果有智能体回复，添加到响应
                result['response'] = {
                    'id': agent_message.id,
                    'content': agent_message.content,
                    'role': agent_message.role,
                    'source': getattr(agent_message, 'source', 'taskConversation'),
                    'agent_id': agent_message.agent_id,
                    'created_at': agent_message.created_at.isoformat() if agent_message.created_at else None
                }

                # 添加智能体信息
                if agent_message.agent_id:
                    agent = Agent.query.get(agent_message.agent_id)
                    if agent:
                        result['response']['agent_name'] = agent.name
                        result['response']['agent'] = {
                            'id': agent.id,
                            'name': agent.name,
                            'description': agent.description
                        }

            return jsonify(result)

        # 流式模式：创建队列和回调
        result_queue = queue.Queue()

        # 获取当前应用上下文以传递给后台线程
        app_context = current_app._get_current_object().app_context()

        # 启动后台线程处理流式响应
        thread = threading.Thread(
            target=ConversationService.process_stream_message,
            args=(app_context, task_id, conversation_id, message_data, result_queue)
        )
        thread.daemon = True
        thread.start()

        # 返回SSE响应
        from app.services.conversation.stream_handler import create_sse_response, queue_to_sse
        return create_sse_response(lambda: queue_to_sse(result_queue))

    except Exception as e:
        # 处理异常
        logger.debug(f"消息处理异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'处理消息失败: {str(e)}'}), 500

@conversation_bp.route('/action-tasks/<int:task_id>/conversations/<int:conversation_id>/auto-discussion', methods=['POST'])
def start_auto_discussion(task_id, conversation_id):
    """
    启动智能体自动讨论

    参数：
    - rounds: 讨论轮数，每轮指所有智能体各发言一次
    - topic: 讨论主题 (可选)
    - summarize: 是否在讨论结束后进行总结 (默认为True)
    - summarizerAgentId: 指定进行总结的智能体ID (可选，默认使用第一个智能体)
    """
    try:
        # 检查会话是否存在且属于该行动任务
        conversation = Conversation.query.get(conversation_id)
        if not conversation or conversation.action_task_id != task_id:
            return jsonify({'error': '会话未找到或不属于该行动任务'}), 404

        # 获取请求数据
        data = request.get_json()

        # 判断任务类型
        is_infinite = data.get('isInfinite', False)  # 变量停止模式
        is_time_trigger = data.get('isTimeTrigger', False)  # 时间触发模式
        is_variable_trigger = data.get('isVariableTrigger', False)  # 变量触发模式

        # 使用流式模式
        is_stream = request.args.get('stream', '0') == '1'

        # 检查会话中是否有智能体
        conv_agents = ConversationAgent.query.filter_by(conversation_id=conversation_id).all()
        if not conv_agents or len(conv_agents) < 2:
            return jsonify({'error': '会话中至少需要两个智能体才能进行自动讨论'}), 400

        if is_infinite:
            # 变量停止模式
            topic = data.get('topic', '请基于各自角色和知识，持续进行行动，直到满足停止条件')
            stop_conditions = data.get('stopConditions', [])
            condition_logic = data.get('conditionLogic', 'and')
            max_runtime = data.get('maxRuntime', 0)

            # 获取计划功能参数
            enable_planning = data.get('enablePlanning', False)
            planner_agent_id = data.get('plannerAgentId')

            # 构建配置
            config = {
                'topic': topic,
                'stopConditions': stop_conditions,
                'conditionLogic': condition_logic,
                'maxRuntime': max_runtime,
                'enable_planning': enable_planning,
                'planner_agent_id': planner_agent_id
            }

            if not is_stream:
                # 非流式模式
                from app.services.conversation.variable_stop_conversation import start_variable_stop_conversation
                result = start_variable_stop_conversation(
                    task_id,
                    conversation_id,
                    config
                )
                return jsonify(result)
            else:
                # 流式模式
                result_queue = queue.Queue()
                app_context = current_app._get_current_object().app_context()

                from app.services.conversation.variable_stop_conversation import start_variable_stop_conversation_stream
                thread = threading.Thread(
                    target=start_variable_stop_conversation_stream,
                    args=(app_context, task_id, conversation_id, config, result_queue)
                )
                thread.daemon = True
                thread.start()

                # 返回SSE响应
                from app.services.conversation.stream_handler import create_sse_response, queue_to_sse
                return create_sse_response(lambda: queue_to_sse(result_queue))

        elif is_time_trigger:
            # 时间触发模式
            try:
                # 提取时间触发模式配置
                config = {
                    'timeInterval': data.get('timeInterval', 30),
                    'maxExecutions': data.get('maxExecutions', 0),
                    'triggerAction': 'single_round',  # 时间触发模式只支持单轮行动
                    'enableTimeLimit': data.get('enableTimeLimit', False),
                    'totalTimeLimit': data.get('totalTimeLimit', 1440),
                    'topic': data.get('topic', '请基于各自角色和知识，持续进行行动'),
                    'speakingMode': data.get('speakingMode', 'sequential'),
                    'enablePlanning': data.get('enablePlanning', False),
                    'plannerAgentId': data.get('plannerAgentId')
                }

                # 验证配置参数
                time_interval = config['timeInterval']
                max_executions = config['maxExecutions']

                if time_interval < 1 or time_interval > 1440:
                    return jsonify({'error': f'时间间隔必须在1-1440分钟之间，当前值: {time_interval}'}), 400

                if max_executions < 0:
                    return jsonify({'error': f'最大执行次数不能为负数，当前值: {max_executions}'}), 400

                if config['enableTimeLimit']:
                    total_time_limit = config['totalTimeLimit']
                    if total_time_limit < 1 or total_time_limit > 10080:  # 1周
                        return jsonify({'error': f'总时长限制必须在1-10080分钟之间，当前值: {total_time_limit}'}), 400

                logger.info(f"启动时间触发模式: task_id={task_id}, conversation_id={conversation_id}, config={config}")

                if is_stream:
                    # 流式模式
                    result_queue = queue.Queue()
                    app_context = current_app._get_current_object().app_context()

                    # 启动后台线程处理时间触发任务
                    from app.services.conversation.time_trigger_conversation import start_time_trigger_conversation
                    thread = threading.Thread(
                        target=start_time_trigger_conversation,
                        args=(task_id, conversation_id, config),
                        kwargs={
                            'streaming': True,
                            'app_context': app_context,
                            'result_queue': result_queue
                        }
                    )
                    thread.daemon = True
                    thread.start()

                    # 返回SSE响应
                    from app.services.conversation.stream_handler import create_sse_response, queue_to_sse
                    return create_sse_response(lambda: queue_to_sse(result_queue))
                else:
                    # 非流式模式
                    from app.services.conversation.time_trigger_conversation import start_time_trigger_conversation
                    result = start_time_trigger_conversation(task_id, conversation_id, config)
                    return jsonify(result)

            except Exception as e:
                logger.error(f"启动时间触发模式失败: {str(e)}")
                import traceback
                traceback.print_exc()
                return jsonify({'error': f'启动时间触发模式失败: {str(e)}'}), 500

        elif is_variable_trigger:
            # 变量触发模式暂未实现
            return jsonify({'error': '变量触发模式暂未实现'}), 400

        else:
            # 传统讨论模式
            rounds = data.get('rounds', 1)
            topic = data.get('topic', '请基于各自角色和知识，进行一次有深度的讨论')
            summarize = data.get('summarize', True)
            summarizer_agent_id = data.get('summarizerAgentId')
            # 获取计划功能参数
            enable_planning = data.get('enablePlanning', False)
            planner_agent_id = data.get('plannerAgentId')

            # 验证必填字段
            if not isinstance(rounds, int) or rounds <= 0:
                return jsonify({'error': '讨论轮数必须是大于0的整数'}), 400

            if not is_stream:
                # 非流式模式，直接调用服务
                from app.services.conversation.auto_conversation import start_auto_discussion
                result = start_auto_discussion(
                    task_id,
                    conversation_id,
                    rounds=rounds,
                    topic=topic,
                    summarize=summarize,
                    summarizer_agent_id=summarizer_agent_id,
                    enable_planning=enable_planning,
                    planner_agent_id=planner_agent_id
                )
                return jsonify(result)
            else:
                # 流式模式：创建队列和回调
                result_queue = queue.Queue()

                # 获取当前应用上下文以传递给后台线程
                app_context = current_app._get_current_object().app_context()

                # 启动后台线程处理流式响应
                from app.services.conversation.auto_conversation import start_auto_discussion_stream
                thread = threading.Thread(
                    target=start_auto_discussion_stream,
                    args=(app_context, task_id, conversation_id, rounds, topic, summarize, result_queue, summarizer_agent_id, enable_planning, planner_agent_id)
                )
                thread.daemon = True
                thread.start()

                # 返回SSE响应
                from app.services.conversation.stream_handler import create_sse_response, queue_to_sse
                return create_sse_response(lambda: queue_to_sse(result_queue))

    except Exception as e:
        # 处理异常
        logger.debug(f"自动讨论处理异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'处理自动讨论失败: {str(e)}'}), 500

@conversation_bp.route('/action-tasks/<int:task_id>/conversations/<int:conversation_id>/cancel-stream', methods=['POST'])
def cancel_streaming_response(task_id, conversation_id):
    """取消当前正在进行的流式响应

    用于客户端中断当前流式输出并切换到下一个智能体（如果有）

    可选参数：
    - agent_id: 智能体ID，如果提供则只取消该智能体的流式任务
    """
    try:
        from app.services.conversation.stream_handler import cancel_streaming_task

        # 获取请求数据
        data = request.get_json() or {}
        agent_id = data.get('agent_id')

        # 尝试取消流式任务
        success = cancel_streaming_task(task_id, conversation_id, agent_id)

        if success:
            if agent_id:
                message = f"成功取消智能体 {agent_id} 的流式响应"
            else:
                message = "成功取消流式响应"
            code = 200
        else:
            if agent_id:
                message = f"没有找到智能体 {agent_id} 的活动流式响应任务"
            else:
                message = "没有找到活动的流式响应任务"
            code = 404

        return jsonify({
            'success': success,
            'message': message,
            'agent_id': agent_id
        }), code
    except Exception as e:
        # 处理异常
        logger.debug(f"取消流式响应异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'取消流式响应失败: {str(e)}'}), 500

@conversation_bp.route('/action-tasks/<int:task_id>/autonomous-tasks', methods=['GET'])
def get_action_task_autonomous_tasks(task_id):
    """获取行动任务的所有自主任务记录"""
    try:
        # 检查行动任务是否存在
        action_task = ActionTask.query.get(task_id)
        if not action_task:
            return jsonify({'error': '行动任务未找到'}), 404

        # 获取该行动任务下所有会话的自主任务
        autonomous_tasks = AutonomousTask.query.join(Conversation).filter(
            Conversation.action_task_id == task_id
        ).order_by(AutonomousTask.created_at.desc()).all()

        result = []
        for task in autonomous_tasks:
            # 获取任务的执行记录
            executions = AutonomousTaskExecution.query.filter_by(autonomous_task_id=task.id).order_by(AutonomousTaskExecution.created_at.desc()).all()

            execution_list = []
            for execution in executions:
                execution_data = {
                    'id': execution.id,
                    'execution_type': execution.execution_type,
                    'trigger_source': execution.trigger_source,
                    'trigger_data': execution.trigger_data,
                    'status': execution.status,
                    'start_time': execution.start_time.isoformat() if execution.start_time else None,
                    'end_time': execution.end_time.isoformat() if execution.end_time else None,
                    'result': execution.result,
                    'error_message': execution.error_message,
                    'created_at': execution.created_at.isoformat() if execution.created_at else None
                }
                execution_list.append(execution_data)

            # 获取会话信息
            conversation = Conversation.query.get(task.conversation_id)
            conversation_info = {
                'id': conversation.id,
                'name': conversation.title if conversation else f'会话 {task.conversation_id}'
            } if conversation else {'id': task.conversation_id, 'name': f'会话 {task.conversation_id}'}

            task_data = {
                'id': task.id,
                'conversation_id': task.conversation_id,
                'conversation': conversation_info,
                'type': task.type,
                'status': task.status,
                'config': task.config,
                'created_at': task.created_at.isoformat() if task.created_at else None,
                'updated_at': task.updated_at.isoformat() if task.updated_at else None,
                'executions': execution_list
            }
            result.append(task_data)

        return jsonify({
            'autonomous_tasks': result,
            'total': len(result)
        })

    except Exception as e:
        logger.error(f"获取行动任务自主任务记录失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'获取行动任务自主任务记录失败: {str(e)}'}), 500


@conversation_bp.route('/action-tasks/<int:task_id>/conversations/<int:conversation_id>/autonomous-tasks', methods=['GET'])
def get_conversation_autonomous_tasks(task_id, conversation_id):
    """获取特定会话的自主任务记录（保持兼容性）"""
    try:
        # 检查会话是否存在且属于该行动任务
        conversation = Conversation.query.get(conversation_id)
        if not conversation or conversation.action_task_id != task_id:
            return jsonify({'error': '会话未找到或不属于该行动任务'}), 404

        # 获取该会话的所有自主任务
        autonomous_tasks = AutonomousTask.query.filter_by(conversation_id=conversation_id).order_by(AutonomousTask.created_at.desc()).all()

        result = []
        for task in autonomous_tasks:
            # 获取任务的执行记录
            executions = AutonomousTaskExecution.query.filter_by(autonomous_task_id=task.id).order_by(AutonomousTaskExecution.created_at.desc()).all()

            execution_list = []
            for execution in executions:
                execution_data = {
                    'id': execution.id,
                    'execution_type': execution.execution_type,
                    'trigger_source': execution.trigger_source,
                    'trigger_data': execution.trigger_data,
                    'status': execution.status,
                    'start_time': execution.start_time.isoformat() if execution.start_time else None,
                    'end_time': execution.end_time.isoformat() if execution.end_time else None,
                    'result': execution.result,
                    'error_message': execution.error_message,
                    'created_at': execution.created_at.isoformat() if execution.created_at else None
                }
                execution_list.append(execution_data)

            # 获取会话信息
            conversation_info = {
                'id': conversation.id,
                'name': conversation.title if conversation else f'会话 {conversation_id}'
            }

            task_data = {
                'id': task.id,
                'conversation_id': task.conversation_id,
                'conversation': conversation_info,
                'type': task.type,
                'status': task.status,
                'config': task.config,
                'created_at': task.created_at.isoformat() if task.created_at else None,
                'updated_at': task.updated_at.isoformat() if task.updated_at else None,
                'executions': execution_list
            }
            result.append(task_data)

        return jsonify({
            'autonomous_tasks': result,
            'total': len(result)
        })

    except Exception as e:
        logger.error(f"获取会话自主任务记录失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'获取会话自主任务记录失败: {str(e)}'}), 500

@conversation_bp.route('/action-tasks/<int:task_id>/conversations/<int:conversation_id>/autonomous-tasks/<int:autonomous_task_id>/stop', methods=['POST'])
def stop_autonomous_task(task_id, conversation_id, autonomous_task_id):
    """停止指定的自主任务"""
    try:
        # 检查会话是否存在且属于该行动任务
        conversation = Conversation.query.get(conversation_id)
        if not conversation or conversation.action_task_id != task_id:
            return jsonify({'error': '会话未找到或不属于该行动任务'}), 404

        # 检查自主任务是否存在且属于该会话
        autonomous_task = AutonomousTask.query.get(autonomous_task_id)
        if not autonomous_task or autonomous_task.conversation_id != conversation_id:
            return jsonify({'error': '自主任务未找到或不属于该会话'}), 404

        # 检查任务是否正在运行
        if autonomous_task.status != 'active':
            return jsonify({'error': f'任务当前状态为{autonomous_task.status}，无法停止'}), 400

        # 根据任务类型调用不同的停止函数
        success = False
        error_message = None

        try:
            if autonomous_task.type == 'discussion':
                # 讨论模式：调用停止自动讨论的函数
                from app.services.conversation.auto_conversation import stop_auto_discussion
                success = stop_auto_discussion(task_id, conversation_id)
                if not success:
                    # 如果停止失败，可能是任务已经结束或不在活动列表中
                    # 这种情况下，我们直接更新数据库状态
                    logger.warning(f"stop_auto_discussion返回False，可能任务已结束，直接更新数据库状态")
                    success = True  # 标记为成功，让后续逻辑更新数据库状态
            elif autonomous_task.type == 'conditional_stop':
                # 条件停止模式：调用停止变量停止会话的函数
                from app.services.conversation.variable_stop_conversation import stop_variable_stop_conversation
                success = stop_variable_stop_conversation(task_id, conversation_id)
                if not success:
                    # 如果停止失败，可能是任务已经结束或不在活动列表中
                    logger.warning(f"stop_variable_stop_conversation返回False，可能任务已结束，直接更新数据库状态")
                    success = True  # 标记为成功，让后续逻辑更新数据库状态
            elif autonomous_task.type == 'variable_trigger':
                # 变量触发模式：TODO - 实现变量触发任务的停止逻辑
                error_message = '变量触发模式暂未实现停止功能'
            elif autonomous_task.type == 'time_trigger':
                # 时间触发模式：调用停止时间触发会话的函数
                from app.services.conversation.time_trigger_conversation import stop_time_trigger_conversation
                success = stop_time_trigger_conversation(task_id, conversation_id)
                if not success:
                    # 如果停止失败，可能是任务已经结束或不在活动列表中
                    logger.warning(f"stop_time_trigger_conversation返回False，可能任务已结束，直接更新数据库状态")
                    success = True  # 标记为成功，让后续逻辑更新数据库状态
            else:
                error_message = f'未知的任务类型: {autonomous_task.type}'

        except Exception as e:
            logger.error(f"调用停止函数失败: {str(e)}")
            error_message = f'停止任务时出错: {str(e)}'

        if success:
            # 注意：对于讨论模式，stop_auto_discussion函数可能已经更新了数据库状态
            # 这里确保状态一致并处理可能的遗漏情况
            try:
                # 刷新任务状态（可能已经被stop_auto_discussion更新了）
                db.session.refresh(autonomous_task)

                # 强制更新状态为停止（无论之前是什么状态）
                if autonomous_task.status == 'active':
                    autonomous_task.status = 'stopped'
                    logger.info(f"手动更新自主任务状态为stopped: {autonomous_task_id}")

                    # 更新执行记录状态
                    latest_execution = AutonomousTaskExecution.query.filter_by(
                        autonomous_task_id=autonomous_task_id,
                        status='running'
                    ).order_by(AutonomousTaskExecution.created_at.desc()).first()

                    if latest_execution:
                        latest_execution.status = 'stopped'
                        latest_execution.end_time = datetime.now()
                        latest_execution.result = {
                            'status': 'stopped',
                            'message': '任务被用户手动停止'
                        }
                        logger.info(f"更新执行记录状态为stopped: {latest_execution.id}")

                    db.session.commit()
                    logger.info(f"数据库状态更新完成")
                else:
                    logger.info(f"任务状态已经是: {autonomous_task.status}")

                return jsonify({
                    'status': 'success',
                    'message': '自主任务已停止',
                    'autonomous_task_id': autonomous_task_id
                })
            except Exception as e:
                logger.error(f"更新任务状态失败: {str(e)}")
                import traceback
                traceback.print_exc()
                return jsonify({
                    'status': 'error',
                    'message': f'任务已停止，但更新状态失败: {str(e)}'
                }), 500
        else:
            return jsonify({
                'status': 'error',
                'message': error_message or '停止任务失败'
            }), 500

    except Exception as e:
        logger.error(f"停止自主任务失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'停止自主任务失败: {str(e)}'}), 500