"""
向量数据库服务抽象层

根据系统设置动态选择不同的向量数据库提供商
支持TiDB、阿里云DashVector等多种提供商
"""

import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from abc import ABC, abstractmethod

from app.services.vector_db.embedding_service import embedding_service

logger = logging.getLogger(__name__)


class VectorDBAdapter(ABC):
    """向量数据库适配器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def create_knowledge_base(self, name: str, dimension: int = 1024, **kwargs) -> Tuple[bool, str, Dict[str, Any]]:
        """创建知识库"""
        pass
    
    @abstractmethod
    def add_documents(self, knowledge_base: str, documents: List[str], 
                     metadatas: Optional[List[Dict[str, Any]]] = None, 
                     source: Optional[str] = None) -> Tuple[bool, str, Dict[str, Any]]:
        """添加文档到知识库"""
        pass
    
    @abstractmethod
    def search(self, knowledge_base: str, query: str, top_k: int = 5, 
              filters: Optional[Dict[str, Any]] = None) -> Tuple[bool, Union[List[Dict[str, Any]], str], Dict[str, Any]]:
        """搜索知识库"""
        pass
    
    @abstractmethod
    def delete_documents(self, knowledge_base: str, document_ids: List[str]) -> Tuple[bool, str, Dict[str, Any]]:
        """删除文档"""
        pass


class TiDBVectorAdapter(VectorDBAdapter):
    """TiDB向量数据库适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self._service = None
        self._initialize()
    
    def _initialize(self):
        """初始化TiDB向量数据库服务"""
        try:
            from app.services.vector_db import tidb_vector_service
            self._service = tidb_vector_service
            
            # 如果有连接字符串配置，初始化服务
            connection_string = self.config.get('connection_string')
            if connection_string:
                success, message = self._service.initialize(connection_string)
                if not success:
                    self.logger.error(f"TiDB向量数据库初始化失败: {message}")
            
        except ImportError as e:
            self.logger.error(f"TiDB向量数据库依赖不可用: {e}")
            self._service = None
    
    def create_knowledge_base(self, name: str, dimension: int = 1024, **kwargs) -> Tuple[bool, str, Dict[str, Any]]:
        """创建知识库"""
        if not self._service:
            return False, "TiDB向量数据库服务不可用", {}
        
        return self._service.create_knowledge_base(name, dimension, **kwargs)
    
    def add_documents(self, knowledge_base: str, documents: List[str], 
                     metadatas: Optional[List[Dict[str, Any]]] = None, 
                     source: Optional[str] = None) -> Tuple[bool, str, Dict[str, Any]]:
        """添加文档到知识库"""
        if not self._service:
            return False, "TiDB向量数据库服务不可用", {}
        
        return self._service.add_documents(knowledge_base, documents, metadatas, source)
    
    def search(self, knowledge_base: str, query: str, top_k: int = 5, 
              filters: Optional[Dict[str, Any]] = None) -> Tuple[bool, Union[List[Dict[str, Any]], str], Dict[str, Any]]:
        """搜索知识库"""
        if not self._service:
            return False, "TiDB向量数据库服务不可用", {}
        
        return self._service.search_knowledge(knowledge_base, query, top_k, filters)
    
    def delete_documents(self, knowledge_base: str, document_ids: List[str]) -> Tuple[bool, str, Dict[str, Any]]:
        """删除文档"""
        if not self._service:
            return False, "TiDB向量数据库服务不可用", {}
        
        return self._service.delete_documents(knowledge_base, document_ids)


class BuiltinVectorAdapter(VectorDBAdapter):
    """内置向量数据库适配器（简单文件存储）"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        # TODO: 实现简单的文件存储向量数据库
    
    def create_knowledge_base(self, name: str, dimension: int = 1024, **kwargs) -> Tuple[bool, str, Dict[str, Any]]:
        """创建知识库"""
        # TODO: 实现内置向量数据库的知识库创建
        return True, f"内置知识库 {name} 创建成功", {'name': name, 'dimension': dimension}
    
    def add_documents(self, knowledge_base: str, documents: List[str], 
                     metadatas: Optional[List[Dict[str, Any]]] = None, 
                     source: Optional[str] = None) -> Tuple[bool, str, Dict[str, Any]]:
        """添加文档到知识库"""
        # TODO: 实现内置向量数据库的文档添加
        return True, f"已添加 {len(documents)} 个文档到知识库 {knowledge_base}", {'count': len(documents)}
    
    def search(self, knowledge_base: str, query: str, top_k: int = 5, 
              filters: Optional[Dict[str, Any]] = None) -> Tuple[bool, Union[List[Dict[str, Any]], str], Dict[str, Any]]:
        """搜索知识库"""
        # TODO: 实现内置向量数据库的搜索
        return True, [], {'query': query, 'results_count': 0}
    
    def delete_documents(self, knowledge_base: str, document_ids: List[str]) -> Tuple[bool, str, Dict[str, Any]]:
        """删除文档"""
        # TODO: 实现内置向量数据库的文档删除
        return True, f"已删除 {len(document_ids)} 个文档", {'deleted_count': len(document_ids)}


class VectorDBAdapterFactory:
    """向量数据库适配器工厂"""
    
    ADAPTERS = {
        'tidb': TiDBVectorAdapter,
        'builtin': BuiltinVectorAdapter,
        # TODO: 添加其他提供商的适配器
        # 'aliyun': AliyunDashVectorAdapter,
        # 'pinecone': PineconeAdapter,
        # 'weaviate': WeaviateAdapter,
    }
    
    @classmethod
    def create_adapter(cls, provider_type: str, config: Dict[str, Any]) -> Optional[VectorDBAdapter]:
        """创建适配器实例"""
        adapter_class = cls.ADAPTERS.get(provider_type)
        if not adapter_class:
            logger.error(f"不支持的向量数据库提供商: {provider_type}")
            return None
        
        try:
            adapter = adapter_class(config)
            logger.info(f"成功创建 {provider_type} 向量数据库适配器")
            return adapter
        except Exception as e:
            logger.error(f"创建 {provider_type} 向量数据库适配器失败: {e}")
            return None
    
    @classmethod
    def get_supported_providers(cls) -> List[str]:
        """获取支持的提供商列表"""
        return list(cls.ADAPTERS.keys())


class VectorDBService:
    """向量数据库统一服务"""
    
    def __init__(self):
        self._adapter = None
        self._initialize()
    
    def _initialize(self):
        """根据系统设置初始化向量数据库适配器"""
        try:
            from flask import has_app_context

            # 检查是否在应用上下文中
            if not has_app_context():
                logger.warning("向量数据库服务需要在Flask应用上下文中初始化")
                self._adapter = None
                return

            from app.models import SystemSetting
            use_builtin = SystemSetting.get('use_builtin_vector_db', True)

            if use_builtin:
                # 使用内置向量数据库
                self._adapter = VectorDBAdapterFactory.create_adapter('builtin', {})
            else:
                # 使用外部向量数据库
                provider = SystemSetting.get('vector_db_provider', 'tidb')

                # 根据提供商获取配置
                config = self._get_provider_config(provider)
                self._adapter = VectorDBAdapterFactory.create_adapter(provider, config)

            if self._adapter:
                logger.info("向量数据库服务初始化成功")
            else:
                logger.error("向量数据库服务初始化失败")

        except Exception as e:
            logger.error(f"向量数据库服务初始化失败: {e}")
            self._adapter = None
    
    def _get_provider_config(self, provider: str) -> Dict[str, Any]:
        """获取提供商配置"""
        from app.models import SystemSetting
        # TODO: 从系统设置或环境变量中获取具体的配置
        if provider == 'tidb':
            return {
                'connection_string': SystemSetting.get('tidb_connection_string', '')
            }
        # TODO: 添加其他提供商的配置获取逻辑
        return {}
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return self._adapter is not None
    
    def create_knowledge_base(self, name: str, dimension: int = 1024, **kwargs) -> Tuple[bool, str, Dict[str, Any]]:
        """创建知识库"""
        if not self._adapter:
            return False, "向量数据库服务不可用", {}
        
        return self._adapter.create_knowledge_base(name, dimension, **kwargs)
    
    def add_documents(self, knowledge_base: str, documents: List[str], 
                     metadatas: Optional[List[Dict[str, Any]]] = None, 
                     source: Optional[str] = None) -> Tuple[bool, str, Dict[str, Any]]:
        """添加文档到知识库"""
        if not self._adapter:
            return False, "向量数据库服务不可用", {}
        
        return self._adapter.add_documents(knowledge_base, documents, metadatas, source)
    
    def search(self, knowledge_base: str, query: str, top_k: int = 5, 
              filters: Optional[Dict[str, Any]] = None) -> Tuple[bool, Union[List[Dict[str, Any]], str], Dict[str, Any]]:
        """搜索知识库"""
        if not self._adapter:
            return False, "向量数据库服务不可用", {}
        
        return self._adapter.search(knowledge_base, query, top_k, filters)
    
    def delete_documents(self, knowledge_base: str, document_ids: List[str]) -> Tuple[bool, str, Dict[str, Any]]:
        """删除文档"""
        if not self._adapter:
            return False, "向量数据库服务不可用", {}
        
        return self._adapter.delete_documents(knowledge_base, document_ids)


# 全局向量数据库服务实例（延迟初始化）
vector_db_service = None

def get_vector_db_service():
    """获取向量数据库服务实例（延迟初始化）"""
    global vector_db_service
    if vector_db_service is None:
        vector_db_service = VectorDBService()
    return vector_db_service
