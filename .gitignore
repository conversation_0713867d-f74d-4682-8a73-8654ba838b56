# Environment variables
#.env
#.env.local
#.env.development.local
#.env.test.local
#.env.production.local
logs/
app.db
frontend/node_modules/.cache/.eslintcache
external-repo-gpustack
agent-workspace/
.pnpm-store/*
test_scripts/*
tests/*

# Node.js / Frontend
node_modules/*
frontend/node_modules/*
frontend/.pnp
frontend/.pnp.js
frontend/build/
frontend/coverage/
frontend/.next/
frontend/out/
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.yarn-integrity

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
#build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
pip-log.txt
pip-delete-this-directory.txt

# Virtual Environment
venv/
env/
ENV/
.venv/
.env/
.ENV/
env.bak/
venv.bak/
.conda/
conda-env/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*~
.*.sw[op]
.project
.classpath
.settings/
.vs/
*.sublime-workspace
*.sublime-project

# Jupyter Notebook
.ipynb_checkpoints
notebooks/

# Coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Database
*.db
*.sqlite3
*.sqlite3-journal
data.db

# Logs
logs/
*.log

# Local folders
instance/
local_data/
private/
testing/
tmp/
temp/

# Large or data files
*.model
*.h5
*.csv
*.json.gz
*.pkl
*.bin
*.jsonl
*.parquet
*.hdf5
*.dat
*.weights
*.pt
*.onnx
*.tflite
data/raw/
data/processed/

# OS specific
Thumbs.db
.DS_Store
.directory
desktop.ini

# Configurations with sensitive information
config.json
secrets.json
credentials.json
settings.local.*

# Docker
.dockerignore
docker-compose.override.yml

# Backup files
*.bak
*.backup
*~
*.swp
*.old
*#

# External utilities
node/
.nvmrc
.node-version
.python-version

# Temporary files
TODO
NOTES
CHANGELOG.local
.scratch/
node_modules
