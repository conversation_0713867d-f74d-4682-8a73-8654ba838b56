import React, { useState, useEffect } from 'react';
import {
  Empty, Button, Modal, Radio, Checkbox, Input, Select,
  message, Spin, List, Tag, Space, Typography, Statistic,
  Divider, Card, Alert, Tooltip
} from 'antd';
import {
  CheckCircleOutlined, CloseCircleOutlined, PlayCircleOutlined,
  InfoCircleOutlined, ExperimentOutlined
} from '@ant-design/icons';
import { actionTaskAPI } from '../../../services/api/actionTask';

const { TextArea } = Input;
const { Text, Title } = Typography;
const { Option } = Select;
const { Group: RadioGroup } = Radio;
const { Group: CheckboxGroup } = Checkbox;

const ActionTaskRules = ({ task }) => {
  // 规则触发数据状态
  const [ruleTriggers, setRuleTriggers] = useState([]);
  const [loadingTriggers, setLoadingTriggers] = useState(false);
  const [triggersPagination, setTriggersPagination] = useState({
    page: 1,
    per_page: 10,
    total: 0,
    pages: 0
  });

  // 手动检查相关状态
  const [checkModalVisible, setCheckModalVisible] = useState(false);
  const [checkScope, setCheckScope] = useState('all'); // 'all' | 'selected'
  const [availableRules, setAvailableRules] = useState([]);
  const [selectedRules, setSelectedRules] = useState([]);
  const [ruleTypeFilter, setRuleTypeFilter] = useState(['llm', 'logic']);
  const [checkScenario, setCheckScenario] = useState('current'); // 'current' | 'custom'
  const [customScenario, setCustomScenario] = useState('');
  const [selectedSupervisor, setSelectedSupervisor] = useState(null);
  const [supervisorAgents, setSupervisorAgents] = useState([]);
  const [checking, setChecking] = useState(false);
  const [checkResults, setCheckResults] = useState([]);
  const [loadingRules, setLoadingRules] = useState(false);

  // 加载规则触发记录
  const loadRuleTriggers = async (page = 1) => {
    if (!task?.id) return;

    setLoadingTriggers(true);
    try {
      const response = await actionTaskAPI.getRuleTriggers(task.id, {
        page,
        per_page: triggersPagination.per_page
      });

      setRuleTriggers(response.triggers || []);
      setTriggersPagination(response.pagination || {});
      console.log('加载规则触发记录成功:', response);
    } catch (error) {
      console.error('加载规则触发记录失败:', error);
      message.error('加载规则触发记录失败');
    } finally {
      setLoadingTriggers(false);
    }
  };

  // 初始化时获取规则数据
  useEffect(() => {
    console.log('ActionTaskRules组件接收到任务数据:', task);
    if (task?.id) {
      loadRuleTriggers();
    }
  }, [task]);

  // 加载任务规则
  const loadTaskRules = async () => {
    if (!task?.id) return;

    setLoadingRules(true);
    try {
      const rules = await actionTaskAPI.getTaskRules(task.id);
      setAvailableRules(rules);
      // 默认选择所有激活的规则
      setSelectedRules(rules.filter(rule => rule.is_active).map(rule => rule.id));
      console.log('加载任务规则成功:', rules);
    } catch (error) {
      message.error('获取任务规则失败: ' + error.message);
    } finally {
      setLoadingRules(false);
    }
  };

  // 加载监督者智能体
  const loadSupervisorAgents = async () => {
    if (!task?.id) return;

    try {
      const agents = await actionTaskAPI.getSupervisorAgents(task.id);
      setSupervisorAgents(agents);
      // 自动选择第一个监督者
      if (agents.length > 0 && !selectedSupervisor) {
        setSelectedSupervisor(agents[0].id);
      }
      console.log('加载监督者智能体成功:', agents);
    } catch (error) {
      console.error('加载监督者智能体失败:', error);
      // 不显示错误消息，因为可能没有监督者
    }
  };

  // 打开手动检查弹窗
  const handleManualCheck = async () => {
    setCheckModalVisible(true);
    setCheckResults([]); // 清空之前的结果

    // 加载规则和监督者数据
    await Promise.all([
      loadTaskRules(),
      loadSupervisorAgents()
    ]);
  };

  // 执行规则检查
  const executeRuleCheck = async () => {
    if (!task?.id) return;

    setChecking(true);
    try {
      // 确定要检查的规则，同时考虑规则类型筛选
      const rulesToCheck = checkScope === 'all'
        ? availableRules.filter(rule => rule.is_active && ruleTypeFilter.includes(rule.type))
        : availableRules.filter(rule => selectedRules.includes(rule.id) && ruleTypeFilter.includes(rule.type));

      if (rulesToCheck.length === 0) {
        message.warning('请选择要检查的规则');
        return;
      }

      // 构建检查上下文
      let context = '';
      if (checkScenario === 'current') {
        if (!task.conversation_id) {
          message.warning('当前没有活动会话，请先创建会话或选择自定义检查场景');
          return;
        }
        context = await actionTaskAPI.buildTaskContext(task.id, task.conversation_id);
      } else {
        context = customScenario;
      }

      if (!context.trim()) {
        message.warning('请提供检查场景描述');
        return;
      }

      // 如果使用当前任务上下文但获取失败，提供默认场景
      if (checkScenario === 'current' && context.includes('无法获取')) {
        context = `默认测试场景：当前时间 ${new Date().toLocaleString()}，正在进行规则合规性检查。`;
        message.info('使用默认测试场景进行检查');
      }

      // 检查自然语言规则是否需要监督者
      const llmRules = rulesToCheck.filter(rule => rule.type === 'llm');
      if (llmRules.length > 0 && !selectedSupervisor) {
        message.warning('检查自然语言规则时必须选择监督者角色');
        return;
      }

      // 分组检查：先逻辑规则，后自然语言规则
      const logicRules = rulesToCheck.filter(rule => rule.type === 'logic');
      const results = [];

      // 检查逻辑规则（逐个）
      for (const rule of logicRules) {
        try {
          const result = await actionTaskAPI.testTaskRules(task.id, [rule], context);
          if (result && result.results && result.results.length > 0) {
            results.push(...result.results);
          } else {
            results.push({
              rule_id: rule.id,
              rule_name: rule.name,
              rule_type: 'logic',
              passed: false,
              message: '规则检查无结果',
              details: '规则测试返回了空结果'
            });
          }
        } catch (error) {
          console.error(`逻辑规则 ${rule.name} 检查失败:`, error);
          results.push({
            rule_id: rule.id,
            rule_name: rule.name,
            rule_type: 'logic',
            passed: false,
            message: '规则检查出错',
            details: error.message || '未知错误'
          });
        }
      }

      // 检查自然语言规则（批量）
      if (llmRules.length > 0) {
        try {
          const result = await actionTaskAPI.testTaskRules(task.id, llmRules, context, selectedSupervisor);
          if (result && result.results && result.results.length > 0) {
            results.push(...result.results);
          } else {
            llmRules.forEach(rule => {
              results.push({
                rule_id: rule.id,
                rule_name: rule.name,
                rule_type: 'llm',
                passed: false,
                message: '规则检查无结果',
                details: '规则测试返回了空结果'
              });
            });
          }
        } catch (error) {
          console.error('自然语言规则检查失败:', error);
          llmRules.forEach(rule => {
            results.push({
              rule_id: rule.id,
              rule_name: rule.name,
              rule_type: 'llm',
              passed: false,
              message: '规则检查出错',
              details: error.message || '未知错误'
            });
          });
        }
      }

      setCheckResults(results);

      // 为每个检查结果创建触发记录
      try {
        for (const result of results) {
          const triggerData = {
            rule_id: result.rule_id,
            conversation_id: task.conversation_id,
            trigger_type: 'manual',
            trigger_source: 'user',
            context: context,
            result: result,
            passed: result.passed,
            message: result.message,
            details: result.details,
            execution_time: result.execution_time
          };

          await actionTaskAPI.createRuleTrigger(task.id, triggerData);
        }

        // 刷新触发记录显示
        loadRuleTriggers();
        console.log('规则触发记录创建成功');
      } catch (error) {
        console.error('创建规则触发记录失败:', error);
        // 不影响主要功能，只记录错误
      }

      // 统计结果
      const passedCount = results.filter(r => r.passed).length;
      const failedCount = results.length - passedCount;

      if (results.length > 0) {
        message.success(`规则检查完成！共检查 ${results.length} 条规则，通过 ${passedCount} 条，未通过 ${failedCount} 条`);
      } else {
        message.warning('没有检查到任何规则，请检查规则配置');
      }
    } catch (error) {
      console.error('规则检查失败:', error);
      message.error('规则检查失败：' + (error.message || '未知错误'));
    } finally {
      setChecking(false);
    }
  };

  // 渲染检查结果统计
  const renderCheckSummary = () => {
    if (checkResults.length === 0) return null;

    const totalRules = checkResults.length;
    const passedRules = checkResults.filter(r => r.passed).length;
    const failedRules = totalRules - passedRules;

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
          <Statistic title="总计" value={totalRules} />
          <Statistic title="通过" value={passedRules} valueStyle={{ color: '#3f8600' }} />
          <Statistic title="未通过" value={failedRules} valueStyle={{ color: '#cf1322' }} />
        </div>
      </Card>
    );
  };

  // 渲染检查结果列表
  const renderCheckResults = () => {
    if (checkResults.length === 0) return null;

    return (
      <List
        dataSource={checkResults}
        renderItem={(result) => (
          <List.Item>
            <List.Item.Meta
              avatar={
                result.passed ?
                  <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} /> :
                  <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />
              }
              title={
                <Space>
                  <Text strong>{result.rule_name}</Text>
                  <Tag color={result.rule_type === 'logic' ? 'blue' : 'green'}>
                    {result.rule_type === 'logic' ? '逻辑规则' : '自然语言规则'}
                  </Tag>
                  <Tag color={result.passed ? 'success' : 'error'}>
                    {result.passed ? '通过' : '未通过'}
                  </Tag>
                </Space>
              }
              description={
                <div>
                  <Text type="secondary">{result.message}</Text>
                  {result.details && (
                    <div style={{ marginTop: 4 }}>
                      <Text style={{ fontSize: '12px' }}>{result.details}</Text>
                    </div>
                  )}
                </div>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  // 渲染规则触发记录
  const renderRuleTriggers = () => {
    if (loadingTriggers) {
      return <Spin tip="加载触发记录..." />;
    }

    if (!ruleTriggers || ruleTriggers.length === 0) {
      return <Empty description="暂无规则触发记录" />;
    }

    return (
      <div>
        <List
          dataSource={ruleTriggers}
          renderItem={(trigger) => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  trigger.passed ?
                    <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} /> :
                    <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />
                }
                title={
                  <Space>
                    <Text strong>{trigger.rule_name}</Text>
                    <Tag color={trigger.rule_type === 'logic' ? 'blue' : 'green'}>
                      {trigger.rule_type === 'logic' ? '逻辑规则' : '自然语言规则'}
                    </Tag>
                    <Tag color={trigger.passed ? 'success' : 'error'}>
                      {trigger.passed ? '通过' : '未通过'}
                    </Tag>
                    <Tag color="default">
                      {trigger.trigger_type === 'manual' ? '手动触发' :
                       trigger.trigger_type === 'automatic' ? '自动触发' : '定时触发'}
                    </Tag>
                  </Space>
                }
                description={
                  <div>
                    <div style={{ marginBottom: 4 }}>
                      <Text type="secondary">{trigger.message}</Text>
                    </div>
                    {trigger.details && (
                      <div style={{ marginBottom: 4 }}>
                        <Text style={{ fontSize: '12px' }}>{trigger.details}</Text>
                      </div>
                    )}
                    <div style={{ fontSize: '12px', color: '#999' }}>
                      <Space split={<span>•</span>}>
                        <span>触发时间: {new Date(trigger.created_at).toLocaleString()}</span>
                        {trigger.execution_time && (
                          <span>执行耗时: {(trigger.execution_time * 1000).toFixed(0)}ms</span>
                        )}
                        {trigger.trigger_source && (
                          <span>触发源: {trigger.trigger_source}</span>
                        )}
                      </Space>
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />

        {/* 分页 */}
        {triggersPagination.pages > 1 && (
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Button.Group>
              <Button
                disabled={!triggersPagination.has_prev}
                onClick={() => loadRuleTriggers(triggersPagination.page - 1)}
              >
                上一页
              </Button>
              <Button disabled>
                {triggersPagination.page} / {triggersPagination.pages}
              </Button>
              <Button
                disabled={!triggersPagination.has_next}
                onClick={() => loadRuleTriggers(triggersPagination.page + 1)}
              >
                下一页
              </Button>
            </Button.Group>
          </div>
        )}
      </div>
    );
  };

  // 渲染规则选择区域
  const renderRuleSelection = () => {
    if (checkScope !== 'selected') return null;

    const filteredRules = availableRules.filter(rule =>
      ruleTypeFilter.includes(rule.type)
    );

    return (
      <div style={{ marginTop: 16 }}>
        <div style={{ marginBottom: 12 }}>
          <Text strong>规则类型筛选：</Text>
          <CheckboxGroup
            value={ruleTypeFilter}
            onChange={setRuleTypeFilter}
            style={{ marginLeft: 8 }}
          >
            <Checkbox value="llm">自然语言规则</Checkbox>
            <Checkbox value="logic">逻辑规则</Checkbox>
          </CheckboxGroup>
        </div>

        <div>
          <Text strong>选择规则：</Text>
          <div style={{ maxHeight: '200px', overflowY: 'auto', marginTop: 8, border: '1px solid #d9d9d9', borderRadius: '6px', padding: '8px' }}>
            <CheckboxGroup
              value={selectedRules}
              onChange={setSelectedRules}
              style={{ width: '100%' }}
            >
              {filteredRules.map(rule => (
                <div key={rule.id} style={{ marginBottom: 8 }}>
                  <Checkbox value={rule.id}>
                    <Space>
                      <Tag color={rule.type === 'logic' ? 'blue' : 'green'} size="small">
                        {rule.type === 'logic' ? '逻辑' : '自然语言'}
                      </Tag>
                      <Text>{rule.name}</Text>
                      {rule.rule_set_name && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          ({rule.rule_set_name})
                        </Text>
                      )}
                    </Space>
                  </Checkbox>
                </div>
              ))}
            </CheckboxGroup>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div>
      {/* 手动检查按钮 */}
      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        <Tooltip title={!task?.id ? "任务信息加载中..." : !task?.conversation_id ? "需要活动会话才能使用当前上下文检查" : "手动检查当前任务的规则合规性"}>
          <Button
            type="primary"
            icon={<ExperimentOutlined />}
            onClick={handleManualCheck}
            disabled={!task?.id}
          >
            手动检查规则
          </Button>
        </Tooltip>
      </div>

      {/* 规则触发记录 */}
      {renderRuleTriggers()}

      {/* 手动检查弹窗 */}
      <Modal
        title={
          <Space>
            <ExperimentOutlined />
            <span>手动检查规则</span>
          </Space>
        }
        open={checkModalVisible}
        onCancel={() => setCheckModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setCheckModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="check"
            type="primary"
            loading={checking}
            onClick={executeRuleCheck}
            disabled={loadingRules}
          >
            开始检查
          </Button>
        ]}
      >
        <Spin spinning={loadingRules} tip="加载规则数据...">
          {/* 检查范围选择 */}
          <div style={{ marginBottom: 16 }}>
            <Text strong>检查范围：</Text>
            <RadioGroup
              value={checkScope}
              onChange={e => setCheckScope(e.target.value)}
              style={{ marginLeft: 8 }}
            >
              <Radio value="all">全部规则 (推荐)</Radio>
              <Radio value="selected">选择特定规则</Radio>
            </RadioGroup>
          </div>

          {/* 规则选择区域 */}
          {renderRuleSelection()}

          {/* 检查场景选择 */}
          <div style={{ marginTop: 16, marginBottom: 16 }}>
            <Text strong>检查场景：</Text>
            <RadioGroup
              value={checkScenario}
              onChange={e => setCheckScenario(e.target.value)}
              style={{ marginLeft: 8 }}
            >
              <Radio value="current" disabled={!task?.conversation_id}>
                使用当前任务上下文
                {!task?.conversation_id && (
                  <Text type="secondary" style={{ fontSize: '12px', marginLeft: 4 }}>
                    (需要活动会话)
                  </Text>
                )}
              </Radio>
              <Radio value="custom">自定义检查场景</Radio>
            </RadioGroup>
          </div>

          {/* 无活动会话提示 */}
          {!task?.conversation_id && (
            <Alert
              message="当前没有活动会话"
              description="要使用当前任务上下文进行规则检查，需要先在左侧创建或选择一个会话。您也可以选择自定义检查场景。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          {/* 自定义场景输入 */}
          {checkScenario === 'custom' && (
            <div style={{ marginBottom: 16 }}>
              <TextArea
                placeholder="请描述要检查的场景..."
                value={customScenario}
                onChange={e => setCustomScenario(e.target.value)}
                rows={4}
              />
            </div>
          )}

          {/* 监督者角色选择 */}
          {availableRules.some(rule => rule.type === 'llm') && (
            <div style={{ marginBottom: 16 }}>
              <Text strong>
                监督者角色：
                <Tooltip title="检查自然语言规则时需要选择监督者角色">
                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                </Tooltip>
              </Text>
              <Select
                placeholder="请选择监督者角色"
                value={selectedSupervisor}
                onChange={setSelectedSupervisor}
                style={{ width: '100%', marginTop: 8 }}
                disabled={supervisorAgents.length === 0}
              >
                {supervisorAgents.map(agent => (
                  <Option key={agent.id} value={agent.id}>
                    {agent.name} ({agent.role_name})
                  </Option>
                ))}
              </Select>
              {supervisorAgents.length === 0 && (
                <Alert
                  message="当前任务没有配置监督者，无法检查自然语言规则"
                  type="warning"
                  showIcon
                  style={{ marginTop: 8 }}
                />
              )}
            </div>
          )}

          {/* 检查结果 */}
          {checkResults.length > 0 && (
            <div style={{ marginTop: 24 }}>
              <Divider>检查结果</Divider>
              {renderCheckSummary()}
              {renderCheckResults()}

              {/* 结果说明 */}
              <Alert
                message="检查结果说明"
                description={
                  <div>
                    <p>• <strong>逻辑规则</strong>：通过JavaScript引擎执行，结果基于代码逻辑</p>
                    <p>• <strong>自然语言规则</strong>：通过AI模型评估，结果基于语义理解</p>
                    <p>• 规则中的变量占位符（如 <code>{`{{variable_name}}`}</code>）会自动替换为任务实际变量值</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginTop: 16 }}
              />
            </div>
          )}
        </Spin>
      </Modal>
    </div>
  );
};

export default ActionTaskRules;