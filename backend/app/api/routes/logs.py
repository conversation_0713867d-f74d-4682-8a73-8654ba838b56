"""
日志文件API路由

处理与系统日志文件相关的所有API请求
"""
import os
from flask import Blueprint, jsonify, request, current_app
import logging

# 创建Blueprint
logs_bp = Blueprint('logs_api', __name__)

# 设置日志
logger = logging.getLogger(__name__)

@logs_bp.route('/logs', methods=['GET'])
def get_logs():
    """获取系统日志文件内容"""
    try:
        # 获取查询参数
        max_lines = request.args.get('max_lines', default=1000, type=int)
        start_line = request.args.get('start_line', default=0, type=int)
        
        # 日志文件路径
        log_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'logs', 'app.log')
        
        # 检查文件是否存在
        if not os.path.exists(log_file_path):
            return jsonify({
                'status': 'error',
                'message': '日志文件不存在'
            }), 404
        
        # 获取文件大小
        file_size = os.path.getsize(log_file_path)
        
        # 读取日志文件内容
        lines = []
        total_lines = 0
        
        with open(log_file_path, 'r', encoding='utf-8') as f:
            # 如果需要跳过前面的行
            if start_line > 0:
                for _ in range(start_line):
                    next(f, None)
            
            # 读取指定行数
            for i, line in enumerate(f):
                if i >= max_lines:
                    break
                lines.append(line.rstrip())
                total_lines += 1
        
        # 获取文件总行数
        with open(log_file_path, 'r', encoding='utf-8') as f:
            all_lines_count = sum(1 for _ in f)
        
        return jsonify({
            'status': 'success',
            'data': {
                'file_path': log_file_path,
                'file_size': file_size,
                'total_lines': all_lines_count,
                'start_line': start_line,
                'lines_count': total_lines,
                'content': lines
            }
        })
    
    except Exception as e:
        logger.error(f"获取日志文件内容失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取日志文件内容失败: {str(e)}'
        }), 500

@logs_bp.route('/logs/tail', methods=['GET'])
def tail_logs():
    """获取日志文件的最后几行"""
    try:
        # 获取查询参数
        lines_count = request.args.get('lines', default=100, type=int)
        
        # 日志文件路径
        log_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'logs', 'app.log')
        
        # 检查文件是否存在
        if not os.path.exists(log_file_path):
            return jsonify({
                'status': 'error',
                'message': '日志文件不存在'
            }), 404
        
        # 获取文件大小
        file_size = os.path.getsize(log_file_path)
        
        # 读取日志文件的最后几行
        lines = []
        
        with open(log_file_path, 'r', encoding='utf-8') as f:
            # 使用deque实现高效的tail操作
            from collections import deque
            lines = deque(f, lines_count)
            lines = list(lines)
            
            # 去除每行末尾的换行符
            lines = [line.rstrip() for line in lines]
        
        # 获取文件总行数
        with open(log_file_path, 'r', encoding='utf-8') as f:
            all_lines_count = sum(1 for _ in f)
        
        return jsonify({
            'status': 'success',
            'data': {
                'file_path': log_file_path,
                'file_size': file_size,
                'total_lines': all_lines_count,
                'lines_count': len(lines),
                'content': lines
            }
        })
    
    except Exception as e:
        logger.error(f"获取日志文件尾部内容失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取日志文件尾部内容失败: {str(e)}'
        }), 500
