import React, { useState } from 'react';
import { Modal, Form, Input, Button, message } from 'antd';
import { LockOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { authAPI } from '../../services/api/auth';

/**
 * 修改密码Modal组件
 */
const ChangePasswordModal = ({ visible, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  // 处理表单提交
  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const result = await authAPI.changePassword(values.newPassword);
      
      if (result.success) {
        message.success(result.message);
        form.resetFields();
        onSuccess && onSuccess();
        onCancel();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error('修改密码失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    setPasswordVisible(false);
    setConfirmPasswordVisible(false);
    onCancel();
  };

  // 密码验证规则
  const passwordRules = [
    { required: true, message: '请输入新密码' },
    { min: 6, message: '密码长度不能少于6位' },
    { max: 50, message: '密码长度不能超过50位' }
  ];

  // 确认密码验证规则
  const confirmPasswordRules = [
    { required: true, message: '请确认新密码' },
    ({ getFieldValue }) => ({
      validator(_, value) {
        if (!value || getFieldValue('newPassword') === value) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('两次输入的密码不一致'));
      },
    }),
  ];

  return (
    <Modal
      title="修改密码"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={400}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        autoComplete="off"
      >
        <Form.Item
          name="newPassword"
          label="新密码"
          rules={passwordRules}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入新密码"
            visibilityToggle={{
              visible: passwordVisible,
              onVisibleChange: setPasswordVisible,
            }}
            iconRender={(visible) => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          label="确认新密码"
          rules={confirmPasswordRules}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请再次输入新密码"
            visibilityToggle={{
              visible: confirmPasswordVisible,
              onVisibleChange: setConfirmPasswordVisible,
            }}
            iconRender={(visible) => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, marginTop: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
            <Button onClick={handleCancel}>
              取消
            </Button>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
            >
              确认修改
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ChangePasswordModal;
