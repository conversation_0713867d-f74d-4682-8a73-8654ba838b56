import api from './axios';
import { validateApiUrl, urlContainsPath } from './validation';

/**
 * 模型配置相关API服务
 */
export const modelConfigAPI = {
  // 获取所有模型配置
  getAll: async (includeKeys = false) => {
    console.log('获取所有模型配置，includeKeys =', includeKeys);
    const response = await api.get(`/model-configs${includeKeys ? '?include_api_keys=true' : ''}`);
    return response.data.model_configs || [];
  },

  // 获取单个模型配置
  getById: async (id, includeKeys = false) => {
    console.log('获取单个模型配置，id =', id, 'includeKeys =', includeKeys);
    const response = await api.get(`/model-configs/${id}${includeKeys ? '?include_api_keys=true' : ''}`);
    return response.data;
  },

  // 创建模型配置
  create: async (configData) => {
    const response = await api.post('/model-configs', configData);
    return response.data;
  },

  // 更新模型配置
  update: async (id, configData) => {
    const response = await api.put(`/model-configs/${id}`, configData);
    return response.data;
  },

  // 删除模型配置
  delete: async (id) => {
    const response = await api.delete(`/model-configs/${id}`);
    return response.data;
  },

  // 设置默认模型（保留向后兼容）
  setDefault: async (id) => {
    const response = await api.post(`/model-configs/${id}/set-default`);
    return response.data;
  },

  // 设置默认模型（支持分别设置文本生成和嵌入模型）
  setDefaults: async (textModelId, embeddingModelId) => {
    const data = {};
    if (textModelId) data.text_model_id = textModelId;
    if (embeddingModelId) data.embedding_model_id = embeddingModelId;

    const response = await api.post('/model-configs/set-defaults', data);
    return response.data;
  },

  // 获取当前默认模型
  getDefaults: async () => {
    const response = await api.get('/model-configs/defaults');
    return response.data;
  },

  // 测试模型配置
  testModel: async (modelId, prompt) => {
    try {
      // 获取模型配置，包括API密钥
      const configResponse = await api.get(`/model-configs/${modelId}?include_api_keys=true`);
      const modelConfig = configResponse.data;

      // 如果模型配置有API基础URL和API密钥，使用API配置
      if (modelConfig.base_url && modelConfig.api_key) {
        // 调用模型API
        return await modelConfigAPI.llmApiTest(modelConfig, prompt);
      } else {
        // 通过后端转发请求
        const response = await api.post(`/model-configs/${modelId}/test`, { prompt });
        const data = response.data;

        // 如果没有response字段，则从message字段提取
        if (data.success && !data.response && data.message && data.message.includes('测试成功:')) {
          const messageParts = data.message.split('测试成功:');
          if (messageParts.length > 1) {
            let extractedResponse = messageParts[1].trim();
            // 移除末尾的省略号
            if (extractedResponse.endsWith('...')) {
              extractedResponse = extractedResponse.substring(0, extractedResponse.length - 3);
            }
            data.response = extractedResponse;
          }
        }

        return data;
      }
    } catch (error) {
      console.error(`测试模型(ID: ${modelId})失败:`, error);
      throw error;
    }
  },

  // 测试聊天功能
  chatTest: async (modelId, prompt, systemPrompt = '', parameters = {}) => {
    try {
      console.log('测试聊天，modelId =', modelId, '提示 =', prompt);

      // 尝试通过API测试
      const response = await api.post(`/model-configs/${modelId}/chat-test`, {
        prompt,
        system_prompt: systemPrompt,
        parameters
      });

      if (response.data && response.data.text) {
        return {
          success: true,
          text: response.data.text
        };
      } else if (response.data && response.data.error) {
        return {
          success: false,
          error: response.data.error
        };
      }

      // 如果API测试失败，抛出错误
      throw new Error('API测试失败，无法获取响应');
    } catch (error) {
      console.error('测试聊天失败:', error);

      // 返回错误信息
      return {
        success: false,
        error: error.message || '测试失败，请检查网络连接和模型配置'
      };
    }
  },

  // LLM API测试函数
  llmApiTest: async (modelConfig, prompt, systemPrompt = "You are a helpful assistant.") => {
    // 实现模型API请求
    try {
      // 检查并修正可能的问题
      const base_url = validateApiUrl(modelConfig.base_url || modelConfig.baseUrl || modelConfig.url || modelConfig.endpoint || '');
      const provider = modelConfig.provider || '';

      console.log('LLM API测试 - 基础URL:', base_url);
      console.log('LLM API测试 - 提供商:', provider);

      if (!base_url) {
        console.error('模型配置缺少URL:', modelConfig);
        throw new Error('模型配置中缺少有效的基础URL (base_url/baseUrl/url/endpoint)');
      }

      let apiUrl;
      let headers = {
        'Content-Type': 'application/json'
      };

      // 根据提供商设置授权头
      if (modelConfig.api_key) {
        if (provider.toLowerCase() === 'anthropic') {
          // Anthropic API使用x-api-key头部
          headers['x-api-key'] = modelConfig.api_key;
          headers['anthropic-version'] = '2023-06-01';
          console.log('LLM API测试 - 已添加Anthropic授权头');
        } else {
          // 其他API使用Bearer token
          headers['Authorization'] = `Bearer ${modelConfig.api_key}`;
          console.log('LLM API测试 - 已添加Bearer授权头');
        }
      } else {
        console.log('LLM API测试 - 无API密钥，未添加授权头');
      }

      let payload;

      // 获取附加参数
      let additionalParams = {};
      try {
        additionalParams = modelConfig.additional_params || {};
        if (typeof additionalParams === 'string') {
          additionalParams = JSON.parse(additionalParams);
        }
      } catch (e) {
        console.warn("无法解析附加参数:", e);
      }

      // 根据不同提供商准备API请求
      if (provider.toLowerCase() === 'openai') {
        // 检查base_url是否已经包含了/v1/chat/completions路径
        if (urlContainsPath(base_url, '/v1/chat/completions')) {
          apiUrl = base_url;
        } else if (urlContainsPath(base_url, '/v1')) {
          apiUrl = `${base_url}/chat/completions`;
        } else {
          apiUrl = `${base_url}/v1/chat/completions`;
        }

        console.log('OpenAI构建的API URL:', apiUrl);

        payload = {
          model: modelConfig.model_id,
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: prompt }
          ],
          temperature: modelConfig.temperature || 0.7,
          max_tokens: modelConfig.max_output_tokens || 2000,
          ...additionalParams
        };
      } else if (provider.toLowerCase() === 'anthropic') {
        // 检查base_url是否已经包含了/v1/messages路径
        if (urlContainsPath(base_url, '/v1/messages')) {
          apiUrl = base_url;
        } else if (urlContainsPath(base_url, '/v1')) {
          apiUrl = `${base_url}/messages`;
        } else {
          apiUrl = `${base_url}/v1/messages`;
        }

        console.log('Anthropic构建的API URL:', apiUrl);

        // Claude API的正确格式：system作为顶级参数，messages中只包含user和assistant
        payload = {
          model: modelConfig.model_id,
          system: systemPrompt,
          messages: [
            { role: "user", content: prompt }
          ],
          temperature: modelConfig.temperature || 0.7,
          max_tokens: modelConfig.max_output_tokens || 2000
        };
      } else if (provider.toLowerCase() === 'ollama') {
        // 使用OpenAI兼容端点
        if (urlContainsPath(base_url, '/v1/chat/completions')) {
          apiUrl = base_url;
        } else if (urlContainsPath(base_url, '/api/chat')) {
          // 如果用户提供了旧的API路径，替换为新的兼容路径
          apiUrl = base_url.replace('/api/chat', '/v1/chat/completions');
        } else {
          apiUrl = `${base_url}/v1/chat/completions`;
        }

        console.log('Ollama构建的API URL:', apiUrl);

        payload = {
          model: modelConfig.model_id,
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: prompt }
          ],
          temperature: modelConfig.temperature || 0.7,
          max_tokens: modelConfig.max_output_tokens || 2000
        };
      } else {
        // 自定义或未知提供商
        console.log(`使用通用格式处理未知提供商: ${provider}`);

        // 根据附加参数中的配置确定API URL
        if (additionalParams.api_path) {
          // 检查base_url是否已经包含了api_path
          if (urlContainsPath(base_url, additionalParams.api_path)) {
            apiUrl = base_url;
          } else {
            apiUrl = `${base_url}${additionalParams.api_path}`;
          }
        } else if (additionalParams.endpoint) {
          // 检查base_url是否已经包含了endpoint
          if (urlContainsPath(base_url, additionalParams.endpoint)) {
            apiUrl = base_url;
          } else {
            apiUrl = `${base_url}${additionalParams.endpoint}`;
          }
        } else {
          // 默认路径 - 常见的聊天完成路径
          // 检查base_url是否已经包含了/v1/chat/completions路径
          if (urlContainsPath(base_url, '/chat/completions') || urlContainsPath(base_url, '/v1/chat/completions')) {
            apiUrl = base_url;
          } else if (urlContainsPath(base_url, '/v1')) {
            apiUrl = `${base_url}/chat/completions`;
          } else {
            apiUrl = `${base_url}/chat/completions`;
          }
        }

        // 构建基本负载
        payload = {
          model: modelConfig.model_id,
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: prompt }
          ],
          temperature: modelConfig.temperature || 0.7,
          max_tokens: modelConfig.max_output_tokens || 2000,
          ...additionalParams.payload_defaults  // 合并附加参数中的默认值
        };

        // 使用指定的负载结构
        if (additionalParams.payload_structure) {
          const structure = additionalParams.payload_structure;
          if (structure === 'openai') {
            // 已经使用了OpenAI格式，不需要修改
          } else if (structure === 'anthropic') {
            payload = {
              model: modelConfig.model_id,
              prompt: `\n\nHuman: ${prompt}\n\nAssistant:`,
              temperature: 0.7,
              max_tokens_to_sample: modelConfig.max_output_tokens || 2000,
              ...additionalParams.payload_defaults
            };
          } else if (structure === 'custom') {
            // 使用完全自定义的负载结构
            try {
              if (additionalParams.custom_payload) {
                const customPayload = additionalParams.custom_payload;
                // 替换模板中的变量
                const payloadStr = JSON.stringify(customPayload)
                  .replace(/"{{prompt}}"/g, JSON.stringify(prompt))
                  .replace(/"{{model}}"/g, JSON.stringify(modelConfig.model_id))
                  .replace(/"{{max_tokens}}"/g, JSON.stringify(modelConfig.max_output_tokens || 2000));

                payload = JSON.parse(payloadStr);
              }
            } catch (e) {
              console.error("构建自定义负载失败:", e);
            }
          }
        }
      }

      // 添加调试日志
      console.log('测试LLM API URL:', apiUrl);
      console.log('测试LLM API请求头:', {...headers, Authorization: headers.Authorization ? '***已隐藏***' : undefined});
      console.log('测试LLM API负载:', payload);

      // 获取前端URL，用于检查是否错误地发送到前端服务器
      const frontendUrl = process.env.REACT_APP_FRONTEND_URL || 'http://localhost:3000';

      // 确保不是发送到前端服务器或其他无效URL
      if (apiUrl.includes(frontendUrl) || apiUrl === '/chat' || apiUrl === '/api/chat') {
        console.error('警告: 检测到请求发送到前端服务器URL，这可能是错误的配置:', apiUrl);
        throw new Error(`URL配置错误，不应发送到 ${apiUrl}，请检查模型配置的base_url`);
      }

      // 发送非流式请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(payload)
      });

      console.log('LLM API响应状态:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('LLM API错误响应:', errorText);
        throw new Error(`API返回错误: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      console.log('LLM API响应数据:', data);

      let content = "";

      // 提取不同API格式的响应内容
      const responseFormat = additionalParams.response_format || 'auto';

      if (provider.toLowerCase() === 'openai' ||
          (responseFormat === 'openai' ||
          (responseFormat === 'auto' && data.choices))) {
        // OpenAI格式处理
        if (data.choices && data.choices[0].message) {
          content = data.choices[0].message.content;
        }
      } else if (provider.toLowerCase() === 'ollama' ||
                (responseFormat === 'ollama' ||
                (responseFormat === 'auto' && data.message))) {
        // Ollama格式处理
        if (data.message && data.message.content) {
          content = data.message.content;
        }
      } else if (provider.toLowerCase() === 'anthropic' ||
                (responseFormat === 'anthropic' ||
                (responseFormat === 'auto' && data.content))) {
        // Anthropic Messages API格式处理
        if (data.content && Array.isArray(data.content) && data.content.length > 0) {
          // 提取第一个text类型的内容块
          const textContent = data.content.find(block => block.type === 'text');
          if (textContent && textContent.text) {
            content = textContent.text;
          }
        } else if (data.completion) {
          // 兼容旧版Anthropic API格式
          content = data.completion;
        }
      } else if (responseFormat === 'custom' && additionalParams.response_path) {
        // 自定义响应路径处理
        try {
          // 使用点表示法路径获取响应值
          const path = additionalParams.response_path.split('.');
          let value = data;
          for (const key of path) {
            if (value[key] !== undefined) {
              value = value[key];
            } else {
              value = null;
              break;
            }
          }
          if (value && typeof value === 'string') {
            content = value;
          } else if (value && typeof value === 'object') {
            content = JSON.stringify(value);
          }
        } catch (e) {
          console.error("获取自定义响应路径值失败:", e);
        }
      } else {
        // 尝试直接找到可能的内容字段
        console.log("尝试自动识别响应格式:", data);
        // 优先检查常见的内容字段名
        const commonContentFields = ['content', 'text', 'message', 'response', 'result', 'generated_text', 'answer'];
        for (const field of commonContentFields) {
          if (data[field]) {
            if (typeof data[field] === 'string') {
              content = data[field];
              break;
            } else if (typeof data[field] === 'object' && data[field].content) {
              content = data[field].content;
              break;
            }
          }
        }

        // 如果仍未找到内容，作为最后手段，转换整个响应为字符串
        if (!content) {
          console.warn("无法识别响应格式，使用整个响应作为内容");
          try {
            content = JSON.stringify(data);
          } catch (e) {
            console.error("转换响应为字符串失败:", e);
            content = "[响应格式无法解析]";
          }
        }
      }

      return {
        success: true,
        response: content,
        message: '模型测试成功'
      };

    } catch (error) {
      console.error('模型测试失败:', error);
      return {
        success: false,
        response: "",
        message: `模型测试失败: ${error.message}`
      };
    }
  },

  // 获取可用的模型配置选项
  getModelConfigs: async () => {
    const response = await api.get('/model-configs/options');
    return response.data.options || [];
  },

  // 运行模型诊断
  diagnoseCorsIssue: async (baseUrl, options = {}) => {
    // 实现CORS问题诊断逻辑
    try {
      // 尝试发送一个简单的请求来检测CORS问题
      const testUrl = baseUrl || api.defaults.baseURL;
      const response = await fetch(`${testUrl}/health-check`, {
        method: 'GET',
        mode: 'cors',
        ...options
      });

      if (response.ok) {
        return { success: true, corsStatus: "可正常访问" };
      } else {
        throw new Error(`HTTP错误: ${response.status}`);
      }
    } catch (error) {
      return { success: false, corsStatus: "存在CORS限制", error: error.message };
    }
  },

  // 使用SSE测试模型配置
  testModelStream: async (modelId, prompt, onChunkReceived = null, systemPrompt = "You are a helpful assistant.", advancedParams = {}) => {
    try {
      console.log(`[ModelConfigAPI] 开始SSE流式测试: 模型ID=${modelId}, 提示="${prompt?.substring(0, 30)}..."`);

      const url = `${api.defaults.baseURL}/model-configs/${modelId}/test-stream`;

      // 基本参数
      const payload = {
        prompt: prompt || 'Hello, can you introduce yourself?',
        system_prompt: systemPrompt || 'You are a helpful assistant.'
      };

      // 添加高级参数
      if (advancedParams) {
        // 检查并添加各个高级参数
        if (advancedParams.temperature !== undefined) payload.temperature = advancedParams.temperature;
        if (advancedParams.top_p !== undefined) payload.top_p = advancedParams.top_p;
        if (advancedParams.frequency_penalty !== undefined) payload.frequency_penalty = advancedParams.frequency_penalty;
        if (advancedParams.presence_penalty !== undefined) payload.presence_penalty = advancedParams.presence_penalty;
        if (advancedParams.max_tokens !== undefined) payload.max_tokens = advancedParams.max_tokens;
        if (advancedParams.stop_sequences !== undefined) payload.stop = advancedParams.stop_sequences;
      }

      console.log('[ModelConfigAPI] 发送请求体:', payload);

      // 发送POST请求，获取SSE流响应
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`流式请求失败(${response.status}): ${errorText}`);
      }

      console.log('[ModelConfigAPI] SSE连接已建立, 开始接收数据流');

      // 读取流
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let buffer = '';
      let fullResponse = '';

      // 读取启动消息已通知UI
      if (onChunkReceived) {
        onChunkReceived(null, { connectionStatus: 'connected' });
      }

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // 使用UTF-8解码二进制数据
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 处理缓冲区中的SSE消息
        const messages = buffer.split('\n\n');
        buffer = messages.pop() || ''; // 保留最后一个可能不完整的消息

        for (const message of messages) {
          if (!message || message.trim() === '') continue;

          // 检查是否为注释或心跳
          if (message.startsWith(':')) continue;

          // 处理数据消息
          if (message.startsWith('data:')) {
            const dataContent = message.slice(5).trim();

            // 检查是否为结束标记
            if (dataContent === '[DONE]') {
              console.log('[ModelConfigAPI] 收到结束标记');
              if (onChunkReceived) {
                onChunkReceived(null, { connectionStatus: 'done' });
              }
              continue;
            }

            try {
              // 解析JSON数据
              const jsonData = JSON.parse(dataContent);

              // 处理状态消息
              if (jsonData.status) {
                console.log(`[ModelConfigAPI] 状态更新: ${jsonData.status}`);
                if (onChunkReceived) {
                  onChunkReceived(null, {
                    connectionStatus: jsonData.status,
                    message: jsonData.message || ''
                  });
                }
                continue;
              }

              // 处理错误消息
              if (jsonData.error) {
                console.error(`[ModelConfigAPI] 错误: ${jsonData.error}`);
                if (onChunkReceived) {
                  onChunkReceived(null, {
                    connectionStatus: 'error',
                    error: jsonData.error
                  });
                }
                continue;
              }

              // 处理内容块
              if (jsonData.choices && jsonData.choices[0].delta && jsonData.choices[0].delta.content !== undefined) {
                const content = jsonData.choices[0].delta.content;
                // 过滤掉null、undefined和'null'字符串，但允许空字符串
                if (onChunkReceived && content !== null && content !== undefined && content !== 'null' && content !== 'undefined') {
                  console.log(`[ModelConfigAPI] 收到内容块: "${content.substring(0, 30)}${content.length > 30 ? '...' : ''}" (${content.length}字符)`);

                  // 更新全部响应
                  fullResponse += content;

                  // 立即通知前端更新UI，不使用setTimeout
                  onChunkReceived(content, { connectionStatus: 'active' });
                }
              }
            } catch (e) {
              console.error(`[ModelConfigAPI] 解析SSE数据错误: ${e.message}`, dataContent);
            }
          }
        }
      }

      console.log(`[ModelConfigAPI] SSE流结束，总响应长度: ${fullResponse.length}`);
      return { success: true, response: fullResponse };

    } catch (error) {
      console.error('[ModelConfigAPI] 测试模型流异常:', error);
      if (onChunkReceived) {
        onChunkReceived(null, {
          connectionStatus: 'error',
          error: error.message
        });
      }
      return {
        success: false,
        message: `流式测试失败: ${error.message}`
      };
    }
  },

  // 获取单个模型配置详情
  getModelById: async (modelId) => {
    try {
      console.log(`[ModelConfigAPI] 获取模型配置详情: ID=${modelId}`);
      const response = await api.get(`/model-configs/${modelId}`);
      return response.data;
    } catch (error) {
      console.error(`[ModelConfigAPI] 获取模型配置详情失败: ${error.message}`);
      throw error;
    }
  },

  // 获取GPUStack模型列表
  fetchGpustackModels: async (baseUrl, apiKey) => {
    try {
      console.log(`[ModelConfigAPI] 获取GPUStack模型列表: baseUrl=${baseUrl}`);
      const response = await api.post('/model-configs/gpustack/models', {
        base_url: baseUrl,
        api_key: apiKey
      });
      return response.data;
    } catch (error) {
      console.error(`[ModelConfigAPI] 获取GPUStack模型列表失败: ${error.message}`);
      throw error;
    }
  },

  // 获取Ollama模型列表
  fetchOllamaModels: async (baseUrl) => {
    try {
      console.log(`[ModelConfigAPI] 获取Ollama模型列表: baseUrl=${baseUrl}`);
      const response = await api.post('/model-configs/ollama/models', {
        base_url: baseUrl
      });
      return response.data;
    } catch (error) {
      console.error(`[ModelConfigAPI] 获取Ollama模型列表失败: ${error.message}`);
      throw error;
    }
  },

  // 获取Anthropic模型列表
  fetchAnthropicModels: async (baseUrl, apiKey) => {
    try {
      console.log(`[ModelConfigAPI] 获取Anthropic模型列表: baseUrl=${baseUrl}`);
      const response = await api.post('/model-configs/anthropic/models', {
        base_url: baseUrl,
        api_key: apiKey
      });
      return response.data;
    } catch (error) {
      console.error(`[ModelConfigAPI] 获取Anthropic模型列表失败: ${error.message}`);
      throw error;
    }
  },

  // 获取Google模型列表
  fetchGoogleModels: async (baseUrl, apiKey) => {
    try {
      console.log(`[ModelConfigAPI] 获取Google模型列表: baseUrl=${baseUrl}`);
      const response = await api.post('/model-configs/google/models', {
        base_url: baseUrl,
        api_key: apiKey
      });
      return response.data;
    } catch (error) {
      console.error(`[ModelConfigAPI] 获取Google模型列表失败: ${error.message}`);
      throw error;
    }
  },

  // 获取X.ai模型列表
  fetchXaiModels: async (baseUrl, apiKey) => {
    try {
      console.log(`[ModelConfigAPI] 获取X.ai模型列表: baseUrl=${baseUrl}`);
      const response = await api.post('/model-configs/xai/models', {
        base_url: baseUrl,
        api_key: apiKey
      });
      return response.data;
    } catch (error) {
      console.error(`[ModelConfigAPI] 获取X.ai模型列表失败: ${error.message}`);
      throw error;
    }
  },

  // 测试模型服务连接
  testConnection: async (baseUrl, provider, apiKey = '') => {
    try {
      console.log(`[ModelConfigAPI] 测试连接: baseUrl=${baseUrl}, provider=${provider}`);
      const response = await api.post('/model-configs/test-connection', {
        base_url: baseUrl,
        provider: provider,
        api_key: apiKey
      });
      return response.data;
    } catch (error) {
      console.error(`[ModelConfigAPI] 连接测试失败: ${error.message}`);
      throw error;
    }
  }
};