"""
消息API路由

处理与消息相关的所有API请求
"""
from flask import Blueprint, request, jsonify
from app.models import Message, Agent, db

# 创建Blueprint
message_bp = Blueprint('message_api', __name__)

@message_bp.route('/messages/<int:message_id>', methods=['GET'])
def get_message(message_id):
    """获取特定消息详情"""
    message = Message.query.get(message_id)
    if not message:
        return jsonify({'error': '消息未找到'}), 404

    result = {
        'id': message.id,
        'content': message.content,
        'role': message.role,
        'action_task_id': message.action_task_id,
        'created_at': message.created_at.isoformat() if message.created_at else None
    }

    # 对于智能体消息，添加智能体信息
    if message.role == 'agent' and message.agent_id:
        agent = Agent.query.get(message.agent_id)
        if agent:
            result['agent'] = {
                'id': agent.id,
                'name': agent.name,
                'description': agent.description
            }

    return jsonify(result)

@message_bp.route('/messages/<int:message_id>', methods=['PUT'])
def update_message(message_id):
    """更新消息内容"""
    message = Message.query.get(message_id)
    if not message:
        return jsonify({'error': '消息未找到'}), 404

    data = request.get_json()

    # 更新消息内容
    if 'content' in data:
        message.content = data['content']

    db.session.commit()

    return jsonify({
        'message': '消息更新成功',
        'id': message.id
    })

@message_bp.route('/messages/<int:message_id>', methods=['DELETE'])
def delete_message(message_id):
    """删除消息"""
    message = Message.query.get(message_id)
    if not message:
        return jsonify({'error': '消息未找到'}), 404

    db.session.delete(message)
    db.session.commit()

    return jsonify({'message': '消息已删除'})