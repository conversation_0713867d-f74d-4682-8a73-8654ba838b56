version: '3.8'

services:
  # ABM-LLM-V2 应用服务
  abm-app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: abm-app
    ports:
      - "8080:8080"  # 后端API
      - "3000:3000"  # 前端应用
    environment:
      - PYTHONIOENCODING=utf-8
      - LC_ALL=C.UTF-8
      - LANG=C.UTF-8
      - FLASK_ENV=production
      - NODE_ENV=development
    volumes:
      - ../data:/app/data
      - ../logs:/app/logs
      - ../config.conf:/app/config.conf
      - ../mcp_config.json:/app/mcp_config.json
      - ../agent-workspace:/app/agent-workspace
      - ../third_mcp_server:/app/third_mcp_server
      - ../frontend/src:/app/frontend/src
      - ../frontend/public:/app/frontend/public
    working_dir: /app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
