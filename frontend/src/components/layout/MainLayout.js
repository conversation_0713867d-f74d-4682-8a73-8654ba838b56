import React, { useState } from 'react';
import { Layout, Menu, Typo<PERSON>, <PERSON>, Col, Space, Avatar, Button, theme, Dropdown, App } from 'antd';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import ChangePasswordModal from '../auth/ChangePasswordModal';
import {
  MessageOutlined,
  TeamOutlined,
  GlobalOutlined,
  SettingOutlined,
  DashboardOutlined,
  DesktopOutlined,
  ApiOutlined,
  BugOutlined,
  FileTextOutlined,
  SafetyCertificateOutlined,
  UserOutlined,
  DatabaseOutlined,
  ToolOutlined,
  RobotOutlined,
  PartitionOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  HomeOutlined,
  MonitorOutlined,
  AppstoreOutlined,
  ApartmentOutlined,
  EnvironmentOutlined,
  BarChartOutlined,
  OrderedListOutlined,
  FileSearchOutlined,
  ScheduleOutlined,
  FundOutlined,
  ProfileOutlined,
  BookOutlined,
  FolderOutlined,
  ControlOutlined,
  LinkOutlined,
  InfoCircleOutlined,
  LogoutOutlined,
  CodeOutlined,
  LockOutlined
} from '@ant-design/icons';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

const MainLayout = ({ children }) => {
  const { message } = App.useApp();
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [collapsed, setCollapsed] = useState(false);
  const [changePasswordVisible, setChangePasswordVisible] = useState(false);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  // 处理登出
  const handleLogout = async () => {
    try {
      await logout();
      message.success('已成功登出');
      navigate('/login');
    } catch (error) {
      console.error('登出失败:', error);
      message.error('登出失败，请稍后再试');
    }
  };

  // 处理修改密码
  const handleChangePassword = () => {
    setChangePasswordVisible(true);
  };

  // 修改密码成功回调
  const handleChangePasswordSuccess = () => {
    message.success('密码修改成功');
  };

  const menuItems = [
    {
      key: 'action-tasks',
      icon: <DashboardOutlined style={{ fontSize: '18px' }} />,
      label: <span>行动中心</span>,
      children: [
        {
          key: '/home',
          icon: <HomeOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/home">系统概览</Link>,
        },
        {
          key: '/action-tasks/overview',
          icon: <OrderedListOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/action-tasks/overview">任务管理</Link>,
        },
        {
          key: '/action-tasks/analysis',
          icon: <FundOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/action-tasks/analysis">任务分析</Link>,
        },
        {
          key: '/action-spaces/monitoring',
          icon: <BarChartOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/action-spaces/monitoring">行动监控</Link>,
          title: "监控行动空间和智能体的运行状态"
        },
        {
          key: '/workspace/browser',
          icon: <FolderOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/workspace/browser">工作空间浏览器</Link>,
        },
      ]
    },

    {
      key: 'roles',
      icon: <TeamOutlined style={{ fontSize: '18px' }} />,
      label: <span>角色与智能体</span>,
      children: [
        {
          key: '/roles/management',
          icon: <UserOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/roles/management">角色管理</Link>,
        },
        {
          key: '/roles/tools',
          icon: <ToolOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/roles/tools">能力与工具</Link>,
        },
        {
          key: '/roles/knowledges',
          icon: <DatabaseOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/roles/knowledges">知识库管理</Link>,
        },
        {
          key: '/roles/memories',
          icon: <PartitionOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/roles/memories">分区记忆管理</Link>,
        }
      ]
    },
    {
      key: 'action-spaces',
      icon: <GlobalOutlined style={{ fontSize: '18px' }} />,
      label: <span>行动空间管理</span>,
      children: [
        {
          key: '/action-spaces/overview',
          icon: <AppstoreOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/action-spaces/overview">行动空间</Link>,
        },
        {
          key: '/action-spaces/joint',
          icon: <LinkOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/action-spaces/joint">联合空间</Link>,
        },
        {
          key: '/action-spaces/rules',
          icon: <ApartmentOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/action-spaces/rules">行动规则</Link>,
        },
        {
          key: '/action-spaces/environment',
          icon: <EnvironmentOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/action-spaces/environment">环境变量</Link>,
        }
      ]
    },
    {
      key: 'settings',
      icon: <SettingOutlined style={{ fontSize: '18px' }} />,
      label: '系统设置',
      children: [
        {
          key: '/settings/general',
          icon: <SafetyCertificateOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/settings/general">基本配置</Link>,
        },
        {
          key: '/settings/model-configs',
          icon: <ApiOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/settings/model-configs">模型配置</Link>,
        },
        {
          key: '/mcp-servers',
          icon: <ControlOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/mcp-servers">MCP服务器</Link>,
        },
        {
          key: '/settings/logs',
          icon: <CodeOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/settings/logs">运行日志</Link>,
        },
        {
          key: '/settings/about',
          icon: <InfoCircleOutlined style={{ fontSize: '14px' }} />,
          label: <Link to="/settings/about">关于系统</Link>,
        },
      ]
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{
        position: 'fixed',
        zIndex: 10,
        width: '100%',
        height: '50px',
        lineHeight: '50px',
        background: '#fff',
        padding: '0 24px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        boxShadow: '0 1px 4px rgba(0, 0, 0, 0.08)',
        borderBottom: '1px solid #f0f0f0'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Row align="middle" gutter={10}>
            <Col>
              <TeamOutlined style={{ fontSize: '20px', color: '#1677ff' }} />
            </Col>
            <Col>
              <Title
                level={5}
                style={{
                  margin: 0,
                  cursor: 'pointer',
                  transition: 'opacity 0.2s'
                }}
                onClick={() => navigate('/home')}
                onMouseEnter={(e) => e.target.style.opacity = '0.8'}
                onMouseLeave={(e) => e.target.style.opacity = '1'}
              >
                <span style={{ color: '#1677ff' }}>MesaLogo</span> 多智能体专家决策与执行系统
              </Title>
            </Col>
          </Row>
        </div>
        <Space>
          <Dropdown
            menu={{
              items: [
                {
                  key: '1',
                  icon: <LockOutlined />,
                  label: '修改密码',
                  onClick: handleChangePassword,
                },
                {
                  key: '2',
                  icon: <LogoutOutlined />,
                  label: '退出登录',
                  onClick: handleLogout,
                  danger: true
                },
              ],
            }}
            placement="bottomRight"
          >
            <Space style={{ cursor: 'pointer' }}>
              <Avatar
                size="small"
                style={{
                  backgroundColor: '#1677ff',
                }}
                icon={<UserOutlined />}
              />
              <span>{user?.username || '管理员'}</span>
            </Space>
          </Dropdown>
        </Space>
      </Header>

      <Layout style={{ marginTop: 50 }}>
        <Sider
          width={260}
          collapsible
          collapsed={collapsed}
          trigger={null}
          collapsedWidth={80}
          style={{
            background: '#fff',
            boxShadow: '0 1px 4px rgba(0, 0, 0, 0.08)',
            position: 'fixed',
            height: 'calc(100vh - 50px)',
            left: 0,
            overflow: 'auto',
            borderRight: '1px solid #f0f0f0',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          <div style={{
            padding: collapsed ? '0 0 20px 0' : '0 20px 20px 20px',
            borderBottom: '1px solid #f0f0f0',
            textAlign: collapsed ? 'center' : 'left',
            marginTop: '20px'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: collapsed ? 'center' : 'flex-start',
              padding: collapsed ? '0' : '0 16px',
              height: '40px'
            }}>
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={toggleCollapsed}
                style={{
                  width: collapsed ? '48px' : '100%',
                  borderRadius: '2px',
                  fontSize: '16px',
                  color: '#000000d9',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: collapsed ? 'center' : 'flex-start'
                }}
              >
                {!collapsed && <span style={{ marginLeft: '8px', fontSize: '12px' }}>收起菜单</span>}
              </Button>
            </div>
          </div>

          <div style={{ flex: 1, overflow: 'auto' }}>
            <Menu
              mode="inline"
              selectedKeys={[location.pathname + location.search]}
              defaultOpenKeys={collapsed ? [] : ['action-tasks', 'roles', 'action-spaces', 'settings']}
              style={{
                border: 'none',
                padding: '16px 0'
              }}
              items={menuItems.map(item => ({
                ...item,
                style: {
                  margin: '4px 0',
                }
              }))}
            />
          </div>
        </Sider>
        <Layout style={{
          padding: '24px',
          marginLeft: collapsed ? 80 : 260,
          minHeight: 'calc(100vh - 50px)',
          transition: 'margin-left 0.2s'
        }}>
          <Content style={{
            background: 'transparent',
            padding: 0
          }}>
            {children}
          </Content>
        </Layout>
      </Layout>

      {/* 修改密码Modal */}
      <ChangePasswordModal
        visible={changePasswordVisible}
        onCancel={() => setChangePasswordVisible(false)}
        onSuccess={handleChangePasswordSuccess}
      />
    </Layout>
  );
};

export default MainLayout;