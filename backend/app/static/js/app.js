// 创建Vue应用
const { createApp } = Vue

// 创建Socket.IO连接
const socket = io();

// 创建Vue应用实例
const app = createApp({
    data() {
        return {
            // 视图状态
            currentView: 'conversations',
            isDarkMode: false,

            // 模态框状态
            showNewConversationModal: false,
            showNewAgentModal: false,
            showNewWorldModal: false,

            // 数据
            conversations: [],
            agents: [],
            worlds: [],
            settings: {
                api_url: '',
                api_key: '',
                model: '',
                temperature: 0.7,
                max_tokens: 2000
            },

            // 新建表单数据
            newConversation: {
                title: '',
                topic: '',
                agents: [],
                world: '',
                mode: 'sequential',
                turns: 3
            },
            newAgent: {
                name: '',
                role: '',
                description: '',
                system_prompt: ''
            },
            newWorld: {
                name: '',
                description: '',
                rules: '',
                background: ''
            }
        }
    },

    created() {
        // 初始化数据
        this.loadData();
        this.loadSettings();

        // 注册Socket.IO事件处理
        this.registerSocketEvents();

        // 检查系统主题偏好
        this.checkSystemTheme();
    },

    methods: {
        // 数据加载方法
        async loadData() {
            try {
                // 加载会话列表
                const conversationsResponse = await fetch('/api/conversations');
                this.conversations = await conversationsResponse.json();

                // 加载智能体列表
                const agentsResponse = await fetch('/api/agents');
                this.agents = await agentsResponse.json();

                // 加载世界列表
                const worldsResponse = await fetch('/api/worlds');
                this.worlds = await worldsResponse.json();
            } catch (error) {
                console.error('Error loading data:', error);
            }
        },

        async loadSettings() {
            try {
                const response = await fetch('/api/settings');
                this.settings = await response.json();
            } catch (error) {
                console.error('Error loading settings:', error);
            }
        },

        // Socket.IO事件处理
        registerSocketEvents() {
            socket.on('connect', () => {
                console.log('Connected to server');
            });

            socket.on('disconnect', () => {
                console.log('Disconnected from server');
            });

            socket.on('conversation_start', (data) => {
                console.log('Conversation started:', data);
                // 处理会话开始事件
            });

            socket.on('conversation_error', (data) => {
                console.error('Conversation error:', data);
                // 处理会话错误事件
            });

            socket.on('conversation_complete', () => {
                console.log('Conversation completed');
                // 处理会话完成事件
            });
        },

        // 主题切换
        toggleDarkMode() {
            this.isDarkMode = !this.isDarkMode;
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('darkMode', this.isDarkMode);
        },

        checkSystemTheme() {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            this.isDarkMode = localStorage.getItem('darkMode') === 'true' || prefersDark;
            if (this.isDarkMode) {
                document.documentElement.classList.add('dark');
            }
        },

        // 会话管理方法
        async createConversation() {
            try {
                const response = await fetch('/api/conversations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.newConversation)
                });

                if (response.ok) {
                    const conversation = await response.json();
                    this.conversations.unshift(conversation);
                    this.showNewConversationModal = false;
                    this.resetNewConversationForm();
                }
            } catch (error) {
                console.error('Error creating conversation:', error);
            }
        },

        async openConversation(conversation) {
            // 实现打开会话的逻辑
            console.log('Opening conversation:', conversation);
        },

        // 智能体管理方法
        async createAgent() {
            try {
                const response = await fetch('/api/agents', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.newAgent)
                });

                if (response.ok) {
                    const agent = await response.json();
                    this.agents.push(agent);
                    this.showNewAgentModal = false;
                    this.resetNewAgentForm();
                }
            } catch (error) {
                console.error('Error creating agent:', error);
            }
        },

        async editAgent(agent) {
            // 实现编辑智能体的逻辑
            console.log('Editing agent:', agent);
        },

        // 世界管理方法
        async createWorld() {
            try {
                const response = await fetch('/api/worlds', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.newWorld)
                });

                if (response.ok) {
                    const world = await response.json();
                    this.worlds.push(world);
                    this.showNewWorldModal = false;
                    this.resetNewWorldForm();
                }
            } catch (error) {
                console.error('Error creating world:', error);
            }
        },

        async editWorld(world) {
            // 实现编辑世界的逻辑
            console.log('Editing world:', world);
        },

        // 设置管理方法
        async saveSettings() {
            try {
                const response = await fetch('/api/settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.settings)
                });

                if (response.ok) {
                    // 显示成功消息
                    console.log('Settings saved successfully');
                }
            } catch (error) {
                console.error('Error saving settings:', error);
            }
        },

        // 表单重置方法
        resetNewConversationForm() {
            this.newConversation = {
                title: '',
                topic: '',
                agents: [],
                world: '',
                mode: 'sequential',
                turns: 3
            };
        },

        resetNewAgentForm() {
            this.newAgent = {
                name: '',
                role: '',
                description: '',
                system_prompt: ''
            };
        },

        resetNewWorldForm() {
            this.newWorld = {
                name: '',
                description: '',
                rules: '',
                background: ''
            };
        },

        // 工具方法
        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }
});

// 挂载应用
app.mount('#app'); 