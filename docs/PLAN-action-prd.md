# 行动任务管理系统 - 产品需求文档

## 1. 概述

### 1.1 产品背景

行动任务管理系统是智能体(ABM)建模平台的重要组成部分，是对原有会话管理功能的升级与改造。在行动空间概念框架下，每个会话被重新定义为"行动任务"，代表智能体在特定行动空间中的一次完整交互过程。行动任务记录了智能体在受控环境中的行为表现、环境变量变化、规则触发情况以及监督者的干预活动。

### 1.2 产品目标

- 提供直观高效的行动任务创建与管理界面
- 建立行动任务与行动空间的紧密关联机制
- 实现行动任务中环境变量状态的全程追踪
- 记录行动任务执行过程中的规则触发和效果
- 支持监督者对行动任务的监控和干预
- 提供跨任务的数据分析和问题解决功能

### 1.3 用户群体

- 业务分析师：创建和分析特定业务场景下的智能体解决方案
- 企业用户：设计面向实际问题的交互任务并评估解决效果
- 决策制定者：模拟不同策略下的决策过程和实际结果
- 平台管理员：监控和管理系统中的行动任务

## 2. 功能需求

### 2.1 任务概览

#### 2.1.1 任务列表展示
- 以卡片/表格形式展示所有行动任务
- 显示任务名称、所属行动空间、创建时间、状态
- 提供排序、筛选和搜索功能
- 支持按行动空间分组查看

#### 2.1.2 任务创建
- 选择行动空间创建新任务
- 配置初始环境参数
- 选择参与智能体
- 设置监督者角色

#### 2.1.3 任务管理
- 暂停/继续行动任务
- 终止行动任务
- 归档完成的任务
- 删除不需要的任务

#### 2.1.4 批量操作
- 批量归档任务
- 批量导出任务数据
- 批量标记和分类

### 2.2 任务详情

#### 2.2.1 基本信息
- 任务标识与描述
- 所属行动空间信息
- 创建时间与持续时长
- 当前状态和进展

#### 2.2.2 任务记录
- 时间线形式展示所有交互
- 智能体行为记录
- 消息和响应历史
- 支持筛选和搜索

#### 2.2.3 环境状态追踪
- 显示环境变量初始状态
- 记录环境变量变化历史
- 提供环境变量状态时间线视图
- 支持环境快照比较

#### 2.2.4 规则触发记录
- 展示任务过程中触发的规则
- 记录规则触发时间和条件
- 显示规则执行结果和影响
- 统计规则触发频率

#### 2.2.5 监督记录
- 记录监督者的所有干预活动
- 监督者评论和笔记
- 监督者提出的问题和建议
- 干预效果追踪

### 2.3 任务分析

#### 2.3.1 单任务分析
- 解决方案评估
- 关键事件标记和分析
- 环境变量影响分析
- 规则效果评估

#### 2.3.2 多任务比较
- 选择多个任务进行并排比较
- 对比不同条件下的解决方案效果
- 分析环境变量变化的影响
- 评估规则调整的效果

#### 2.3.3 数据可视化
- 环境变量变化图表
- 行为频率分布图
- 规则触发热力图
- 交互模式网络图

#### 2.3.4 报告生成
- 自动生成任务摘要报告
- 自定义报告内容和格式
- 导出为多种文件格式
- 定期报告计划设置

### 2.4 任务导入导出

#### 2.4.1 导出功能
- 导出单个任务数据
- 批量导出多个任务
- 选择性导出特定数据
- 支持多种导出格式

#### 2.4.2 导入功能
- 导入外部任务数据
- 导入验证和兼容性检查
- 导入冲突解决
- 批量导入工具

## 3. 界面设计

### 3.1 整体导航结构

#### 3.1.1 一级导航菜单
- **行动任务管理**（原会话管理，平台的核心交互模块）

#### 3.1.2 二级导航菜单
- **任务概览** - 任务列表与批量管理
- **任务详情** - 任务内容与状态查看
- **任务分析** - 数据分析与可视化

#### 3.1.3 通用布局结构
- **左侧**：导航菜单，显示一级与二级菜单
- **中央**：主要内容区域，显示当前选择的功能页面
- **右侧**：上下文信息面板，显示选中项的详情和快捷操作

### 3.2 任务概览模块

#### 3.2.1 列表视图
- **功能区**：创建新任务、筛选、搜索、批量操作按钮
- **视图切换**：卡片视图/表格视图/分组视图
- **排序选项**：按时间、行动空间、状态等排序
- **卡片内容**：
  * 任务名称和简短描述
  * 所属行动空间
  * 创建时间和持续时间
  * 状态标识（进行中/已完成/已归档）
  * 参与智能体数量
  * 快捷操作按钮（查看、暂停/继续、终止、归档）

#### 3.2.2 分组视图
- **分组依据**：按行动空间、状态、创建时间等
- **分组统计**：各组中的任务数量和状态分布
- **组内排序**：组内任务的排序选项
- **组操作**：对整组任务的批量操作

### 3.3 任务详情模块

#### 3.3.1 基本信息 Tab
- **任务信息卡**：ID、名称、描述、状态
- **行动空间信息**：所属空间、关联规则集
- **时间信息**：创建时间、最后活动时间、总持续时间
- **参与者信息**：智能体列表、监督者角色

#### 3.3.2 任务记录 Tab
- **时间线视图**：按时间顺序展示所有交互
- **消息卡片**：
  * 发送者和接收者
  * 消息内容
  * 发送时间
  * 相关环境状态快照链接
  * 触发的规则标记
- **过滤工具**：按发送者、时间段、关键词过滤
- **搜索框**：全文搜索交互内容

#### 3.3.3 环境状态 Tab
- **变量列表**：显示所有环境变量及当前值
- **变量分类视图**：按公共变量/角色变量/动态变量分类
- **历史变化图表**：环境变量值的时间变化趋势
- **状态快照**：关键时间点的环境状态快照
- **变量关联图**：变量间影响关系可视化

#### 3.3.4 规则记录 Tab
- **规则触发列表**：按时间顺序显示所有规则触发
- **规则详情卡**：
  * 规则内容和类型
  * 触发条件
  * 触发时间
  * 执行结果和影响
- **规则统计**：各规则触发频率和效果统计
- **规则分类视图**：按规则类型/规则集分组查看

#### 3.3.5 监督记录 Tab
- **干预记录**：监督者的所有干预活动
- **评论列表**：监督者添加的评论和笔记
- **问题记录**：监督者提出的问题及回应
- **干预效果**：干预前后的状态对比

### 3.4 任务分析模块

#### 3.4.1 分析仪表盘
- **关键指标卡片**：任务总数、活跃度、完成率等
- **趋势图表**：任务创建量、完成率等趋势
- **热点图**：行动空间使用热度图
- **异常标记**：异常任务的自动识别和标记

#### 3.4.2 单任务分析工具
- **问题解决路径**：智能体解决问题的路径可视化
- **事件时间线**：关键事件标记和分析
- **因果分析图**：行为与环境变量的关联分析
- **规则影响评估**：规则触发对解决方案的影响分析

#### 3.4.3 多任务比较工具
- **任务选择器**：选择要比较的多个任务
- **并排视图**：任务数据的并排比较显示
- **差异突出显示**：自动识别和突出显示关键差异
- **效果对比**：不同策略下解决效果的对比分析

#### 3.4.4 报告生成器
- **报告模板选择**：多种预设报告模板
- **内容定制**：选择要包含的数据和图表
- **格式设置**：调整报告格式和样式
- **导出选项**：PDF/Word/HTML等导出格式

## 4. 交互流程

### 4.1 创建行动任务流程

1. 用户点击"创建新任务"按钮
2. 选择行动空间
3. 配置任务名称和描述
4. 调整初始环境参数（可选）
5. 选择参与智能体
6. 设置监督者角色
7. 确认创建任务

### 4.2 任务执行流程

1. 进入新创建的任务
2. 系统初始化环境状态
3. 开始智能体交互
4. 实时记录环境变量变化和规则触发
5. 监督者可进行监控和干预
6. 用户可暂停/继续/归档任务
7. 任务完成后自动生成摘要报告

### 4.3 任务分析流程

1. 选择要分析的任务
2. 查看任务详情和任务记录
3. 使用分析工具探索解决方案
4. 标记关键事件和决策点
5. 比较多个任务的解决效果
6. 生成解决方案报告
7. 导出或分享结果

## 5. 技术需求

### 5.1 数据架构

#### 5.1.1 行动任务模型
```json
// 行动任务模型结构示例
{
  "id": "at-001",
  "name": "供应链风险测试任务",
  "description": "测试全球供应链中断对生产的影响",
  "status": "active",
  "action_space": {
    "id": "as-001",
    "name": "全球供应链运营环境"
  },
  "created_at": "2023-05-15T09:00:00Z",
  "last_activity": "2023-05-15T11:30:45Z",
  "duration": 9045,
  "participants": [
    {
      "id": "agent-001",
      "name": "供应链经理",
      "type": "agent"
    },
    {
      "id": "agent-002",
      "name": "采购专员",
      "type": "agent"
    }
  ],
  "supervisors": [
    {
      "id": "sup-001",
      "name": "运营总监",
      "permissions": ["monitor", "intervene", "comment"]
    }
  ],
  "environment_state": {
    "initial": {
      "geopolitical_stability": 0.7,
      "transportation_cost": 100,
      "raw_material_availability": 0.8
    },
    "current": {
      "geopolitical_stability": 0.4,
      "transportation_cost": 180,
      "raw_material_availability": 0.6
    },
    "history": [
      {
        "timestamp": "2023-05-15T09:30:00Z",
        "changes": {
          "geopolitical_stability": { "from": 0.7, "to": 0.6 }
        },
        "trigger": "rule-001"
      }
    ]
  },
  "rule_executions": [
    {
      "rule_id": "rule-001",
      "rule_type": "llm",
      "content": "当关键供应商所在地区出现政治不稳定时，地区稳定性下降",
      "triggered_at": "2023-05-15T09:30:00Z",
      "conditions": {
        "event": "political_unrest_news"
      },
      "effects": [
        {
          "variable": "geopolitical_stability",
          "change": -0.1
        }
      ]
    }
  ],
  "interactions": [
    {
      "id": "int-001",
      "timestamp": "2023-05-15T09:15:00Z",
      "sender": "agent-001",
      "content": "我们需要评估最新的地缘政治形势",
      "triggered_rules": []
    }
  ],
  "supervisor_logs": [
    {
      "id": "log-001",
      "supervisor_id": "sup-001",
      "timestamp": "2023-05-15T09:35:00Z",
      "type": "comment",
      "content": "供应链经理正确识别了风险因素"
    }
  ]
}
```

#### 5.1.2 存储方案
- 使用时序数据库存储环境变量历史变化
- 为任务记录和规则触发建立高效索引
- 实现任务数据的增量备份和恢复机制

### 5.2 后端需求

- 行动任务与行动空间的关联管理
- 环境变量状态追踪和计算
- 规则触发记录和效果评估
- 监督者干预处理机制
- 数据分析和报告生成引擎

### 5.3 前端需求

- 响应式任务管理界面
- 交互时间线的高效渲染
- 环境变量变化的可视化图表
- 多任务比较的并排视图
- 报告编辑和预览功能

### 5.4 集成需求

- 与行动空间管理系统的深度集成
- 与规则引擎的实时交互
- 与智能体服务的通信接口
- 与用户权限系统的集成

## 6. 实现优先级

### 6.1 第一阶段（核心功能）
- 行动任务基础管理（创建、编辑、删除）
- 任务与行动空间的关联机制
- 基本任务记录功能
- 简化版环境状态追踪

### 6.2 第二阶段（增强功能）
- 规则触发记录和分析
- 监督者干预功能
- 环境变量历史图表

### 6.3 第三阶段（高级功能）
- 多任务比较工具
- 高级数据分析和可视化
- 自动化报告生成
- 任务导入导出功能

## 7. 成功指标

- 任务创建效率：从选择行动空间到任务创建完成的平均时间＜2分钟
- 问题解决效率：在相同问题场景下，使用系统与传统方法相比效率提升＞40%
- 分析工具使用率：业务用户使用分析工具的比例＞80%
- 解决方案可重用性：成功的解决方案被应用到其他场景的比例＞30%
- 用户满意度：行动任务管理功能用户满意度评分＞4.5（满分5分） 