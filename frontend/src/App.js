import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { App as AntdApp } from 'antd';
import MainLayout from './components/layout/MainLayout';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import { useGlobalErrorHandler } from './hooks/useGlobalErrorHandler';

// 导入登录页面
import Login from './pages/login/Login';

// 导入首页组件
import Home from './pages/Home';

// 从重组的目录导入角色管理相关页面
import RoleManagement from './pages/roles/RoleManagement';
import ToolManagement from './pages/roles/ToolManagement';
import WorkspaceManagement from './pages/workspace';
import PartitionWorkspaceTab from './pages/workspace/PartitionWorkspaceTab';

// 导入知识库管理相关页面
import KnowledgeBaseMain from './pages/knowledgebase/KnowledgeBaseMain';

// 从重组的目录导入行动空间相关页面
import ActionSpaceOverview from './pages/actionspace/ActionSpaceOverview';
import ActionSpaceDetail from './pages/actionspace/ActionSpaceDetail';
import ActionRules from './pages/actionspace/ActionRules';
import EnvironmentVariables from './pages/actionspace/EnvironmentVariables';
import MonitoringCenter from './pages/actionspace/MonitoringCenter';
import JointSpaceManagement from './pages/actionspace/JointSpaceManagement';

// 行动任务组件
import ActionTaskOverview from './pages/actiontask/ActionTaskOverview';
import ActionTaskDetail from './pages/actiontask/ActionTaskDetail';
import ActionTaskAnalysis from './pages/actiontask/ActionTaskAnalysis';

// 还未移动的页面
// 导入设置页面
import Settings from './pages/settings/Settings';
import GeneralSettingsPage from './pages/settings/GeneralSettingsPage';
import ModelConfigsPage from './pages/settings/ModelConfigsPage';
import MCPServersPage from './pages/settings/MCPServersPage';
import AboutPage from './pages/settings/AboutPage';
import LogsPage from './pages/settings/LogsPage';

// 暂时保留旧组件用于兼容
import Agents from './pages/Agents';

import './App.css';

function App() {
  console.log('App: 渲染');

  // 启用全局错误处理
  useGlobalErrorHandler();

  return (
    <AntdApp>
      <AuthProvider>
        <Routes>
          {/* 登录页面 - 不需要认证 */}
          <Route path="/login" element={<Login />} />

          {/* 所有需要认证的路由 */}
          <Route path="/*" element={
            <ProtectedRoute>
              <MainLayout>
                <Routes>
                  {/* 首页重定向到系统概览 */}
                  <Route path="/" element={<Navigate to="/home" replace />} />

                  {/* 系统概览页面 */}
                  <Route path="/home" element={<Home />} />

                  {/* 行动任务管理路由 */}
                  <Route path="/action-tasks/overview" element={<ActionTaskOverview />} />
                  <Route path="/action-tasks/detail/:taskId" element={<ActionTaskDetail />} />
                  <Route path="/action-tasks/detail" element={<Navigate to="/action-tasks/overview" replace />} />
                  <Route path="/action-tasks/analysis" element={<ActionTaskAnalysis />} />
                  <Route path="/action-tasks/monitoring" element={<Navigate to="/action-spaces/monitoring" replace />} />
                  <Route path="/action-tasks/agent/:id" element={<ActionTaskDetail />} />

                  {/* 角色与智能体路由 */}
                  <Route path="/agents" element={<Agents />} />
                  <Route path="/roles" element={<RoleManagement />} />
                  <Route path="/roles/management" element={<RoleManagement />} />
                  <Route path="/roles/knowledges" element={<KnowledgeBaseMain />} />
                  <Route path="/roles/tools" element={<ToolManagement />} />
                  <Route path="/roles/memories" element={<WorkspaceManagement />} />
                  <Route path="/workspace/browser" element={<PartitionWorkspaceTab />} />

                  {/* 行动空间管理路由 */}
                  <Route path="/action-spaces/overview" element={<ActionSpaceOverview />} />
                  <Route path="/action-spaces/detail/:id" element={<ActionSpaceDetail />} />
                  <Route path="/action-spaces/joint" element={<JointSpaceManagement />} />
                  <Route path="/action-spaces/rules" element={<ActionRules />} />
                  <Route path="/action-spaces/environment" element={<EnvironmentVariables />} />
                  <Route path="/action-spaces/monitoring" element={<MonitoringCenter />} />
                  <Route path="/actionspace" element={<Navigate to="/action-spaces/overview" replace />} />
                  <Route path="/actionspace/detail/:id" element={<ActionSpaceDetail />} />

                  {/* 系统设置 */}
                  <Route path="/settings" element={<Navigate to="/settings/general" replace />} />
                  <Route path="/settings/general" element={<GeneralSettingsPage />} />
                  <Route path="/settings/model-configs" element={<ModelConfigsPage />} />
                  <Route path="/settings/logs" element={<LogsPage />} />
                  <Route path="/settings/about" element={<AboutPage />} />

                  {/* MCP服务器管理 */}
                  <Route path="/mcp-servers" element={<MCPServersPage />} />
                </Routes>
              </MainLayout>
            </ProtectedRoute>
          } />
        </Routes>
      </AuthProvider>
    </AntdApp>
  );
}

export default App;