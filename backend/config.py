import os
import configparser
from dotenv import load_dotenv

# 加载.env文件中的环境变量
load_dotenv()

# 加载config.conf配置文件
config_parser = configparser.ConfigParser()
config_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'config.conf')
if os.path.exists(config_path):
    # 使用UTF-8编码读取配置文件，解决中文Windows系统上的编码问题
    config_parser.read(config_path, encoding='utf-8')

# 获取后端配置
def get_backend_url():
    """从配置文件获取后端URL"""
    if 'BACKEND_CONFIG' in config_parser:
        host = config_parser['BACKEND_CONFIG'].get('HOST', '0.0.0.0')
        port = config_parser['BACKEND_CONFIG'].get('PORT', '8080')
        return f"http://{host}:{port}"
    return "http://localhost:8080"  # 默认值

# 获取前端配置
def get_frontend_url():
    """从配置文件获取前端URL"""
    if 'FRONTEND_CONFIG' in config_parser:
        host = config_parser['FRONTEND_CONFIG'].get('HOST', 'localhost')
        port = config_parser['FRONTEND_CONFIG'].get('PORT', '3000')
        return f"http://{host}:{port}"
    return "http://localhost:3000"  # 默认值

# 定义BASE_DIR常量
BASE_DIR = os.path.abspath(os.path.dirname(__file__))

# 后端URL
BACKEND_URL = get_backend_url()
# 前端URL
FRONTEND_URL = get_frontend_url()

# 获取LLM响应调试开关
def get_debug_llm_response():
    """从配置文件获取是否启用LLM响应调试"""
    if 'BACKEND_CONFIG' in config_parser:
        debug_llm = config_parser['BACKEND_CONFIG'].get('DEBUG_LLM_RESPONSE', 'False')
        return debug_llm.lower() in ('true', 't', 'yes', 'y', '1')
    return False  # 默认值

# LLM响应调试开关
DEBUG_LLM_RESPONSE = get_debug_llm_response()

# 获取日志级别
def get_log_level():
    """从配置文件获取日志级别"""
    if 'BACKEND_CONFIG' in config_parser:
        log_level = config_parser['BACKEND_CONFIG'].get('LOG_LEVEL', 'INFO')
        # 转换为大写，确保与logging模块的常量匹配
        return log_level.upper()
    return 'INFO'  # 默认值

# 日志级别
LOG_LEVEL = get_log_level()

class Config:
    """基础配置类"""
    # 应用设置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-please-change-in-production'
    DEBUG = os.environ.get('FLASK_DEBUG', '0') == '1'

    # 许可证设置
    LICENSE_SECRET_KEY = os.environ.get('LICENSE_SECRET_KEY') or 'license-key-for-development-only'

    # 数据库设置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URI') or 'sqlite:///' + os.path.join(BASE_DIR, 'instance', 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # 日志设置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', LOG_LEVEL)

    # API设置
    API_VERSION = '1.0'
    API_BASE_URL = BACKEND_URL

    # 文件上传设置
    UPLOAD_FOLDER = os.path.join(BASE_DIR, 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # 默认超时设置
    REQUEST_TIMEOUT = 120  # 秒

    # 会话设置
    SESSION_TYPE = 'filesystem'
    SESSION_PERMANENT = False
    PERMANENT_SESSION_LIFETIME = 3600  # 1小时

    # 跨域设置
    CORS_ORIGINS = [FRONTEND_URL]  # 允许的跨域来源

    # 平台时区设置
    TIMEZONE = os.environ.get('TIMEZONE', 'Asia/Shanghai')

class TestConfig(Config):
    """测试配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    # 在生产环境中，应通过环境变量设置SECRET_KEY
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URI')
    # 启用HTTPS
    SESSION_COOKIE_SECURE = True
    REMEMBER_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True

    # 生产环境日志级别
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'ERROR')

    # 限制跨域来源
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '').split(',')

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    # 开发环境可以启用更详细的日志
    LOG_LEVEL = 'DEBUG'
    # 允许任何来源的跨域请求
    CORS_ORIGINS = '*'

# 根据环境变量选择配置
config_dict = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestConfig,
    'default': DevelopmentConfig
}

def get_config():
    """根据环境变量获取配置"""
    env = os.environ.get('FLASK_ENV', 'default')
    return config_dict.get(env, config_dict['default'])