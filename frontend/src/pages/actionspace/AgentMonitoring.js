import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Modal, Tag, Descriptions, Statistic, Row, Col, Timeline, message, Typography, Tabs, Empty } from 'antd';
import { MonitorOutlined, EyeOutlined, SyncOutlined, ReloadOutlined, DatabaseOutlined, MessageOutlined } from '@ant-design/icons';
import { agentAPI } from '../../services/api/agent';
import AgentVariables from '../../components/agent/AgentVariables';

const { Text } = Typography;
const { TabPane } = Tabs;

const AgentMonitoring = () => {
  const [agents, setAgents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [memoryModalVisible, setMemoryModalVisible] = useState(false);
  const [memories, setMemories] = useState([]);
  const [activeDetailTab, setActiveDetailTab] = useState('info');

  // 行动监控是只读的

  // 获取智能体列表
  const fetchAgents = async () => {
    try {
      setLoading(true);
      const response = await agentAPI.getAllActive();
      // 如果是数组直接使用，如果是对象格式则提取data属性
      const agentData = Array.isArray(response) ? response : response.data || [];
      setAgents(agentData);
    } catch (error) {
      message.error('获取智能体列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 行动监控只需要获取智能体列表

  useEffect(() => {
    fetchAgents();
    // 设置定时刷新
    const interval = setInterval(fetchAgents, 30000);
    return () => clearInterval(interval);
  }, []);

  // 获取智能体记忆和详细信息
  const fetchMemories = async (agentId) => {
    try {
      // 获取记忆
      const memoryResponse = await agentAPI.getMemories(agentId);
      const memoryData = memoryResponse.data || [];
      setMemories(memoryData);

      // 获取智能体详细信息，包括行动空间和行动任务
      const agentResponse = await agentAPI.getById(agentId);
      const agentData = agentResponse.data || {};
      setSelectedAgent(prevAgent => ({
        ...prevAgent,
        ...agentData
      }));
    } catch (error) {
      message.error('获取智能体记忆失败');
      setMemories([]);
    }
  };

  // 行动监控是只读的，不需要停止和删除功能

  // 查看智能体详情
  const showDetail = (agent) => {
    setSelectedAgent(agent);
    setActiveDetailTab('info'); // 重置为基本信息标签
    setDetailModalVisible(true);
  };

  // 处理详情标签页切换
  const handleDetailTabChange = (key) => {
    setActiveDetailTab(key);
  };

  // 行动监控是只读的，不需要搜索和过滤功能

  // 查看智能体记忆
  const showMemories = (agent) => {
    setSelectedAgent(agent);
    fetchMemories(agent.id);
    setMemoryModalVisible(true);
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <MonitorOutlined style={{ color: '#1677ff' }} />
          <span>{text}</span>
        </Space>
      ),
      width: 180,
    },
    {
      title: '角色',
      key: 'role',
      render: (_, record) => {
        // 根据role_id获取角色名称，或直接使用返回的角色名
        if (record.role && record.role.name) {
          return record.role.name;
        }
        // 兼容API返回，可能会返回role_name字段
        return record.role_name || '未知角色';
      },
      width: 150,
    },
    {
      title: '类型',
      key: 'source',
      render: (_, record) => (
        <Tag color={record.source === 'internal' ? 'blue' : 'orange'}>
          {record.source === 'internal' ? '内部' : '外部'}
        </Tag>
      ),
      width: 80,
    },
    {
      title: '行动任务',
      key: 'action_task',
      render: (_, record) => {
        // 获取行动任务名称
        let taskName = '';
        if (record.action_task && record.action_task.name) {
          taskName = record.action_task.name;
        } else if (record.action_task_name && record.action_task_name !== '未分配') {
          taskName = record.action_task_name;
        } else if (record.action_task_id) {
          taskName = `任务#${record.action_task_id}`;
        }

        // 获取行动空间名称
        let spaceName = '';
        if (record.action_space && record.action_space.name) {
          spaceName = record.action_space.name;
        } else if (record.action_space_name) {
          spaceName = record.action_space_name;
        }

        // 如果有行动任务和行动空间，显示格式为 "任务[空间]"
        if (taskName && spaceName) {
          return <Tag color="green">{`${taskName}[${spaceName}]`}</Tag>;
        } else if (taskName) {
          // 只有任务没有空间
          return <Tag color="green">{taskName}</Tag>;
        } else {
          // 都没有
          return <Text type="secondary">未分配</Text>;
        }
      },
      width: 220,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={
          status === 'active' ? 'success' :
          status === 'idle' ? 'default' :
          status === 'busy' ? 'processing' :
          'error'
        }>
          {
            status === 'active' ? '活跃' :
            status === 'idle' ? '空闲' :
            status === 'busy' ? '忙碌' :
            '离线'
          }
        </Tag>
      ),
      width: 80,
    },
    {
      title: '会话数',
      dataIndex: 'conversation_count',
      key: 'conversation_count',
      width: 90,
    },
    {
      title: '最后活动时间',
      dataIndex: 'last_active',
      key: 'last_active',
      render: (date) => date ? new Date(date).toLocaleString() : '未知',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => showDetail(record)}
          >
            详情
          </Button>
          <Button
            type="text"
            icon={<SyncOutlined />}
            onClick={() => showMemories(record)}
          >
            记忆
          </Button>
        </Space>
      ),
      width: 180,
      fixed: 'right',
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '20px' }}>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchAgents}
            size="large"
            style={{
              borderRadius: '8px',
              height: '42px',
              fontSize: '14px'
            }}
          >
            刷新状态
          </Button>
        </div>
      </div>

      <Card
        bordered={false}
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
        }}
      >
        <Table
          columns={columns}
          dataSource={agents}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个智能体`,
            position: ['bottomRight']
          }}
          style={{ overflowX: 'auto' }}
          scroll={{ x: 1500 }}
          locale={{
            emptyText: <Empty description="暂无智能体数据" />
          }}
        />
      </Card>

      {/* 智能体详情模态框 */}
      <Modal
        title={`智能体详情 - ${selectedAgent?.name}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedAgent && (
          <Tabs activeKey={activeDetailTab} onChange={handleDetailTabChange}>
            <TabPane tab={<span>基本信息</span>} key="info">
              <Descriptions bordered column={2}>
                <Descriptions.Item label="ID">{selectedAgent.id}</Descriptions.Item>
                <Descriptions.Item label="名称">{selectedAgent.name}</Descriptions.Item>
                <Descriptions.Item label="角色">{selectedAgent.role?.name}</Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag color={
                    selectedAgent.status === 'active' ? 'success' :
                    selectedAgent.status === 'idle' ? 'default' :
                    selectedAgent.status === 'busy' ? 'processing' :
                    'error'
                  }>
                    {
                      selectedAgent.status === 'active' ? '活跃' :
                      selectedAgent.status === 'idle' ? '空闲' :
                      selectedAgent.status === 'busy' ? '忙碌' :
                      '离线'
                    }
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="类型" span={1}>
                  <Tag color={selectedAgent?.source === 'internal' ? 'blue' : 'orange'}>
                    {selectedAgent?.source === 'internal' ? '内部智能体' : '外部智能体'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="行动空间" span={1}>
                  {selectedAgent?.action_space ? (
                    <Tag color="purple">{selectedAgent.action_space.name}</Tag>
                  ) : (
                    <Text type="secondary">未分配</Text>
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="行动任务" span={2}>
                  {selectedAgent?.action_task ? (
                    <Tag color="green">{selectedAgent.action_task.name}</Tag>
                  ) : (
                    <Text type="secondary">未分配</Text>
                  )}
                </Descriptions.Item>
              </Descriptions>

              <Row gutter={16} style={{ marginTop: '24px' }}>
                <Col span={8}>
                  <Statistic title="总会话数" value={selectedAgent.conversation_count} />
                </Col>
                <Col span={8}>
                  <Statistic title="总消息数" value={selectedAgent.message_count} />
                </Col>
                <Col span={8}>
                  <Statistic title="平均响应时间" value={selectedAgent.avg_response_time} suffix="ms" />
                </Col>
              </Row>
            </TabPane>
            <TabPane
              tab={<span><DatabaseOutlined />代理变量</span>}
              key="variables"
            >
              <AgentVariables agentId={selectedAgent.id} />
            </TabPane>
            <TabPane
              tab={<span><MessageOutlined />消息记录</span>}
              key="messages"
            >
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <Text type="secondary">消息记录功能开发中...</Text>
              </div>
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 智能体记忆模态框 */}
      <Modal
        title={`智能体记忆 - ${selectedAgent?.name}`}
        open={memoryModalVisible}
        onCancel={() => setMemoryModalVisible(false)}
        footer={null}
        width={800}
      >
        {/* 智能体所属信息 */}
        <div style={{ marginBottom: '20px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
          <Descriptions size="small" column={2} bordered>
            <Descriptions.Item label="角色" span={1}>
              {selectedAgent?.role?.name || '未知角色'}
            </Descriptions.Item>
            <Descriptions.Item label="类型" span={1}>
              <Tag color={selectedAgent?.source === 'internal' ? 'blue' : 'orange'}>
                {selectedAgent?.source === 'internal' ? '内部智能体' : '外部智能体'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="行动空间" span={2}>
              {selectedAgent?.action_space ? (
                <Tag color="purple">{selectedAgent.action_space.name}</Tag>
              ) : (
                <Text type="secondary">未分配</Text>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="行动任务" span={2}>
              {selectedAgent?.action_task ? (
                <Tag color="green">{selectedAgent.action_task.name}</Tag>
              ) : (
                <Text type="secondary">未分配</Text>
              )}
            </Descriptions.Item>
          </Descriptions>
        </div>

        {/* 记忆时间线 */}
        <Timeline
          items={memories.map(memory => ({
            color: memory.type === 'conversation' ? 'blue' : 'green',
            children: (
              <>
                <p style={{ margin: 0 }}>
                  <Tag color={memory.type === 'conversation' ? 'blue' : 'green'}>
                    {memory.type === 'conversation' ? '对话' : '知识'}
                  </Tag>
                  <span style={{ marginLeft: '8px' }}>{new Date(memory.created_at).toLocaleString()}</span>
                </p>
                <p style={{ margin: '8px 0 0 0' }}>{memory.content}</p>
              </>
            ),
          }))}
        />
      </Modal>
    </div>
  );
};

export default AgentMonitoring;
