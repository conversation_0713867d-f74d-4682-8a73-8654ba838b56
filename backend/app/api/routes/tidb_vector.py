"""
TiDB向量数据库API路由

提供TiDB向量数据库的配置管理和连接测试API
"""

import logging
from flask import Blueprint, request, jsonify, current_app
from typing import Dict, Any

from app.services.vector_db.tidb_config import tidb_config_manager, TiDBConfig
from app.services.vector_db.tidb_connection import tidb_connection_manager
from app.services.vector_db.embedding_service import embedding_service
from app.services.vector_db.vector_operations import vector_operations
from app.services.vector_db.table_manager import vector_table_manager
from app.services.vector_db.models import VectorCollection, VectorDistanceMetric, VectorDataType
from app.models import ModelConfig

logger = logging.getLogger(__name__)

# 创建蓝图
tidb_vector_bp = Blueprint('tidb_vector', __name__, url_prefix='/api/tidb-vector')


@tidb_vector_bp.route('/config/validate', methods=['POST'])
def validate_config():
    """验证TiDB配置"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        connection_string = data.get('connection_string')
        if not connection_string:
            return jsonify({
                'success': False,
                'message': '连接字符串不能为空'
            }), 400
        
        # 创建配置并验证
        config = tidb_config_manager.create_config(connection_string)
        is_valid, message = config.validate()
        
        if is_valid:
            return jsonify({
                'success': True,
                'message': message,
                'config': config.to_dict()
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400
            
    except Exception as e:
        logger.error(f"验证TiDB配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'配置验证失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/connection/test', methods=['POST'])
def test_connection():
    """测试TiDB连接"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        connection_string = data.get('connection_string')
        if not connection_string:
            return jsonify({
                'success': False,
                'message': '连接字符串不能为空'
            }), 400
        
        # 创建配置
        config = tidb_config_manager.create_config(connection_string)
        
        # 测试连接
        success, message, info = tidb_config_manager.test_connection(config)
        
        response_data = {
            'success': success,
            'message': message
        }
        
        if success:
            response_data['info'] = info
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"测试TiDB连接失败: {e}")
        return jsonify({
            'success': False,
            'message': f'连接测试失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/connection/test-vector', methods=['POST'])
def test_vector_operations():
    """测试TiDB向量操作"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        connection_string = data.get('connection_string')
        if not connection_string:
            return jsonify({
                'success': False,
                'message': '连接字符串不能为空'
            }), 400
        
        # 创建配置并初始化连接
        config = tidb_config_manager.create_config(connection_string)
        tidb_connection_manager.initialize(config)
        
        # 测试向量操作
        success, message, info = tidb_connection_manager.test_vector_operations()
        
        response_data = {
            'success': success,
            'message': message
        }
        
        if success:
            response_data['info'] = info
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"测试TiDB向量操作失败: {e}")
        return jsonify({
            'success': False,
            'message': f'向量操作测试失败: {str(e)}'
        }), 500
    finally:
        # 清理连接
        try:
            tidb_connection_manager.close()
        except Exception:
            pass


@tidb_vector_bp.route('/config/parse', methods=['POST'])
def parse_connection_string():
    """解析TiDB连接字符串"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        connection_string = data.get('connection_string')
        if not connection_string:
            return jsonify({
                'success': False,
                'message': '连接字符串不能为空'
            }), 400
        
        # 创建配置（会自动解析连接字符串）
        config = TiDBConfig(connection_string=connection_string)
        
        return jsonify({
            'success': True,
            'message': '连接字符串解析成功',
            'config': config.to_dict()
        })
        
    except Exception as e:
        logger.error(f"解析TiDB连接字符串失败: {e}")
        return jsonify({
            'success': False,
            'message': f'连接字符串解析失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/info', methods=['GET'])
def get_tidb_info():
    """获取TiDB向量数据库信息"""
    try:
        # 检查依赖是否可用
        try:
            from tidb_vector.integrations import TiDBVectorClient
            import pymysql
            dependencies_available = True
            dependency_error = None
        except ImportError as e:
            dependencies_available = False
            dependency_error = str(e)
        
        # 获取默认配置
        default_config = tidb_config_manager.get_default_config()
        
        info = {
            'dependencies_available': dependencies_available,
            'dependency_error': dependency_error,
            'has_default_config': default_config is not None,
            'supported_features': [
                'vector_storage',
                'similarity_search',
                'metadata_filtering',
                'batch_operations'
            ],
            'required_dependencies': [
                'tidb-vector[client]',
                'pymysql',
                'sentence-transformers'
            ]
        }
        
        if default_config:
            info['default_config'] = default_config.to_dict()
        
        return jsonify({
            'success': True,
            'info': info
        })
        
    except Exception as e:
        logger.error(f"获取TiDB信息失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取信息失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        # 检查依赖
        try:
            from tidb_vector.integrations import TiDBVectorClient
            import pymysql
            dependencies_ok = True
        except ImportError:
            dependencies_ok = False
        
        # 检查连接状态
        connection_info = tidb_connection_manager.get_connection_info()
        
        health_status = {
            'status': 'healthy' if dependencies_ok else 'unhealthy',
            'dependencies_available': dependencies_ok,
            'connection_active': connection_info.get('connected', False),
            'timestamp': current_app.config.get('REQUEST_TIME', 'unknown')
        }
        
        if connection_info:
            health_status['connection_info'] = connection_info
        
        status_code = 200 if dependencies_ok else 503
        
        return jsonify(health_status), status_code
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


# 错误处理
@tidb_vector_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': '接口不存在'
    }), 404


@tidb_vector_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'message': '请求方法不允许'
    }), 405


@tidb_vector_bp.route('/embedding/models', methods=['GET'])
def get_embedding_models():
    """获取可用的嵌入模型列表"""
    try:
        # 查询所有支持向量输出的模型
        embedding_models = ModelConfig.query.filter(
            ModelConfig.modalities.contains('vector_output')
        ).all()

        models_info = []
        for model in embedding_models:
            model_info = embedding_service.get_model_info(model)
            models_info.append(model_info)

        # 获取默认模型
        default_model = embedding_service.get_default_embedding_model()
        default_model_id = default_model.id if default_model else None

        return jsonify({
            'success': True,
            'models': models_info,
            'default_model_id': default_model_id,
            'total_count': len(models_info)
        })

    except Exception as e:
        logger.error(f"获取嵌入模型列表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取模型列表失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/embedding/generate', methods=['POST'])
def generate_embeddings():
    """生成嵌入向量"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400

        texts = data.get('texts')
        if not texts:
            return jsonify({
                'success': False,
                'message': '文本内容不能为空'
            }), 400

        # 获取模型配置
        model_id = data.get('model_id')
        if model_id:
            model_config = embedding_service.get_embedding_model_by_id(model_id)
            if not model_config:
                return jsonify({
                    'success': False,
                    'message': f'模型ID {model_id} 不存在或不支持向量输出'
                }), 400
        else:
            model_config = None  # 使用默认模型

        # 生成嵌入向量
        success, result, meta_info = embedding_service.generate_embeddings(texts, model_config)

        response_data = {
            'success': success,
            'meta_info': meta_info
        }

        if success:
            response_data['embeddings'] = result
            response_data['message'] = '嵌入向量生成成功'
        else:
            response_data['message'] = result

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"生成嵌入向量失败: {e}")
        return jsonify({
            'success': False,
            'message': f'生成嵌入向量失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/embedding/test', methods=['POST'])
def test_embedding_model():
    """测试嵌入模型"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400

        # 获取测试文本
        test_text = data.get('text', '这是一个测试文本')

        # 获取模型配置
        model_id = data.get('model_id')
        if model_id:
            model_config = embedding_service.get_embedding_model_by_id(model_id)
            if not model_config:
                return jsonify({
                    'success': False,
                    'message': f'模型ID {model_id} 不存在或不支持向量输出'
                }), 400
        else:
            model_config = embedding_service.get_default_embedding_model()
            if not model_config:
                return jsonify({
                    'success': False,
                    'message': '未找到可用的嵌入模型'
                }), 400

        # 测试生成嵌入向量
        success, result, meta_info = embedding_service.generate_single_embedding(test_text, model_config)

        response_data = {
            'success': success,
            'meta_info': meta_info,
            'model_info': embedding_service.get_model_info(model_config)
        }

        if success:
            response_data['embedding'] = result
            response_data['vector_dimension'] = len(result)
            response_data['message'] = '嵌入模型测试成功'
        else:
            response_data['message'] = result

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"测试嵌入模型失败: {e}")
        return jsonify({
            'success': False,
            'message': f'测试嵌入模型失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/tables', methods=['GET'])
def list_tables():
    """列出所有向量表"""
    try:
        tables = vector_table_manager.list_tables()

        return jsonify({
            'success': True,
            'tables': tables,
            'total_count': len(tables)
        })

    except Exception as e:
        logger.error(f"列出向量表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'列出向量表失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/tables/<table_name>', methods=['POST'])
def create_table(table_name):
    """创建向量表"""
    try:
        data = request.get_json() or {}

        # 获取参数
        dimension = data.get('dimension', 1024)
        distance_metric = data.get('distance_metric', 'COSINE')
        description = data.get('description', '')

        # 验证参数
        if not isinstance(dimension, int) or dimension <= 0:
            return jsonify({
                'success': False,
                'message': '向量维度必须是正整数'
            }), 400

        try:
            metric = VectorDistanceMetric(distance_metric)
        except ValueError:
            return jsonify({
                'success': False,
                'message': f'不支持的距离度量: {distance_metric}'
            }), 400

        # 创建集合配置
        collection = VectorCollection(
            name=table_name,
            dimension=dimension,
            distance_metric=metric,
            description=description
        )

        # 创建表
        success, message = vector_table_manager.create_table(collection)

        response_data = {
            'success': success,
            'message': message
        }

        if success:
            # 获取表信息
            table_info = vector_table_manager.get_table_info(table_name)
            if table_info:
                response_data['table_info'] = table_info

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"创建向量表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'创建向量表失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/tables/<table_name>', methods=['DELETE'])
def drop_table(table_name):
    """删除向量表"""
    try:
        success, message = vector_table_manager.drop_table(table_name)

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        logger.error(f"删除向量表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除向量表失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/tables/<table_name>/info', methods=['GET'])
def get_table_info(table_name):
    """获取表信息"""
    try:
        table_info = vector_table_manager.get_table_info(table_name)

        if table_info:
            return jsonify({
                'success': True,
                'table_info': table_info
            })
        else:
            return jsonify({
                'success': False,
                'message': f'表 {table_name} 不存在'
            }), 404

    except Exception as e:
        logger.error(f"获取表信息失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取表信息失败: {str(e)}'
        }), 500


@tidb_vector_bp.route('/tables/<table_name>/search', methods=['POST'])
def semantic_search(table_name):
    """语义搜索"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400

        query_text = data.get('query_text')
        if not query_text:
            return jsonify({
                'success': False,
                'message': '查询文本不能为空'
            }), 400

        # 获取参数
        limit = data.get('limit', 10)
        distance_metric = data.get('distance_metric', 'COSINE')
        filters = data.get('filters')
        model_id = data.get('model_id')

        try:
            metric = VectorDistanceMetric(distance_metric)
        except ValueError:
            return jsonify({
                'success': False,
                'message': f'不支持的距离度量: {distance_metric}'
            }), 400

        # 执行语义搜索
        success, results, info = vector_operations.semantic_search(
            table_name=table_name,
            query_text=query_text,
            limit=limit,
            distance_metric=metric,
            filters=filters,
            model_config_id=model_id
        )

        response_data = {
            'success': success,
            'info': info
        }

        if success:
            # 转换结果为字典格式
            response_data['results'] = [result.to_dict() for result in results]
            response_data['message'] = f'搜索完成，返回 {len(results)} 条结果'
        else:
            response_data['message'] = results

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"语义搜索失败: {e}")
        return jsonify({
            'success': False,
            'message': f'语义搜索失败: {str(e)}'
        }), 500


@tidb_vector_bp.errorhandler(500)
def internal_error(error):
    logger.error(f"TiDB向量数据库API内部错误: {error}")
    return jsonify({
        'success': False,
        'message': '服务器内部错误'
    }), 500
