# 内部知识库文件存储

此目录用于存储内部知识库的文件和相关数据。

## 目录结构

```
backend/knowledgebase/
├── {knowledge_id}/          # 每个知识库的专用目录
│   ├── files/              # 原始文件存储
│   ├── processed/          # 处理后的文本块
│   ├── metadata.json       # 知识库元数据
│   └── vector_config.json  # 向量化配置信息
└── temp/                   # 临时文件目录
```

## 文件说明

- **files/**: 存储用户上传的原始文件（PDF、Word、TXT、Markdown等）
- **processed/**: 存储文本提取和分块处理后的结果
- **metadata.json**: 知识库的基本信息和统计数据
- **vector_config.json**: 向量化处理的配置信息（使用的嵌入模型、向量数据库等）
- **temp/**: 文件上传和处理过程中的临时文件

## 注意事项

- 向量数据本身存储在配置的向量数据库中（如TiDB、阿里云DashVector等）
- 此目录主要存储原始文件和处理过程中的中间数据
- 定期清理temp目录中的过期临时文件
