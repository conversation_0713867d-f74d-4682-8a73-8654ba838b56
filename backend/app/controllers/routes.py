from flask import request, jsonify
from app.services.action_task_service import ActionTaskService
from app.services.role_service import RoleService
from app.models import Agent, db

# 创建服务实例
role_service = RoleService()
# 使用默认值初始化ActionTaskService
action_task_service = ActionTaskService(agents=[], initial_message="欢迎使用多智能体行动任务系统")

def register_routes(app):
    """注册所有HTTP路由"""

    # 主页路由
    @app.route('/')
    def index():
        return jsonify({"message": "多智能体行动任务系统API服务已启动", "status": "running"})

    # API路由 - 行动任务管理
    @app.route('/api/action-tasks', methods=['GET'])
    def get_action_tasks():
        """获取所有行动任务列表"""
        return jsonify(action_task_service.get_all_action_tasks())

    @app.route('/api/action-tasks/<task_id>', methods=['GET'])
    def get_action_task(task_id):
        """获取特定行动任务详情"""
        task = action_task_service.get_action_task(task_id)
        if task:
            return jsonify(task)
        return jsonify({"error": "行动任务未找到"}), 404

    @app.route('/api/action-tasks', methods=['POST'])
    def create_action_task():
        """创建新行动任务"""
        data = request.json
        task = action_task_service.create_action_task(data)
        return jsonify(task), 201

    @app.route('/api/action-tasks/<task_id>', methods=['DELETE'])
    def delete_action_task(task_id):
        """删除行动任务"""
        success = action_task_service.delete_action_task(task_id)
        if success:
            return jsonify({"success": True})
        return jsonify({"error": "行动任务未找到"}), 404

    # API路由 - 智能体管理
    @app.route('/api/agents', methods=['GET'])
    def get_agents():
        """获取所有智能体列表"""
        agents = role_service.get_all_agents()
        result = []
        for agent in agents:
            result.append(role_service.format_agent_for_api(agent))
        return jsonify(result)

    @app.route('/api/agents/<agent_id>', methods=['GET'])
    def get_agent(agent_id):
        """获取特定智能体详情"""
        agent = role_service.get_agent_by_id(agent_id)
        if agent:
            return jsonify(role_service.format_agent_for_api(agent))
        return jsonify({"error": "智能体未找到"}), 404

    @app.route('/api/agents', methods=['POST'])
    def create_agent():
        """创建新智能体"""
        data = request.json
        agent = role_service.create_agent(data)
        return jsonify(role_service.format_agent_for_api(agent)), 201

    @app.route('/api/agents/<agent_id>', methods=['PUT'])
    def update_agent(agent_id):
        """更新智能体"""
        data = request.json
        agent = role_service.update_agent(agent_id, data)
        if agent:
            return jsonify(role_service.format_agent_for_api(agent))
        return jsonify({"error": "智能体未找到"}), 404

    @app.route('/api/agents/<agent_id>', methods=['DELETE'])
    def delete_agent(agent_id):
        """删除智能体"""
        success = role_service.delete_agent(agent_id)
        if success:
            return jsonify({"success": True})
        return jsonify({"error": "智能体未找到"}), 404

    # API路由 - 系统设置
    # 注意：以下路由已被弃用，请使用app/api/routes/settings.py中的路由
    # 这些路由保留在这里仅作为参考，实际上不会被注册到应用中
    """
    @app.route('/api/settings', methods=['GET'])
    def get_settings():
        # 获取系统设置
        return jsonify({
            "api_url": app.config.get("API_URL"),
            "model": app.config.get("DEFAULT_MODEL"),
            "temperature": app.config.get("TEMPERATURE", 0.7),
            "max_tokens": app.config.get("MAX_TOKENS", 2000)
        })

    @app.route('/api/settings', methods=['POST'])
    def update_settings():
        # 更新系统设置
        data = request.json
        # 更新应用配置
        for key, value in data.items():
            app.config[key.upper()] = value
        return jsonify({"success": True})
    """