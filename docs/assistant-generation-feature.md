# 角色系统提示词辅助生成功能

## 功能概述

在角色编辑页面的系统提示词部分，新增了"辅助生成"按钮，可以根据角色名称和描述自动生成专业的系统提示词。

## 功能特点

### 1. 智能生成
- 根据角色名称和描述自动生成系统提示词
- 使用专业的提示词工程技术
- 生成内容包含角色身份、能力、行为准则等

### 2. 实时流式显示
- 支持流式响应，实时显示生成过程
- 生成过程中文本框会有视觉反馈（绿色边框）
- 生成完成后自动填充到系统提示词字段

### 3. 配置灵活
- 可在系统设置中启用/禁用辅助生成功能
- 可配置使用的生成模型（默认使用系统默认模型）
- 支持温度、最大token等参数调整

## 使用方法

### 1. 基本使用步骤
1. 在角色管理页面点击"创建角色"或编辑现有角色
2. 填写角色名称和描述
3. 在系统提示词字段右上角点击"辅助生成"按钮
4. 等待生成完成，系统会自动填充生成的提示词
5. 可根据需要手动调整生成的内容

### 2. 前置条件
- 必须先填写角色名称和描述
- 系统设置中需要启用辅助生成功能
- 至少配置一个可用的模型

### 3. 生成示例

**输入：**
- 角色名称：数据分析师
- 角色描述：专业的数据分析师，擅长数据挖掘、统计分析和可视化

**生成的系统提示词：**
```
你是一名专业的数据分析师，具备丰富的数据挖掘、统计分析和数据可视化经验。

## 角色定位
- 身份：资深数据分析师
- 专业领域：数据科学、统计学、商业智能
- 核心能力：数据挖掘、统计分析、数据可视化、业务洞察

## 工作职责
1. 收集、清理和处理各类数据源
2. 运用统计方法和机器学习算法进行数据分析
3. 创建直观的数据可视化图表和报告
4. 从数据中提取有价值的商业洞察
5. 为业务决策提供数据支持和建议

## 行为准则
- 始终保持客观、严谨的分析态度
- 使用准确的统计术语和方法
- 提供基于数据的事实性结论
- 清晰解释分析过程和结果
- 注重数据质量和分析的可靠性

## 回答风格
- 逻辑清晰，条理分明
- 使用专业但易懂的语言
- 提供具体的数据支持
- 给出可操作的建议
- 必要时提供可视化建议

请始终以专业数据分析师的身份进行回答，确保分析的准确性和实用性。
```

## 技术实现

### 1. 前端实现
- 在系统提示词字段标签中添加辅助生成按钮
- 使用RobotOutlined图标标识
- 实现流式响应的实时更新
- 添加加载状态和视觉反馈

### 2. 生成逻辑
- 构建专业的生成提示词模板
- 调用配置的模型进行生成
- 支持流式响应实时更新
- 错误处理和用户提示

### 3. 配置管理
- 通过系统设置控制功能开关
- 支持选择生成模型
- 参数可配置（温度、最大token等）

## 配置说明

### 系统设置项
- `enableAssistantGeneration`: 是否启用辅助生成功能（默认：true）
- `assistantGenerationModel`: 辅助生成使用的模型（默认：'default'）

### 生成参数
- 温度：0.7（平衡创造性和准确性）
- 最大token：1000（确保生成完整的提示词）
- 系统提示词：专业的提示词工程师角色

## 注意事项

1. **依赖条件**：需要先配置可用的模型
2. **网络要求**：需要稳定的网络连接进行API调用
3. **生成质量**：生成结果可能需要人工调整和优化
4. **成本考虑**：每次生成会消耗模型API调用额度
5. **数据安全**：角色信息会发送到配置的模型服务

## 故障排除

### 常见问题
1. **按钮禁用**：检查系统设置中是否启用了辅助生成功能
2. **生成失败**：检查模型配置是否正确，网络是否正常
3. **生成内容不理想**：可以多次生成或手动调整
4. **加载时间长**：正常现象，等待模型响应完成
5. **生成内容末尾出现"null"**：已修复，系统会自动过滤无效内容

### 错误信息
- "辅助生成功能未启用"：需要在系统设置中开启
- "请先填写角色名称和描述"：需要完善基本信息
- "未找到可用的模型"：需要配置至少一个模型
- "辅助生成失败"：检查模型配置和网络连接

### 已修复的问题
- **null值过滤**：修复了流式响应中可能出现的null值导致提示词末尾显示"null"的问题
- **数据清理**：在前端和API层面都添加了数据过滤机制
- **内容验证**：确保只有有效的文本内容才会被添加到生成的提示词中

## 未来改进

1. **模板库**：提供预设的角色模板
2. **多语言支持**：支持不同语言的提示词生成
3. **个性化定制**：根据用户偏好调整生成风格
4. **批量生成**：支持批量角色的提示词生成
5. **版本管理**：保存和管理提示词的历史版本
