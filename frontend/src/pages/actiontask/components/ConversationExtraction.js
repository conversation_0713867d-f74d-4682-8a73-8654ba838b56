import React, { useEffect, useMemo } from 'react';
import { Card, Typography, Tag, Collapse, Space } from 'antd';
import {
  ToolOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  DownOutlined,
  RightOutlined,
  CodeOutlined,
  ApiOutlined,
  ThunderboltOutlined,
  SearchOutlined,
  DatabaseOutlined,
  GlobalOutlined,
  BulbOutlined
} from '@ant-design/icons';
import ReactJson from 'react-json-view';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypeRaw from 'rehype-raw';
import mermaid from 'mermaid';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
// 导入对话样式
import '../css/conversation.css';

const { Text, Paragraph } = Typography;

// 初始化mermaid配置
mermaid.initialize({
  startOnLoad: true,
  theme: 'default',
  securityLevel: 'loose',
  fontSize: 14,
  suppressErrors: true,
  suppressErrorRendering: true,
  logLevel: 'fatal', // 只显示致命错误
  errorLevel: 'fatal', // 只显示致命错误
  parseError: function() {
    // 静默处理解析错误，不输出到控制台
    return false;
  }
});


/**
 * 解析思考标签内容，保留原始位置信息
 * @param {string} text 消息内容
 * @returns {Object} 解析后的段落数组，包含普通文本和思考内容
 */
const parseThinking = (text) => {
  if (!text) return { segments: [] };

  try {
    // 定义思考标记的正则表达式
    const thinkingPatterns = [
      { pattern: /<think>([\s\S]*?)<\/think>/g, type: 'think' },
      { pattern: /<thinking>([\s\S]*?)<\/thinking>/g, type: 'thinking' },
      { pattern: /<observing>([\s\S]*?)<\/observing>/g, type: 'observing' }
    ];

    // 存储所有匹配的标签及其位置
    const matches = [];

    // 查找所有思考标签及其位置
    thinkingPatterns.forEach(({ pattern, type }) => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        matches.push({
          type: 'thinking',
          subtype: type,
          content: match[1].trim(),
          startPos: match.index,
          endPos: match.index + match[0].length,
          fullMatch: match[0]
        });
      }
    });

    // 检查是否有未闭合的思考标签（流式状态下可能出现）
    thinkingPatterns.forEach(({ type }) => {
      // 修改正则表达式，匹配未闭合的标签
      const unclosedPattern = new RegExp(`<${type}>([\\\s\\\S]*?)(?=<\\/${type}>|$)`, 'g');
      let match;

      while ((match = unclosedPattern.exec(text)) !== null) {
        // 检查是否已经有完整标签匹配了这部分内容
        const isAlreadyMatched = matches.some(m =>
          m.startPos <= match.index && m.endPos >= match.index + match[0].length
        );

        // 如果没有被完整标签匹配，且不是空内容，则添加为思考内容
        if (!isAlreadyMatched && match[1].trim()) {
          matches.push({
            type: 'thinking',
            subtype: type,
            content: match[1].trim(),
            startPos: match.index,
            endPos: match.index + match[0].length,
            fullMatch: match[0],
            isUnclosed: true
          });
        }
      }
    });

    // 如果没有找到思考标签，直接返回原文本
    if (matches.length === 0) {
      return {
        segments: [{ type: 'text', content: text }]
      };
    }

    // 按照位置排序匹配结果
    matches.sort((a, b) => a.startPos - b.startPos);

    // 构建段落数组，保留原始顺序
    const segments = [];
    let currentPos = 0;

    matches.forEach(match => {
      // 添加匹配前的文本
      if (match.startPos > currentPos) {
        const textBefore = text.substring(currentPos, match.startPos);
        if (textBefore.trim()) {
          segments.push({
            type: 'text',
            content: textBefore
          });
        }
      }

      // 添加思考内容
      segments.push({
        type: 'thinking',
        subtype: match.subtype,
        content: match.content,
        isUnclosed: match.isUnclosed
      });

      // 更新当前位置
      currentPos = match.endPos;
    });

    // 添加最后一个匹配后的文本
    if (currentPos < text.length) {
      const textAfter = text.substring(currentPos);
      if (textAfter.trim()) {
        segments.push({
          type: 'text',
          content: textAfter
        });
      }
    }

    return { segments };
  } catch (error) {
    console.error('思考内容解析失败:', error);
    // 发生错误时，将所有内容作为普通文本返回
    return {
      segments: [{ type: 'text', content: text }]
    };
  }
};

/**
 * Mermaid图表渲染组件
 */
const MermaidRenderer = ({ chart }) => {
  const [svg, setSvg] = React.useState('');
  const [error, setError] = React.useState(null);
  const [id] = React.useState(`mermaid-${Math.random().toString(36).substring(2, 10)}`);
  const [lastRenderedChart, setLastRenderedChart] = React.useState('');
  const [isRendering, setIsRendering] = React.useState(false);

  // 检查图表内容是否看起来完整
  const isChartComplete = React.useCallback((chartContent) => {
    if (!chartContent || chartContent.trim() === '') return false;

    const trimmed = chartContent.trim();

    // 检查是否有基本的mermaid语法结构
    const hasValidStart = /^(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|gitgraph|pie|gantt|mindmap|timeline|quadrantChart|requirement|c4Context)/i.test(trimmed);

    if (!hasValidStart) return false;

    // 检查是否有未闭合的引号或括号（简单检查）
    const openQuotes = (trimmed.match(/"/g) || []).length;
    const openBrackets = (trimmed.match(/\[/g) || []).length;
    const closeBrackets = (trimmed.match(/\]/g) || []).length;
    const openParens = (trimmed.match(/\(/g) || []).length;
    const closeParens = (trimmed.match(/\)/g) || []).length;

    // 基本的平衡检查
    const isBalanced = (openQuotes % 2 === 0) &&
                      (openBrackets === closeBrackets) &&
                      (openParens === closeParens);

    return isBalanced;
  }, []);

  useEffect(() => {
    if (!chart || isRendering) return;

    // 如果内容与上次渲染的内容相同，跳过渲染
    if (chart === lastRenderedChart && svg) return;

    // 检查图表内容是否看起来完整
    if (!isChartComplete(chart)) {
      // 如果图表不完整，显示原始代码但不尝试渲染
      setError(new Error('图表内容不完整'));
      return;
    }

    // 添加防抖机制，避免在流式输出过程中频繁重新渲染
    const debounceTimer = setTimeout(() => {
      setIsRendering(true);

      try {
        // 直接尝试渲染，不进行语法验证
        // 禁用所有警告和错误输出
        mermaid.render(id, chart).then((result) => {
          setSvg(result.svg);
          setError(null);
          setLastRenderedChart(chart);
          setIsRendering(false);
        }).catch(err => {
          // 渲染失败时静默处理，不输出任何警告
          // 可以选择显示原始代码或者空白
          setSvg('');
          setError(err);
          setIsRendering(false);
        });
      } catch (err) {
        // 解析失败时静默处理
        setSvg('');
        setError(err);
        setIsRendering(false);
      }
    }, 300); // 300ms 防抖延迟

    // 清理函数
    return () => {
      clearTimeout(debounceTimer);
    };
  }, [chart, id, lastRenderedChart, svg, isRendering, isChartComplete]);

  // 如果正在渲染且已有SVG，显示当前SVG避免闪烁
  if (isRendering && svg) {
    return (
      <div className="mermaid-diagram" dangerouslySetInnerHTML={{ __html: svg }} />
    );
  }

  // 如果渲染失败或没有SVG，返回原始代码
  if (error || !svg) {
    return (
      <div className="mermaid-fallback" style={{
        padding: '10px',
        backgroundColor: '#f5f5f5',
        borderRadius: '4px',
        fontFamily: 'monospace',
        fontSize: '12px',
        whiteSpace: 'pre-wrap'
      }}>
        {chart}
      </div>
    );
  }

  return (
    <div className="mermaid-diagram" dangerouslySetInnerHTML={{ __html: svg }} />
  );
};

/**
 * 通用Markdown渲染组件
 * 用于统一渲染Markdown内容，支持代码高亮、数学公式、mermaid图表等
 * @param {Object} props - 组件属性
 * @param {string} props.content - Markdown内容
 * @param {boolean} props.showLineNumbers - 是否显示代码行号，默认为true
 * @returns {JSX.Element} 渲染后的Markdown内容
 */
export const MarkdownRenderer = ({ content, showLineNumbers = true }) => {
  if (!content) return null;

  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm, remarkMath]}
      rehypePlugins={[rehypeKatex, rehypeRaw]}
      components={{
        code({node, inline, className, children, ...props}) {
          const match = /language-(\w+)/.exec(className || '');

          // 处理行内代码
          if (inline) {
            return <code className={className} {...props}>{children}</code>;
          }

          // 获取代码内容
          const codeContent = String(children).replace(/\n$/, '');

          // 处理mermaid图表
          if (match && match[1] === 'mermaid') {
            // 直接渲染mermaid图表，不进行语法验证
            const trimmedCode = codeContent.trim();

            // 直接使用MermaidRenderer渲染，让它内部处理错误
            return <MermaidRenderer chart={trimmedCode} />;
          }

          // 处理普通代码块
          return match ? (
            <SyntaxHighlighter
              style={oneDark}
              language={match[1]}
              PreTag="div"
              showLineNumbers={showLineNumbers}
              {...props}
            >
              {codeContent}
            </SyntaxHighlighter>
          ) : (
            <code className={className} {...props}>
              {children}
            </code>
          );
        }
      }}
    >
      {content}
    </ReactMarkdown>
  );
};

/**
 * 渲染思考内容的组件
 */
export const ThinkingContentRenderer = ({ thinkingContent, isUnclosed }) => {
  // 确保thinkingContent存在且为字符串
  if (!thinkingContent || typeof thinkingContent !== 'string' || thinkingContent.trim() === '') {
    return null;
  }

  // 移除标签并清理内容
  const cleanedContent = thinkingContent
    .replace(/<\/?think>/g, '')
    .replace(/<\/?thinking>/g, '')
    .replace(/<\/?observing>/g, '')
    .trim();

  // 如果清理后内容为空，不渲染
  if (!cleanedContent) {
    return null;
  }

  return (
    <div style={{ marginBottom: '8px' }}>
      <Collapse
        ghost
        size="small"
        defaultActiveKey={isUnclosed ? ['1'] : []} // 如果是未闭合的标签（流式状态），默认展开
        items={[
          {
            key: '1',
            label: (
              <Text type="secondary">
                <BulbOutlined style={{ marginRight: '5px' }} />
                查看思考过程 {isUnclosed && <Tag color="grey" size="small">思考中</Tag>}
              </Text>
            ),
            children: (
              <div
                style={{
                  whiteSpace: 'pre-wrap',
                  fontSize: '12px',
                  padding: '12px',
                  backgroundColor: '#f9f9f9',
                  borderRadius: '6px',
                  border: '1px solid "#f0f0f0"',
                  overflowY: 'auto'
                }}
              >
                {cleanedContent}
              </div>
            )
          }
        ]}
      />
    </div>
  );
};

/**
 * 内容渲染组件
 * 用于解析和展示会话中的各种内容，包括普通文本、工具调用和思考内容
 * 作为所有消息内容渲染的中心组件
 */
function ConversationExtraction({
  content,
  messageThinking,
  isToolCallOnly = false,
  message = null
}) {
  // 处理消息内容
  const { displayContent, thinkingContent } = useMemo(() => {
    // 如果提供了完整的message对象，优先从message中提取内容
    let displayContent = message ? message.content : content;
    let thinkingContent = message ? message.thinking : messageThinking;

    // 如果有单独的thinking字段，确保格式正确
    if (thinkingContent && typeof thinkingContent === 'string' &&
        !thinkingContent.includes('<think>') &&
        !thinkingContent.includes('<thinking>') &&
        !thinkingContent.includes('<observing>')) {
      thinkingContent = `<think>\n${thinkingContent}\n</think>`;
    }

    return {
      displayContent: displayContent || '',
      thinkingContent
    };
  }, [content, messageThinking, message]);

  // 使用新的解析器处理思考内容和工具调用

  // 解析工具调用和结果，并保留原始位置信息
  const parseToolCalls = (text) => {
    if (!text) return { segments: [] };

    // 存储工具调用ID与索引的映射，用于关联调用和结果
    const toolCallIdMap = {};

    try {
      // 查找所有可能的JSON对象
      const jsonSegments = [];
      let currentText = '';
      let currentPos = 0;

      while (currentPos < text.length) {
        const startPos = text.indexOf('{"content":', currentPos);

        // 如果找不到更多的JSON对象，将剩余文本作为普通内容添加
        if (startPos === -1) {
          if (currentPos < text.length) {
            currentText += text.substring(currentPos);
          }
          break;
        }

        // 将JSON前的文本作为普通内容添加
        if (startPos > currentPos) {
          currentText += text.substring(currentPos, startPos);
        }

        // 查找匹配的JSON结束位置
        let endPos = startPos;
        let braceCount = 0;
        let inString = false;
        let escapeNext = false;

        for (let i = startPos; i < text.length; i++) {
          const char = text[i];

          if (escapeNext) {
            escapeNext = false;
            continue;
          }

          if (char === '\\') {
            escapeNext = true;
            continue;
          }

          if (char === '"' && !escapeNext) {
            inString = !inString;
            continue;
          }

          if (!inString) {
            if (char === '{') braceCount++;
            if (char === '}') {
              braceCount--;
              if (braceCount === 0) {
                endPos = i + 1;
                break;
              }
            }
          }
        }

        // 如果找到完整的JSON
        if (endPos > startPos) {
          const jsonStr = text.substring(startPos, endPos);

          try {
            // 尝试解析JSON，如果失败则作为普通文本处理
            let jsonObj;
            try {
              jsonObj = JSON.parse(jsonStr);
            } catch (parseError) {
              // 记录警告而不是错误，避免控制台出现大量错误信息
              console.warn('JSON解析失败:', parseError);
              // 将JSON字符串作为普通文本添加
              currentText += jsonStr;
              currentPos = endPos;
              continue;
            }

            // 检查是否为空白内容的JSON，如果是则跳过
            if (jsonObj.content && jsonObj.content.trim() === "" && !jsonObj.meta) {
              // 跳过空白内容的JSON
              currentPos = endPos;
              continue;
            }

            // 如果当前累积的文本不为空，添加为文本段
            if (currentText.trim() !== '') {
              jsonSegments.push({
                type: 'text',
                content: currentText.trim()
              });
              currentText = '';
            }

            // 解析工具调用
            if (jsonObj.meta) {
              // 处理工具调用动作 - ToolCallAction
              if (jsonObj.meta.ToolCallAction) {
                const actionData = jsonObj.meta.ToolCallAction;
                const toolCallId = jsonObj.meta.toolCallId || '';

                // 创建工具调用对象
                const toolCall = {
                  type: 'toolCall',
                  subtype: 'action',
                  function: actionData.Function,
                  arguments: actionData.Arguments,
                  toolCallId: toolCallId,
                  result: null,
                  // 存储原始JSON对象
                  rawJson: jsonObj
                };

                // 记录到段落集合中
                const index = jsonSegments.length;
                jsonSegments.push(toolCall);

                // 记录工具调用ID映射
                if (toolCallId) {
                  toolCallIdMap[toolCallId] = index;
                }
              }
              // 处理工具调用结果 - ToolCallResult 或 role:tool 格式
              else if (jsonObj.meta.ToolCallResult || (jsonObj.meta.type === 'toolResult' && jsonObj.meta.role === 'tool')) {
                // 支持两种格式：旧的ToolCallResult格式和新的role:tool格式
                let resultContent, toolName, toolCallId, status, toolParameter;

                if (jsonObj.meta.ToolCallResult) {
                  // 旧格式
                  resultContent = jsonObj.meta.ToolCallResult;
                  toolName = jsonObj.meta.toolName || '';
                  toolCallId = jsonObj.meta.toolCallId || '';
                  status = jsonObj.meta.status || 'success';
                  toolParameter = jsonObj.meta.toolParameter || '{}';
                } else {
                  // 新的role:tool格式
                  resultContent = jsonObj.meta.content;
                  toolName = jsonObj.meta.tool_name || '';
                  toolCallId = jsonObj.meta.tool_call_id || '';
                  status = jsonObj.meta.status || 'success'; // 读取status字段，默认成功状态
                  toolParameter = jsonObj.meta.tool_parameter || '{}';
                }

                // 创建结果对象
                const resultObj = {
                  content: resultContent,
                  toolName: toolName,
                  status: status,
                  // 存储原始JSON对象
                  rawJson: jsonObj
                };

                // 如果有对应的工具调用，将结果添加到该工具调用中
                if (toolCallId && toolCallIdMap[toolCallId] !== undefined) {
                  const index = toolCallIdMap[toolCallId];
                  jsonSegments[index].result = resultObj;
                  jsonSegments[index].status = status; // 更新状态到对应的工具调用中
                } else {
                  // 如果没有找到对应的工具调用，创建一个新的工具调用对象并添加到段落集合
                  // 标记为自动创建的，以便在渲染时可以区别处理
                  // 使用toolParameter作为arguments
                  let toolArguments = {};
                  try {
                    // 尝试解析工具参数
                    if (typeof toolParameter === 'string') {
                      // 如果是字符串，尝试解析JSON
                      if (toolParameter.trim() === '' || toolParameter === '{}') {
                        // 空字符串或空对象，使用默认空对象
                        toolArguments = {};
                      } else {
                        // 尝试解析JSON字符串
                        toolArguments = JSON.parse(toolParameter);
                      }
                    } else if (typeof toolParameter === 'object' && toolParameter !== null) {
                      // 如果已经是对象，直接使用
                      toolArguments = toolParameter;
                    } else {
                      // 其他情况使用空对象
                      toolArguments = {};
                    }
                  } catch (e) {
                    console.warn('工具参数解析失败:', e);
                    console.warn('原始参数内容:', toolParameter);
                    // 解析失败时，尝试创建一个包含原始内容的对象
                    toolArguments = {
                      raw_parameter: toolParameter,
                      parse_error: e.message
                    };
                  }

                  jsonSegments.push({
                    type: 'toolCall',
                    subtype: 'action',
                    function: toolName || 'unknown',
                    arguments: toolArguments,
                    toolCallId: toolCallId,
                    status: status, // 设置工具调用状态
                    result: resultObj,
                    isAutoCreated: true // 标记为自动创建的工具调用
                  });
                }
              }
              // 处理其他类型的元数据 (如果需要)
              else if (jsonObj.content) {
                // 如果有常规内容，添加为文本段
                if (jsonObj.content.trim() !== '') {
                  jsonSegments.push({
                    type: 'text',
                    content: jsonObj.content.trim()
                  });
                }
              }
            } else if (jsonObj.content) {
              // 如果只有常规内容，添加为文本段
              if (jsonObj.content.trim() !== '') {
                jsonSegments.push({
                  type: 'text',
                  content: jsonObj.content.trim()
                });
              }
            }
          } catch (e) {
            console.error('JSON解析失败:', e);
            // 如果解析失败，将该JSON字符串作为普通文本添加
            currentText += jsonStr;
          }

          currentPos = endPos;
        } else {
          // 如果无法找到完整的JSON，将剩余文本作为普通内容添加
          currentText += text.substring(currentPos);
          break;
        }
      }

      // 处理最后可能剩余的文本
      if (currentText.trim() !== '') {
        jsonSegments.push({
          type: 'text',
          content: currentText.trim()
        });
      }

      // 合并相邻的文本段，避免出现多个分散的文本块
      const mergedSegments = [];
      let currentTextSegment = null;

      for (const segment of jsonSegments) {
        if (segment.type === 'text') {
          if (currentTextSegment) {
            currentTextSegment.content += '\n\n' + segment.content;
          } else {
            currentTextSegment = { ...segment };
            mergedSegments.push(currentTextSegment);
          }
        } else {
          currentTextSegment = null;
          mergedSegments.push(segment);
        }
      }

      // 移除所有HTML注释
      mergedSegments.forEach(segment => {
        if (segment.type === 'text') {
          // 移除所有HTML注释
          segment.content = segment.content.replace(/<!-- .*? -->/g, '').trim();
        }
      });

      // 过滤掉空文本段
      return {
        segments: mergedSegments.filter(segment =>
          segment.type !== 'text' || segment.content.trim() !== ''
        )
      };
    } catch (error) {
      console.error('工具调用解析失败:', error);
      // 发生错误时，将所有内容作为普通文本返回
      return {
        segments: [{ type: 'text', content: text }]
      };
    }
  };

  // 获取工具图标
  const getToolIcon = (toolName) => {
    const toolIcons = {
      'sequentialthinking': <ThunderboltOutlined />,
      'search_web': <SearchOutlined />,
      'web_search': <SearchOutlined />,
      'web_fetch': <GlobalOutlined />,
      'get_agent_var': <DatabaseOutlined />,
      'set_agent_var': <DatabaseOutlined />,
      'code': <CodeOutlined />,
      'api': <ApiOutlined />,
      'default': <ToolOutlined />
    };

    return toolIcons[toolName] || toolIcons.default;
  };

  // 渲染文本内容
  const renderTextContent = (segment, index) => {
    if (segment.type === 'text') {
      // 不再尝试自动检测和修复代码块外的Mermaid内容
      // 只渲染Markdown内容，由Markdown组件中的代码块处理器处理完整的mermaid标签

      return (
        <div key={`text-${index}`} className="text-content" style={{
          marginBottom: '12px',
          width: '100%', // 确保文本内容宽度与父容器一致
          maxWidth: '100%', // 确保不超过父容器宽度
          overflowX: 'auto', // 添加水平滚动以防内容溢出
          wordBreak: 'break-word', // 确保长单词可以换行
          overflowWrap: 'break-word' // 确保长单词可以换行
        }}>
          <MarkdownRenderer content={segment.content} />
        </div>
      );
    }
    return null;
  };

  // 渲染思考内容段落
  const renderThinkingSegment = (segment, index) => {
    if (segment.type === 'thinking') {
      return <ThinkingContentRenderer
        key={`thinking-${index}`}
        thinkingContent={segment.content}
        isUnclosed={segment.isUnclosed}
      />;
    }
    return null;
  };

  // 渲染工具调用卡片
  const renderToolCallCard = (toolCall, index) => {
    // 只处理toolCall类型的段落
    if (toolCall.type === 'toolCall' && toolCall.subtype === 'action') {
      // 检查是否有结果
      const hasResult = toolCall.result !== null;

      // 获取状态标签
      let statusTag = <Tag color="processing">处理中...</Tag>;

      if (hasResult) {
        // 只使用status字段判断结果状态
        const status = toolCall.result.status || 'success';
        if (status === 'error') {
          statusTag = <Tag icon={<CloseCircleOutlined />} color="error">失败</Tag>;
        } else if (status === 'warning') {
          statusTag = <Tag icon={<WarningOutlined />} color="warning">警告</Tag>;
        } else {
          statusTag = <Tag icon={<CheckCircleOutlined />} color="success">成功</Tag>;
        }
      }

      return (
        <Card
          key={`tool-${index}`}
          size="small"
          title={
            <Space>
              {getToolIcon(toolCall.function)}
              <Text strong>{toolCall.function}</Text>
              {statusTag}
              {toolCall.isAutoCreated && <Tag color="blue" size="small">自动生成</Tag>}
            </Space>
          }
          style={{
            marginBottom: '12px',
            borderRadius: '8px',
            boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
            width: '100%', // 确保工具调用卡片宽度与父容器一致
            maxWidth: '100%' // 确保不超过父容器宽度
          }}
        >
          <div>
            <Collapse
              variant="borderless"
              expandIcon={({ isActive }) => isActive ? <DownOutlined /> : <RightOutlined />}
              defaultActiveKey={[]} // 默认闭合工具卡片的collapse
              size="small"
              items={[
                ...(hasResult ? [{
                  key: '2',
                  label: <Text strong>结果</Text>,
                  style: { border: 'none' },
                  children: (
                    <div style={{ maxHeight: '300px', overflow: 'auto' }}>
                      {/* 显示完整的ToolCallResult内容 */}
                      {toolCall.result.rawJson ? (
                        <ReactJson
                          src={toolCall.result.rawJson}
                          theme="chalk"
                          displayDataTypes={false}
                          collapsed={2}
                        />
                      ) : (
                        typeof toolCall.result.content === 'string' ? (
                          <div className="tool-result-content">
                            <MarkdownRenderer
                              content={toolCall.result.content}
                              showLineNumbers={false}
                            />
                          </div>
                        ) : (
                          (() => {
                            try {
                              // 尝试解析JSON，如果失败则显示原始字符串
                              return (
                                <ReactJson
                                  src={toolCall.result.content}
                                  theme="chalk"
                                  displayDataTypes={false}
                                  collapsed={1}
                                />
                              );
                            } catch (e) {
                              console.warn('工具调用结果JSON解析失败:', e);
                              // 解析失败时显示警告和原始内容
                              return (
                                <div>
                                  <div style={{
                                    padding: '10px',
                                    backgroundColor: '#fffbe6',
                                    border: '1px solid #ffe58f',
                                    borderRadius: '4px',
                                    color: '#874d00',
                                    marginBottom: '10px'
                                  }}>
                                    <p>JSON解析警告: {e.message}</p>
                                  </div>
                                  <pre style={{
                                    maxHeight: '150px',
                                    overflow: 'auto',
                                    padding: '8px',
                                    backgroundColor: '#f5f5f5',
                                    borderRadius: '4px'
                                  }}>
                                    {typeof toolCall.result.content === 'string'
                                      ? toolCall.result.content
                                      : JSON.stringify(toolCall.result.content, null, 2)}
                                  </pre>
                                </div>
                              );
                            }
                          })()
                        )
                      )}
                    </div>
                  )
                }] : [])
              ]}
            />
          </div>
        </Card>
      );
    }
    return null;
  };

  // 解析内容
  // 先解析思考内容
  const { segments: thinkingSegments } = parseThinking(displayContent || '');

  // 对每个文本段落解析工具调用
  const allSegments = [];

  thinkingSegments.forEach(segment => {
    if (segment.type === 'text') {
      // 对文本段落解析工具调用
      const { segments: toolSegments } = parseToolCalls(segment.content);
      allSegments.push(...toolSegments);
    } else {
      // 保留思考内容段落
      allSegments.push(segment);
    }
  });

  // 如果没有任何段落 (内容为空)
  if (allSegments.length === 0) {
    return null;
  }

  // 检查是否有工具调用 - 通过解析后的段落判断
  const hasToolCall = allSegments.some(segment => segment.type === 'toolCall');

  // 如果只有一个文本段落，且没有工具调用，且指定只处理工具调用，则返回null
  if (isToolCallOnly && !hasToolCall) {
    return null;
  }

  // 渲染组件 - 包含思考内容和段落内容
  return (
    <div className="conversation-extraction">
      {/* 渲染单独的思考内容（如果存在） */}
      {thinkingContent && typeof thinkingContent === 'string' && thinkingContent.trim() !== '' && (
        <ThinkingContentRenderer
          thinkingContent={thinkingContent}
          isUnclosed={
            (thinkingContent.includes('<thinking>') && !thinkingContent.includes('</thinking>')) ||
            (thinkingContent.includes('<think>') && !thinkingContent.includes('</think>')) ||
            (thinkingContent.includes('<observing>') && !thinkingContent.includes('</observing>'))
          }
        />
      )}

      {/* 渲染所有段落，包括普通文本、思考内容和工具调用 */}
      {allSegments.map((segment, index) => {
        if (segment.type === 'text') {
          return renderTextContent(segment, index);
        } else if (segment.type === 'toolCall') {
          return renderToolCallCard(segment, index);
        } else if (segment.type === 'thinking') {
          return renderThinkingSegment(segment, index);
        }
        return null;
      })}
    </div>
  );
}

export default ConversationExtraction;