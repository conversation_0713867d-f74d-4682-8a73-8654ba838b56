"""
功能域API路由模块

此模块按照功能域组织API路由，每个功能域有自己的Blueprint
"""


from app.api.routes.action_tasks import action_task_bp
from app.api.routes.agents import agent_bp
from app.api.routes.health import health_bp
from app.api.routes.messages import message_bp
from app.api.routes.model_configs import model_bp
from app.api.routes.roles import role_bp
from app.api.routes.settings import settings_bp
from app.api.routes.rules import rule_bp
from app.api.routes.action_spaces import action_space_bp
from app.api.routes.capabilities import capabilities_bp
from app.api.routes.tools import tools_bp
from app.api.routes.conversations import conversation_bp
from app.api.routes.agent_variables import agent_variable_bp
from app.api.routes.environment_variables import env_var_bp
from app.api.routes.mcp_servers import bp as mcp_api_bp
from app.api.routes.tool_schema_cache import bp as tool_schema_cache_bp
from app.api.routes.license import license_bp
from app.api.routes.auth import auth_bp
from app.api.routes.workspace import workspace_bp
from app.api.routes.logs import logs_bp
from app.api.routes.statistics import statistics_bp
from app.api.routes.external_variables import external_variables_bp
from app.api.routes.external_knowledge import external_knowledge_bp
from app.api.routes.knowledge import knowledge_bp
from app.api.routes.graph_enhancement import graph_enhancement_bp
from app.api.routes.graph_mcp import graph_mcp_bp
from app.api.routes.tidb_vector import tidb_vector_bp
from app.api.routes.vector_database import vector_db_bp

def register_api_blueprints(app):
    """
    注册所有API Blueprint到Flask应用

    Args:
        app: Flask应用实例
    """
    # 注册蓝图
    app.register_blueprint(agent_bp, url_prefix='/api')
    app.register_blueprint(action_task_bp, url_prefix='/api')
    app.register_blueprint(health_bp, url_prefix='/api')
    app.register_blueprint(message_bp, url_prefix='/api')
    app.register_blueprint(model_bp, url_prefix='/api')
    app.register_blueprint(role_bp, url_prefix='/api')
    app.register_blueprint(settings_bp, url_prefix='/api')
    app.register_blueprint(rule_bp, url_prefix='/api')
    app.register_blueprint(action_space_bp, url_prefix='/api')
    app.register_blueprint(capabilities_bp, url_prefix='/api')
    app.register_blueprint(tools_bp, url_prefix='/api')
    app.register_blueprint(conversation_bp, url_prefix='/api')
    app.register_blueprint(agent_variable_bp, url_prefix='/api')
    app.register_blueprint(env_var_bp, url_prefix='/api')
    app.register_blueprint(mcp_api_bp)
    app.register_blueprint(tool_schema_cache_bp, url_prefix='/api')
    app.register_blueprint(license_bp, url_prefix='/api')
    app.register_blueprint(auth_bp, url_prefix='/api')
    app.register_blueprint(workspace_bp, url_prefix='/api')
    app.register_blueprint(logs_bp, url_prefix='/api')
    app.register_blueprint(statistics_bp, url_prefix='/api')
    app.register_blueprint(external_variables_bp, url_prefix='/api')
    app.register_blueprint(external_knowledge_bp, url_prefix='/api')
    app.register_blueprint(knowledge_bp, url_prefix='/api')
    app.register_blueprint(graph_enhancement_bp, url_prefix='/api')
    app.register_blueprint(graph_mcp_bp)  # MCP接口不使用/api前缀
    app.register_blueprint(tidb_vector_bp)
    app.register_blueprint(vector_db_bp)