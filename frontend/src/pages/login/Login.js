import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, Alert, App } from 'antd';
import { UserOutlined, LockOutlined, TeamOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './Login.css';

const { Title, Text } = Typography;

/**
 * 登录页面组件
 * 提供管理员登录功能
 */
const Login = () => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { isAuthenticated, loading: authLoading, login } = useAuth();

  // 从location中获取重定向路径
  const from = location.state?.from?.pathname || '/home';

  // 检查是否已登录
  useEffect(() => {
    console.log('Login组件: 检查登录状态, 目标路径:', from, '认证状态:', isAuthenticated, '加载中:', authLoading);

    // 如果已认证且不在加载中，重定向到目标页面
    if (isAuthenticated && !authLoading) {
      console.log('Login组件: 已登录，重定向到:', from);
      // 已登录，重定向到首页或来源页面
      setTimeout(() => {
        navigate(from, { replace: true });
      }, 100); // 添加短暂延迟，确保状态更新
    }
  }, [navigate, from, isAuthenticated, authLoading]);

  // 处理登录表单提交
  const handleSubmit = async (values) => {
    setLoading(true);
    setError(null);

    try {
      const { username, password } = values;
      const result = await login(username, password);

      if (result.success) {
        message.success('登录成功');
        console.log('Login组件: 登录成功，将自动重定向到:', from);
        // 不需要手动重定向，useEffect会处理
      } else {
        // 登录失败，显示错误信息
        setError(result.message || '登录失败，请检查用户名和密码');
      }
    } catch (error) {
      console.error('登录失败:', error);
      setError(error.response?.data?.message || '登录失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-content">
        <div className="login-header">
          <TeamOutlined className="login-logo" />
          <Title level={2} className="login-title">多智能体专家决策与执行系统</Title>
          <Text type="secondary" className="login-subtitle">管理员登录</Text>
        </div>

        <Card
          className="login-card"
          bordered={false}
        >
          {error && (
            <Alert
              message="登录失败"
              description={error}
              type="error"
              showIcon
              style={{ marginBottom: 24 }}
            />
          )}

          <Form
            form={form}
            name="login"
            layout="vertical"
            onFinish={handleSubmit}
            autoComplete="off"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名"
                size="large"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                size="large"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="login-button"
                size="large"
                block
              >
                登录
              </Button>
            </Form.Item>
          </Form>
        </Card>

        <div className="login-footer">
          <Text type="secondary">© 2025 上海同悦信息科技有限公司. All Rights Reserved.</Text>
        </div>
      </div>
    </div>
  );
};

export default Login;
