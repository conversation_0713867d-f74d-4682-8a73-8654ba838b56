import { useState, useEffect, useCallback } from 'react';
import {
  Typography,
  Form,
  Input,
  Select,
  Card,
  Button,
  Switch,
  Row,
  Col,
  Space,
  Tooltip,
  InputNumber,
  App,
  Modal,
  Tabs,
  Alert,
  Steps,
  Spin,
  Progress
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  GlobalOutlined,
  ApiOutlined,
  MessageOutlined,
  SettingOutlined,
  DatabaseOutlined,
  RobotOutlined,
  ClockCircleOutlined,
  BugOutlined,
  <PERSON>boltOutlined,
  EyeOutlined,
  SaveFilled,
  BulbOutlined,
  EditOutlined,
  ToolOutlined,
  FieldTimeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  LinkOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { settingsAPI } from '../../services/api/settings';
import { modelConfigAPI } from '../../services/api/model';
import { vectorDatabaseAPI, validateProviderConfig, getProviderDisplayName } from '../../services/api/vectorDatabase';

const { Title, Text } = Typography;

// 定义主要时区列表
const TIMEZONE_OPTIONS = [
  { value: 'Asia/Shanghai', label: '亚洲/上海 (GMT+8)' },
  { value: 'Asia/Hong_Kong', label: '亚洲/香港 (GMT+8)' },
  { value: 'Asia/Tokyo', label: '亚洲/东京 (GMT+9)' },
  { value: 'Asia/Singapore', label: '亚洲/新加坡 (GMT+8)' },
  { value: 'Europe/London', label: '欧洲/伦敦 (GMT+0/+1)' },
  { value: 'Europe/Paris', label: '欧洲/巴黎 (GMT+1/+2)' },
  { value: 'Europe/Berlin', label: '欧洲/柏林 (GMT+1/+2)' },
  { value: 'America/New_York', label: '美洲/纽约 (GMT-5/-4)' },
  { value: 'America/Los_Angeles', label: '美洲/洛杉矶 (GMT-8/-7)' },
  { value: 'America/Chicago', label: '美洲/芝加哥 (GMT-6/-5)' },
  { value: 'Australia/Sydney', label: '澳洲/悉尼 (GMT+10/+11)' },
  { value: 'Pacific/Auckland', label: '太平洋/奥克兰 (GMT+12/+13)' },
  { value: 'UTC', label: '协调世界时 (UTC)' }
];

// 测试步骤定义
const TEST_STEPS = [
  {
    title: '配置验证',
    description: '验证向量数据库配置参数',
    icon: <SettingOutlined />,
    key: 'config_validation'
  },
  {
    title: '连接测试',
    description: '测试数据库连接是否正常',
    icon: <LinkOutlined />,
    key: 'connection_test'
  },
  {
    title: '向量操作',
    description: '测试向量存储和搜索功能',
    icon: <SearchOutlined />,
    key: 'vector_operations'
  }
];

const GeneralSettingsPage = () => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [modelConfigs, setModelConfigs] = useState([]);
  const [defaultModels, setDefaultModels] = useState({});
  const [useBuiltinVectorDB, setUseBuiltinVectorDB] = useState(true);

  // 向量数据库配置modal相关状态
  const [vectorDBConfigVisible, setVectorDBConfigVisible] = useState(false);
  const [vectorDBConfigForm] = Form.useForm();
  const [vectorDBConfigLoading, setVectorDBConfigLoading] = useState(false);
  const [currentVectorDBConfig, setCurrentVectorDBConfig] = useState({});
  const [testConnectionLoading, setTestConnectionLoading] = useState(false);

  // 辅助生成提示词模板管理状态
  const [promptTemplateModalVisible, setPromptTemplateModalVisible] = useState(false);
  const [promptTemplateForm] = Form.useForm();
  const [promptTemplateLoading, setPromptTemplateLoading] = useState(false);

  // 测试步骤Modal状态 - 合并为单个对象
  const [testStepsModal, setTestStepsModal] = useState({
    visible: false,
    currentStep: 0,
    stepsData: [],
    result: null,
    providerName: ''
  });



  // 获取模型配置列表
  const fetchModelConfigs = useCallback(async () => {
    try {
      console.log('开始获取模型配置...');
      const configs = await modelConfigAPI.getAll();
      console.log('获取到的模型配置:', configs);
      // 过滤出具备文本能力的模型
      const textCapableModels = configs.filter(config =>
        !config.capabilities ||
        config.capabilities.length === 0 ||
        config.capabilities.includes('text')
      );
      console.log('过滤后的文本模型:', textCapableModels);
      setModelConfigs(textCapableModels);
    } catch (error) {
      console.error('获取模型配置失败:', error);
      // 如果获取失败，设置为空数组，但保留默认选项
      setModelConfigs([]);
    }
  }, []);

  // 获取默认模型配置
  const fetchDefaultModels = useCallback(async () => {
    try {
      console.log('开始获取默认模型配置...');
      const defaults = await modelConfigAPI.getDefaults();
      console.log('获取到的默认模型配置:', defaults);
      setDefaultModels(defaults);
    } catch (error) {
      console.error('获取默认模型配置失败:', error);
      setDefaultModels({});
    }
  }, []);

  // 获取系统设置
  const fetchSettings = useCallback(async () => {
    setLoading(true);
    try {
      // 使用API获取设置
      const data = await settingsAPI.getSettings();
      console.log('获取到系统设置:', data);

      // 设置表单值
      form.setFieldsValue({
        apiUrl: data.api_url || process.env.REACT_APP_API_URL || 'http://localhost:8080/api',
        enableDebugMode: data.enableDebugMode || false,
        maxConversationHistoryLength: data.maxConversationHistoryLength || 10,
        streamingEnabled: data.streamingEnabled || true,
        timezone: data.timezone || 'Asia/Shanghai',
        // 新增的数据库特有设置
        storeLlmErrorMessages: data.storeLlmErrorMessages !== undefined ? data.storeLlmErrorMessages : true,
        includeThinkingContentInContext: data.includeThinkingContentInContext !== undefined ? data.includeThinkingContentInContext : true,
        splitToolCallsInHistory: data.splitToolCallsInHistory !== undefined ? data.splitToolCallsInHistory : true,
        // 向量数据库设置
        useBuiltinVectorDB: data.useBuiltinVectorDB !== undefined ? data.useBuiltinVectorDB : true,
        vectorDBProvider: data.vectorDBProvider || 'aliyun',
        // 辅助生成设置
        enableAssistantGeneration: data.enableAssistantGeneration !== undefined ? data.enableAssistantGeneration : true,
        assistantGenerationModel: data.assistantGenerationModel || 'default',
        // 超时配置设置
        httpConnectionTimeout: data.httpConnectionTimeout || 30,
        httpReadTimeout: data.httpReadTimeout || 300,
        streamSocketTimeout: data.streamSocketTimeout || 60,
        defaultModelTimeout: data.defaultModelTimeout || 60
      });

      // 更新向量数据库状态
      setUseBuiltinVectorDB(data.useBuiltinVectorDB !== undefined ? data.useBuiltinVectorDB : true);

      // 加载向量数据库配置
      if (data.vectorDBConfig) {
        setCurrentVectorDBConfig(data.vectorDBConfig);
      }

      // 打印日志，帮助调试
      console.log('设置表单值:', {
        apiUrl: data.api_url,
        enableDebugMode: data.enableDebugMode,
        maxConversationHistoryLength: data.maxConversationHistoryLength,
        streamingEnabled: data.streamingEnabled,
        timezone: data.timezone,
        storeLlmErrorMessages: data.storeLlmErrorMessages,
        includeThinkingContentInContext: data.includeThinkingContentInContext
      });
      setLoading(false);
    } catch (error) {
      console.error('获取系统设置失败:', error);
      // 设置默认值，确保表单能正常工作
      form.setFieldsValue({
        apiUrl: process.env.REACT_APP_API_URL || 'http://localhost:8080/api',
        enableDebugMode: false,
        maxConversationHistoryLength: 10,
        streamingEnabled: true,
        timezone: 'Asia/Shanghai',
        storeLlmErrorMessages: true,
        includeThinkingContentInContext: true,
        splitToolCallsInHistory: true,
        useBuiltinVectorDB: true,
        vectorDBProvider: 'aliyun',
        // 辅助生成设置默认值
        enableAssistantGeneration: true,
        assistantGenerationModel: 'default',
        // 超时配置默认值
        httpConnectionTimeout: 30,
        httpReadTimeout: 300,
        streamSocketTimeout: 60,
        defaultModelTimeout: 60
      });
      message.error('获取系统设置失败，已加载默认设置: ' + (error.message || '未知错误'));
      setLoading(false);
    }
  }, [form, message]);

  useEffect(() => {
    fetchSettings();
    fetchModelConfigs();
    fetchDefaultModels();
  }, [fetchSettings, fetchModelConfigs, fetchDefaultModels]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 将前端表单字段名转换为后端API期望的格式
      const apiData = {
        apiUrl: values.apiUrl,
        enableDebugMode: values.enableDebugMode,
        maxConversationHistoryLength: values.maxConversationHistoryLength,
        streamingEnabled: values.streamingEnabled,
        timezone: values.timezone,
        // 新增的数据库特有设置
        storeLlmErrorMessages: values.storeLlmErrorMessages,
        includeThinkingContentInContext: values.includeThinkingContentInContext,
        splitToolCallsInHistory: values.splitToolCallsInHistory,
        // 向量数据库设置
        useBuiltinVectorDB: values.useBuiltinVectorDB,
        vectorDBProvider: values.vectorDBProvider,
        // 辅助生成设置
        enableAssistantGeneration: values.enableAssistantGeneration,
        assistantGenerationModel: values.assistantGenerationModel,
        // 超时配置设置
        httpConnectionTimeout: values.httpConnectionTimeout,
        httpReadTimeout: values.httpReadTimeout,
        streamSocketTimeout: values.streamSocketTimeout,
        defaultModelTimeout: values.defaultModelTimeout
      };

      // 调用API保存设置
      const result = await settingsAPI.updateSettings(apiData);

      if (result.success) {
        message.success('系统设置保存成功');
      } else {
        message.error('保存失败: ' + (result.message || '未知错误'));
      }
      setLoading(false);
    } catch (error) {
      console.error('保存系统设置失败:', error);
      message.error('保存系统设置失败: ' + (error.message || '未知错误'));
      setLoading(false);
    }
  };

  const handleReset = () => {
    fetchSettings();
    message.success('系统设置已重置');
  };

  // 获取提示词模板
  const fetchPromptTemplates = useCallback(async () => {
    try {
      const templates = await settingsAPI.getPromptTemplates();
      promptTemplateForm.setFieldsValue(templates);
    } catch (error) {
      console.error('获取提示词模板失败:', error);
      message.error('获取提示词模板失败: ' + (error.message || '未知错误'));
    }
  }, [promptTemplateForm, message]);

  // 打开提示词模板管理modal
  const handleOpenPromptTemplateModal = () => {
    setPromptTemplateModalVisible(true);
    fetchPromptTemplates();
  };

  // 打开向量数据库配置modal
  const handleOpenVectorDBConfigModal = () => {
    // 获取当前的向量数据库配置
    const currentProvider = form.getFieldValue('vectorDBProvider') || 'aliyun';
    const currentConfig = currentVectorDBConfig[currentProvider] || {};

    console.log('打开向量数据库配置Modal:', {
      currentProvider,
      currentConfig,
      allConfig: currentVectorDBConfig
    });

    vectorDBConfigForm.setFieldsValue({
      provider: currentProvider,
      ...currentConfig
    });

    setVectorDBConfigVisible(true);
  };

  // 处理向量数据库配置保存
  const handleVectorDBConfigSave = async () => {
    try {
      const values = await vectorDBConfigForm.validateFields();
      setVectorDBConfigLoading(true);

      const { provider, ...config } = values;

      // 更新当前配置
      const newConfig = {
        ...currentVectorDBConfig,
        [provider]: config
      };

      setCurrentVectorDBConfig(newConfig);

      // 保存配置到后端
      await settingsAPI.updateSettings({
        vectorDBConfig: newConfig
      });

      message.success('向量数据库配置保存成功');
      setVectorDBConfigVisible(false);
    } catch (error) {
      console.error('保存向量数据库配置失败:', error);
      message.error('保存向量数据库配置失败');
    } finally {
      setVectorDBConfigLoading(false);
    }
  };

  // 显示详细的测试结果
  const showDetailedTestResult = (providerDisplayName, result) => {
    const { info } = result;

    if (!info || !info.test_levels) {
      return;
    }

    const { test_levels, embedding_model, performance_metrics } = info;

    // 构建详细信息内容
    let detailContent = [];

    // 分层测试结果
    detailContent.push('📋 测试层级结果:');

    const levels = [
      { key: 'config_validation', name: '配置验证', icon: '⚙️' },
      { key: 'connection_test', name: '连接测试', icon: '🔗' },
      { key: 'vector_operations', name: '向量操作', icon: '🔍' }
    ];

    levels.forEach(level => {
      const levelResult = test_levels[level.key];
      if (levelResult) {
        const status = levelResult.passed ? '✅' : '❌';
        detailContent.push(`  ${level.icon} ${level.name}: ${status} ${levelResult.message}`);
      }
    });

    // 嵌入模型信息
    if (embedding_model) {
      detailContent.push('');
      detailContent.push('🤖 嵌入模型信息:');
      detailContent.push(`  模型名称: ${embedding_model.name}`);
      detailContent.push(`  提供商: ${embedding_model.provider}`);
    }

    // 性能指标
    if (performance_metrics && Object.keys(performance_metrics).length > 0) {
      detailContent.push('');
      detailContent.push('📊 性能指标:');

      if (performance_metrics.vector_dimension) {
        detailContent.push(`  向量维度: ${performance_metrics.vector_dimension}`);
      }
      if (performance_metrics.embedding_time) {
        detailContent.push(`  嵌入耗时: ${performance_metrics.embedding_time.toFixed(1)}ms`);
      }
      if (performance_metrics.similarity_score !== undefined) {
        // 显示更精确的相似度分数
        const score = performance_metrics.similarity_score;
        if (Math.abs(score) < 0.0001) {
          detailContent.push(`  相似度分数: ${score.toExponential(2)}`);
        } else {
          detailContent.push(`  相似度分数: ${score.toFixed(4)}`);
        }
      }
      if (performance_metrics.distance_score !== undefined) {
        detailContent.push(`  原始距离: ${performance_metrics.distance_score.toFixed(4)}`);
      }
      if (performance_metrics.search_results_count !== undefined) {
        detailContent.push(`  搜索结果数: ${performance_metrics.search_results_count}`);
      }
    }

    // 使用Modal显示详细信息
    Modal.info({
      title: `${providerDisplayName} 测试详情`,
      content: (
        <div style={{ whiteSpace: 'pre-line', fontFamily: 'monospace', fontSize: '13px' }}>
          {detailContent.join('\n')}
        </div>
      ),
      width: 600,
      okText: '确定'
    });
  };

  // 初始化测试步骤
  const initializeTestSteps = (providerName) => {
    const steps = TEST_STEPS.map(step => ({
      ...step,
      status: 'wait',
      message: '',
      duration: null
    }));

    setTestStepsModal({
      visible: true,
      currentStep: 0,
      stepsData: steps,
      result: null,
      providerName
    });
  };

  // 更新测试步骤状态
  const updateTestStep = (stepIndex, status, message = '', duration = null) => {
    setTestStepsModal(prev => {
      const newSteps = [...prev.stepsData];
      newSteps[stepIndex] = {
        ...newSteps[stepIndex],
        status,
        message,
        duration
      };

      let newCurrentStep = prev.currentStep;
      if (status === 'process') {
        newCurrentStep = stepIndex;
      } else if (status === 'finish' && stepIndex < TEST_STEPS.length - 1) {
        newCurrentStep = stepIndex + 1;
      }

      return {
        ...prev,
        stepsData: newSteps,
        currentStep: newCurrentStep
      };
    });
  };

  // 完成测试
  const finishTest = (success, finalMessage, testInfo) => {
    setTestStepsModal(prev => ({
      ...prev,
      result: {
        success,
        message: finalMessage,
        info: testInfo
      }
    }));
  };

  // 测试向量数据库连接（使用步骤Modal）
  const handleTestVectorDBConnectionWithSteps = async () => {
    try {
      const currentProvider = form.getFieldValue('vectorDBProvider') || 'aliyun';
      const currentConfig = currentVectorDBConfig[currentProvider] || {};

      // 验证配置完整性
      const validation = validateProviderConfig(currentProvider, currentConfig);
      if (!validation.valid) {
        message.warning(`配置不完整：${validation.error}`);
        return;
      }

      const providerDisplayName = getProviderDisplayName(currentProvider);

      // 初始化测试步骤Modal
      initializeTestSteps(providerDisplayName);

      // 开始测试
      setTestConnectionLoading(true);

      console.log(`开始测试${providerDisplayName}连接...`, { provider: currentProvider, config: currentConfig });

      // 模拟步骤进度（因为后端是一次性返回结果）
      updateTestStep(0, 'process', '正在验证配置参数...');

      // 短暂延迟以显示步骤
      await new Promise(resolve => setTimeout(resolve, 500));

      updateTestStep(0, 'finish', '配置验证完成', 0.5);
      updateTestStep(1, 'process', '正在连接数据库...');

      await new Promise(resolve => setTimeout(resolve, 300));

      // 调用统一的向量数据库测试API
      const result = await vectorDatabaseAPI.testConnection(currentProvider, currentConfig);

      // 根据返回结果更新步骤状态
      if (result.info && result.info.test_levels) {
        const levels = result.info.test_levels;

        // 更新配置验证步骤
        const configResult = levels.config_validation;
        if (configResult) {
          updateTestStep(0, configResult.passed ? 'finish' : 'error', configResult.message);
        }

        // 更新连接测试步骤
        const connResult = levels.connection_test;
        if (connResult) {
          updateTestStep(1, connResult.passed ? 'finish' : 'error', connResult.message);
          if (connResult.passed) {
            updateTestStep(2, 'process', '正在测试向量操作...');
            await new Promise(resolve => setTimeout(resolve, 300));
          }
        }

        // 更新向量操作步骤
        const vectorResult = levels.vector_operations;
        if (vectorResult) {
          updateTestStep(2, vectorResult.passed ? 'finish' : 'error', vectorResult.message);
        }
      }

      // 完成测试
      finishTest(result.success, result.message, result.info);

      console.log(`${providerDisplayName}测试结果:`, result);

    } catch (error) {
      const currentProvider = form.getFieldValue('vectorDBProvider') || 'aliyun';
      const providerDisplayName = getProviderDisplayName(currentProvider);
      console.error(`测试${providerDisplayName}连接失败:`, error);

      // 更新当前步骤为错误状态
      updateTestStep(testStepsModal.currentStep, 'error', `测试失败: ${error.message || '网络错误'}`);
      finishTest(false, '连接测试失败，请检查网络和配置', {});

    } finally {
      setTestConnectionLoading(false);
    }
  };

  // 旧的测试函数已被handleTestVectorDBConnectionWithSteps替代

  // 关闭提示词模板管理modal
  const handleClosePromptTemplateModal = () => {
    setPromptTemplateModalVisible(false);
  };

  // 保存提示词模板
  const handleSavePromptTemplates = async () => {
    try {
      const values = await promptTemplateForm.validateFields();
      setPromptTemplateLoading(true);

      await settingsAPI.updatePromptTemplates(values);
      message.success('提示词模板保存成功');
      setPromptTemplateModalVisible(false);
    } catch (error) {
      console.error('保存提示词模板失败:', error);
      message.error('保存提示词模板失败: ' + (error.message || '未知错误'));
    } finally {
      setPromptTemplateLoading(false);
    }
  };

  // 重置提示词模板为默认值
  const handleResetPromptTemplates = async () => {
    try {
      setPromptTemplateLoading(true);
      // 调用重置API
      const result = await settingsAPI.resetPromptTemplates();
      if (result.success) {
        // 使用返回的默认模板更新表单
        promptTemplateForm.setFieldsValue(result.templates);
        message.success('已重置为默认模板');
      } else {
        message.error('重置失败: ' + (result.message || '未知错误'));
      }
    } catch (error) {
      console.error('重置提示词模板失败:', error);
      message.error('重置失败: ' + (error.message || '未知错误'));
    } finally {
      setPromptTemplateLoading(false);
    }
  };

  // 设置项配置
  const settingGroups = [
    {
      title: '基础配置',
      icon: <SettingOutlined />,
      color: '#1890ff',
      items: [
        {
          name: 'timezone',
          label: '平台时区',
          icon: <GlobalOutlined />,
          type: 'select',
          options: TIMEZONE_OPTIONS,
          required: true,
          tooltip: '设置平台使用的时区，影响所有时间相关的显示和计算'
        },
        {
          name: 'apiUrl',
          label: 'API地址',
          icon: <ApiOutlined />,
          type: 'input',
          placeholder: '例如: http://localhost:8080/api',
          required: true,
          tooltip: '系统后端API的基础URL，通常不需要修改'
        }
      ]
    },
    {
      title: '对话设置',
      icon: <MessageOutlined />,
      color: '#52c41a',
      items: [
        {
          name: 'maxConversationHistoryLength',
          label: '上下文历史消息长度',
          icon: <ClockCircleOutlined />,
          type: 'number',
          min: 0,
          max: 500,
          required: true,
          tooltip: '限制构建上下文时包含的历史消息数量，影响模型推理和性能。设置为0时将包含当前会话的所有历史消息'
        },
        {
          name: 'streamingEnabled',
          label: '启用流式输出',
          icon: <ThunderboltOutlined />,
          type: 'switch',
          tooltip: '启用后，智能体回复将实时流式显示，提供更好的用户体验'
        },
        {
          name: 'includeThinkingContentInContext',
          label: '在上下文中包含思考内容',
          icon: <BulbOutlined />,
          type: 'switch',
          tooltip: '启用后，智能体的思考内容将包含在对话上下文中，可能提高回复质量但增加Token消耗'
        },
        {
          name: 'splitToolCallsInHistory',
          label: '拆分工具调用为独立消息',
          icon: <ToolOutlined />,
          type: 'switch',
          tooltip: '启用后，工具调用将被拆分为独立的历史消息（assistant + tool），符合OpenAI API格式，有助于模型理解工具使用上下文。建议保持开启'
        }
      ]
    },
    {
      title: '系统设置',
      icon: <BugOutlined />,
      color: '#fa8c16',
      items: [
        {
          name: 'enableDebugMode',
          label: '启用调试模式',
          icon: <BugOutlined />,
          type: 'switch',
          tooltip: '启用后，系统将显示更多调试信息，有助于开发和故障排除'
        },
        {
          name: 'storeLlmErrorMessages',
          label: '存储LLM错误消息',
          icon: <SaveFilled />,
          type: 'switch',
          tooltip: '启用后，系统将保存大语言模型返回的错误消息，有助于调试但可能占用更多存储空间'
        }
      ]
    },
    {
      title: '向量数据库',
      icon: <DatabaseOutlined />,
      color: '#722ed1',
      items: [
        {
          name: 'useBuiltinVectorDB',
          label: '使用内置向量数据库',
          icon: <DatabaseOutlined />,
          type: 'switch',
          tooltip: '启用后，系统将使用内置的Milvus向量数据库，否则使用外部向量数据库服务'
        },
        {
          name: 'vectorDBProvider',
          label: '向量数据库提供商',
          icon: <ApiOutlined />,
          type: 'vector-db-select',
          tooltip: '选择外部向量数据库提供商，如阿里云、TiDB等',
          dependsOn: 'useBuiltinVectorDB',
          dependsValue: false
        }
      ]
    },
    {
      title: '辅助生成',
      icon: <RobotOutlined />,
      color: '#eb2f96',
      items: [
        {
          name: 'enableAssistantGeneration',
          label: '启用辅助生成',
          icon: <RobotOutlined />,
          type: 'switch',
          tooltip: '启用后，系统可以使用指定的模型自动生成角色提示词、空间描述、空间规则等内容'
        },
        {
          name: 'assistantGenerationModel',
          label: '辅助生成模型',
          icon: <EyeOutlined />,
          type: 'model-select',
          tooltip: '选择用于辅助生成内容的模型，默认使用系统默认模型'
        }
      ]
    },
    {
      title: '超时配置',
      icon: <FieldTimeOutlined />,
      color: '#f5222d',
      items: [
        {
          name: 'httpConnectionTimeout',
          label: 'HTTP连接超时',
          icon: <ClockCircleOutlined />,
          type: 'number',
          min: 5,
          max: 120,
          required: true,
          tooltip: 'HTTP请求建立连接的超时时间（秒），范围5-120秒，默认30秒'
        },
        {
          name: 'httpReadTimeout',
          label: 'HTTP读取超时',
          icon: <ClockCircleOutlined />,
          type: 'number',
          min: 30,
          max: 600,
          required: true,
          tooltip: 'HTTP请求读取数据的超时时间（秒），范围30-600秒，默认300秒。用于流式响应的长时间读取'
        },
        {
          name: 'streamSocketTimeout',
          label: '流式Socket超时',
          icon: <ThunderboltOutlined />,
          type: 'number',
          min: 10,
          max: 300,
          required: true,
          tooltip: '流式响应Socket读取的超时时间（秒），范围10-300秒，默认60秒。影响流式输出的响应速度'
        },
        {
          name: 'defaultModelTimeout',
          label: '模型请求默认超时',
          icon: <FieldTimeOutlined />,
          type: 'number',
          min: 10,
          max: 300,
          required: true,
          tooltip: '模型配置的默认请求超时时间（秒），范围10-300秒，默认60秒。新建模型配置时的默认值'
        }
      ]
    }
  ];

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>系统基本配置</Title>
            <Text type="secondary">
              配置系统的基本参数，包括API地址、对话设置等
            </Text>
          </div>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={loading}
              size="large"
              style={{
                borderRadius: '8px',
                height: '42px',
                fontSize: '14px'
              }}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
              size="large"
              style={{
                borderRadius: '8px',
                height: '42px',
                fontSize: '14px'
              }}
            >
              重置设置
            </Button>
          </Space>
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        disabled={loading}
        style={{ maxWidth: '100%' }}
      >
        <Row gutter={[24, 24]}>
          {settingGroups.map((group, groupIndex) => (
            <Col xs={24} sm={24} md={12} lg={8} xl={8} key={groupIndex}>
              <Card
                style={{
                  borderRadius: '12px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                  border: `1px solid ${group.color}20`,
                  height: '100%'
                }}
                styles={{ body: { padding: '20px' } }}
              >
                <div style={{ marginBottom: '16px' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: '8px'
                  }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '8px',
                      backgroundColor: `${group.color}15`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: '12px',
                      color: group.color,
                      fontSize: '16px'
                    }}>
                      {group.icon}
                    </div>
                    <Title level={5} style={{ margin: 0, color: group.color }}>
                      {group.title}
                    </Title>
                  </div>
                </div>

                <Space direction="vertical" style={{ width: '100%' }} size="middle">
                  {group.items.map((item, itemIndex) => (
                    <div key={itemIndex}>
                      <Form.Item
                        name={item.name}
                        label={
                          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                            <span style={{
                              color: group.color,
                              marginRight: '8px',
                              fontSize: '14px'
                            }}>
                              {item.icon}
                            </span>
                            <span style={{ fontSize: '14px', fontWeight: '500' }}>
                              {item.label}
                            </span>
                            <Tooltip title={item.tooltip}>
                              <InfoCircleOutlined
                                style={{
                                  marginLeft: '6px',
                                  color: '#999',
                                  fontSize: '12px'
                                }}
                              />
                            </Tooltip>
                          </div>
                        }
                        rules={item.required ? [{ required: true, message: `请${item.type === 'select' ? '选择' : '输入'}${item.label}` }] : []}
                        valuePropName={item.type === 'switch' ? 'checked' : 'value'}
                        style={{ marginBottom: '12px' }}
                      >
                        {item.type === 'input' && (
                          <Input
                            placeholder={item.placeholder}
                            size="small"
                            style={{ borderRadius: '6px' }}
                          />
                        )}
                        {item.type === 'select' && (
                          <Select
                            showSearch
                            placeholder={`选择${item.label}`}
                            optionFilterProp="label"
                            filterOption={(input, option) =>
                              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            }
                            options={item.options}
                            size="small"
                            style={{ borderRadius: '6px' }}
                          />
                        )}
                        {item.type === 'number' && (
                          <InputNumber
                            min={item.min}
                            max={item.max}
                            style={{ width: '100%', borderRadius: '6px' }}
                            size="small"
                          />
                        )}
                        {item.type === 'switch' && (
                          <Switch
                            size="small"
                            onChange={item.name === 'useBuiltinVectorDB' ? setUseBuiltinVectorDB : undefined}
                          />
                        )}
                        {item.type === 'vector-db-select' && (
                          <Select
                            placeholder="选择向量数据库提供商"
                            showSearch
                            optionFilterProp="label"
                            filterOption={(input, option) =>
                              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            }
                            size="small"
                            style={{ borderRadius: '6px' }}
                            disabled={useBuiltinVectorDB}
                          >
                            <Select.Option value="aliyun" label="阿里云 DashVector">
                              阿里云 DashVector
                            </Select.Option>
                            <Select.Option value="tidb" label="TiDB Cloud">
                              TiDB Cloud
                            </Select.Option>
                            <Select.Option value="aws-opensearch" label="AWS OpenSearch">
                              AWS OpenSearch
                            </Select.Option>
                            <Select.Option value="aws-bedrock" label="AWS Bedrock Knowledge Base">
                              AWS Bedrock Knowledge Base
                            </Select.Option>
                            <Select.Option value="azure-cognitive-search" label="Azure Cognitive Search">
                              Azure Cognitive Search
                            </Select.Option>
                            <Select.Option value="azure-cosmos-db" label="Azure Cosmos DB">
                              Azure Cosmos DB
                            </Select.Option>
                            <Select.Option value="gcp-vertex-ai" label="Google Cloud Vertex AI Vector Search">
                              Google Cloud Vertex AI Vector Search
                            </Select.Option>
                            <Select.Option value="gcp-firestore" label="Google Cloud Firestore">
                              Google Cloud Firestore
                            </Select.Option>
                            <Select.Option value="pinecone" label="Pinecone">
                              Pinecone
                            </Select.Option>
                            <Select.Option value="weaviate" label="Weaviate">
                              Weaviate
                            </Select.Option>
                            <Select.Option value="qdrant" label="Qdrant">
                              Qdrant
                            </Select.Option>
                            <Select.Option value="chroma" label="Chroma">
                              Chroma
                            </Select.Option>
                            <Select.Option value="milvus" label="Milvus">
                              Milvus
                            </Select.Option>
                            <Select.Option value="elasticsearch" label="Elasticsearch">
                              Elasticsearch
                            </Select.Option>
                            <Select.Option value="custom" label="自定义">
                              自定义
                            </Select.Option>
                          </Select>
                        )}
                        {item.type === 'model-select' && (
                          <Select
                            placeholder="选择辅助生成模型"
                            showSearch
                            optionFilterProp="label"
                            filterOption={(input, option) =>
                              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            }
                            size="small"
                            style={{ borderRadius: '6px' }}
                          >
                            <Select.Option value="default" label={`默认文本生成模型${defaultModels.text_model ? ` (${defaultModels.text_model.name})` : ''}`}>
                              默认文本生成模型{defaultModels.text_model ? ` (${defaultModels.text_model.name})` : ''}
                            </Select.Option>
                            {modelConfigs && modelConfigs.length > 0 ? (
                              modelConfigs.map(config => (
                                <Select.Option
                                  key={config.id}
                                  value={config.id.toString()}
                                  label={config.name}
                                >
                                  {config.name} ({config.provider})
                                </Select.Option>
                              ))
                            ) : (
                              <>
                                <Select.Option value="loading" label="加载中..." disabled>
                                  加载中...
                                </Select.Option>
                                <Select.Option value="mock1" label="模拟模型1">
                                  模拟模型1 (OpenAI)
                                </Select.Option>
                                <Select.Option value="mock2" label="模拟模型2">
                                  模拟模型2 (Anthropic)
                                </Select.Option>
                              </>
                            )}
                          </Select>
                        )}
                      </Form.Item>
                    </div>
                  ))}
                </Space>

                {/* 在辅助生成卡片底部添加管理按钮 */}
                {group.title === '辅助生成' && (
                  <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: '1px solid #f0f0f0' }}>
                    <Button
                      type="default"
                      icon={<EditOutlined />}
                      onClick={handleOpenPromptTemplateModal}
                      size="small"
                      style={{
                        width: '100%',
                        borderRadius: '6px',
                        height: '32px'
                      }}
                    >
                      修改辅助生成提示词
                    </Button>
                  </div>
                )}

                {/* 在向量数据库卡片底部添加配置按钮 */}
                {group.title === '向量数据库' && (
                  <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: '1px solid #f0f0f0' }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button
                        type="default"
                        icon={<SettingOutlined />}
                        onClick={handleOpenVectorDBConfigModal}
                        size="small"
                        style={{
                          width: '100%',
                          borderRadius: '6px',
                          height: '32px'
                        }}
                        disabled={useBuiltinVectorDB}
                      >
                        配置向量数据库连接
                      </Button>

                      {/* 测试连接按钮 - 使用统一测试接口 */}
                      {!useBuiltinVectorDB && (
                        <Button
                          type="primary"
                          icon={<ApiOutlined />}
                          onClick={handleTestVectorDBConnectionWithSteps}
                          loading={testConnectionLoading}
                          size="small"
                          style={{
                            width: '100%',
                            borderRadius: '6px',
                            height: '32px'
                          }}
                        >
                          {testConnectionLoading ? '测试中...' : '测试连接'}
                        </Button>
                      )}
                    </Space>
                  </div>
                )}
              </Card>
            </Col>
          ))}
        </Row>
      </Form>

      {/* 提示词模板管理Modal */}
      <Modal
        title="辅助生成提示词模板管理"
        open={promptTemplateModalVisible}
        onCancel={handleClosePromptTemplateModal}
        width={800}
        footer={[
          <Button key="reset" onClick={handleResetPromptTemplates}>
            重置为默认
          </Button>,
          <Button key="cancel" onClick={handleClosePromptTemplateModal}>
            取消
          </Button>,
          <Button
            key="save"
            type="primary"
            loading={promptTemplateLoading}
            onClick={handleSavePromptTemplates}
          >
            保存
          </Button>
        ]}
      >
        <Form
          form={promptTemplateForm}
          layout="vertical"
        >
          <Tabs
            defaultActiveKey="roleSystemPrompt"
            items={[
              {
                key: 'roleSystemPrompt',
                label: '角色系统提示词',
                children: (
                  <Form.Item
                    name="roleSystemPrompt"
                    label="角色系统提示词生成模板"
                    extra="可用变量：{{name}} - 角色名称，{{description}} - 角色描述"
                  >
                    <Input.TextArea
                      rows={12}
                      placeholder="请输入角色系统提示词生成模板..."
                    />
                  </Form.Item>
                )
              },
              {
                key: 'actionSpaceBackground',
                label: '行动空间背景',
                children: (
                  <Form.Item
                    name="actionSpaceBackground"
                    label="行动空间背景设定生成模板"
                    extra="可用变量：{{name}} - 空间名称，{{description}} - 空间描述"
                  >
                    <Input.TextArea
                      rows={12}
                      placeholder="请输入行动空间背景设定生成模板..."
                    />
                  </Form.Item>
                )
              },
              {
                key: 'actionSpaceRules',
                label: '行动空间规则',
                children: (
                  <Form.Item
                    name="actionSpaceRules"
                    label="行动空间基本规则生成模板"
                    extra="可用变量：{{name}} - 空间名称，{{description}} - 空间描述"
                  >
                    <Input.TextArea
                      rows={12}
                      placeholder="请输入行动空间基本规则生成模板..."
                    />
                  </Form.Item>
                )
              },
              {
                key: 'actionTaskDescription',
                label: '行动任务描述',
                children: (
                  <Form.Item
                    name="actionTaskDescription"
                    label="行动任务描述生成模板"
                    extra="可用变量：{{title}} - 任务名称，{{action_space_name}} - 空间名称，{{action_space_description}} - 空间描述，{{roles}} - 参与角色"
                  >
                    <Input.TextArea
                      rows={12}
                      placeholder="请输入行动任务描述生成模板..."
                    />
                  </Form.Item>
                )
              },
              {
                key: 'userMessageExpand',
                label: '用户消息辅助',
                children: (
                  <Form.Item
                    name="userMessageExpand"
                    label="用户消息辅助生成模板"
                    extra="可用变量：{{original_message}} - 原始消息，{{action_space_name}} - 空间名称，{{action_space_description}} - 空间描述，{{participant_roles}} - 参与角色，{{assist_mode}} - 辅助模式"
                  >
                    <Input.TextArea
                      rows={12}
                      placeholder="请输入用户消息辅助生成模板..."
                    />
                  </Form.Item>
                )
              }
            ]}
          />
        </Form>
      </Modal>

      {/* 向量数据库配置Modal */}
      <Modal
        title="配置向量数据库连接"
        open={vectorDBConfigVisible}
        onOk={handleVectorDBConfigSave}
        onCancel={() => setVectorDBConfigVisible(false)}
        confirmLoading={vectorDBConfigLoading}
        width={600}

      >
        <Form
          form={vectorDBConfigForm}
          layout="vertical"
          style={{ marginTop: '16px' }}
        >
          <Alert
            message="配置外部向量数据库连接"
            description="根据选择的提供商，配置相应的连接参数。配置完成后，系统将使用外部向量数据库进行向量存储和检索。"
            type="info"
            showIcon
            style={{ marginBottom: '24px' }}
          />

          <Form.Item
            name="provider"
            label="向量数据库提供商"
            rules={[{ required: true, message: '请选择向量数据库提供商' }]}
          >
            <Select
              placeholder="选择向量数据库提供商"
              onChange={(value) => {
                // 切换提供商时清空其他字段
                vectorDBConfigForm.setFieldsValue({
                  provider: value,
                  // 通用字段
                  apiKey: '',
                  endpoint: '',
                  region: '',
                  // TiDB字段
                  connectionString: '',
                  // AWS字段
                  accessKeyId: '',
                  secretAccessKey: '',
                  knowledgeBaseId: '',
                  // Azure字段
                  key: '',
                  indexName: '',
                  databaseName: '',
                  containerName: '',
                  // GCP字段
                  projectId: '',
                  location: '',
                  indexEndpoint: '',
                  serviceAccountKey: '',
                  databaseId: '',
                  collectionName: '',
                  // 其他字段
                  environment: '',
                  username: '',
                  password: ''
                });
              }}
            >
              <Select.Option value="aliyun">阿里云 DashVector</Select.Option>
              <Select.Option value="tidb">TiDB Cloud</Select.Option>
              <Select.Option value="aws-opensearch">AWS OpenSearch</Select.Option>
              <Select.Option value="aws-bedrock">AWS Bedrock Knowledge Base</Select.Option>
              <Select.Option value="azure-cognitive-search">Azure Cognitive Search</Select.Option>
              <Select.Option value="azure-cosmos-db">Azure Cosmos DB</Select.Option>
              <Select.Option value="gcp-vertex-ai">Google Cloud Vertex AI Vector Search</Select.Option>
              <Select.Option value="gcp-firestore">Google Cloud Firestore</Select.Option>
              <Select.Option value="pinecone">Pinecone</Select.Option>
              <Select.Option value="weaviate">Weaviate</Select.Option>
              <Select.Option value="qdrant">Qdrant</Select.Option>
              <Select.Option value="chroma">Chroma</Select.Option>
              <Select.Option value="milvus">Milvus</Select.Option>
              <Select.Option value="elasticsearch">Elasticsearch</Select.Option>
              <Select.Option value="custom">自定义</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item dependencies={['provider']}>
            {({ getFieldValue }) => {
              const provider = getFieldValue('provider');

              if (provider === 'aliyun') {
                return (
                  <>
                    <Form.Item
                      name="apiKey"
                      label="API Key"
                      rules={[{ required: true, message: '请输入API Key' }]}
                    >
                      <Input.Password placeholder="请输入阿里云DashVector的API Key" />
                    </Form.Item>
                    <Form.Item
                      name="endpoint"
                      label="Cluster Endpoint"
                      rules={[{ required: true, message: '请输入Cluster Endpoint' }]}
                    >
                      <Input placeholder="例如: https://your-cluster.dashvector.cn-hangzhou.aliyuncs.com" />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'tidb') {
                return (
                  <>
                    <Form.Item
                      name="connectionString"
                      label="Connection String"
                      rules={[{ required: true, message: '请输入Connection String' }]}
                    >
                      <Input.TextArea
                        placeholder="例如: mysql://3WYw82L9THMvuY5.root:<EMAIL>:4000/test"
                        rows={2}
                      />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'aws-opensearch') {
                return (
                  <>
                    <Form.Item
                      name="accessKeyId"
                      label="Access Key ID"
                      rules={[{ required: true, message: '请输入Access Key ID' }]}
                    >
                      <Input placeholder="请输入AWS Access Key ID" />
                    </Form.Item>
                    <Form.Item
                      name="secretAccessKey"
                      label="Secret Access Key"
                      rules={[{ required: true, message: '请输入Secret Access Key' }]}
                    >
                      <Input.Password placeholder="请输入AWS Secret Access Key" />
                    </Form.Item>
                    <Form.Item
                      name="region"
                      label="AWS Region"
                      rules={[{ required: true, message: '请输入AWS Region' }]}
                    >
                      <Input placeholder="例如: us-east-1" />
                    </Form.Item>
                    <Form.Item
                      name="endpoint"
                      label="OpenSearch Endpoint"
                      rules={[{ required: true, message: '请输入OpenSearch Endpoint' }]}
                    >
                      <Input placeholder="例如: https://search-domain.us-east-1.es.amazonaws.com" />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'aws-bedrock') {
                return (
                  <>
                    <Form.Item
                      name="accessKeyId"
                      label="Access Key ID"
                      rules={[{ required: true, message: '请输入Access Key ID' }]}
                    >
                      <Input placeholder="请输入AWS Access Key ID" />
                    </Form.Item>
                    <Form.Item
                      name="secretAccessKey"
                      label="Secret Access Key"
                      rules={[{ required: true, message: '请输入Secret Access Key' }]}
                    >
                      <Input.Password placeholder="请输入AWS Secret Access Key" />
                    </Form.Item>
                    <Form.Item
                      name="region"
                      label="AWS Region"
                      rules={[{ required: true, message: '请输入AWS Region' }]}
                    >
                      <Input placeholder="例如: us-east-1" />
                    </Form.Item>
                    <Form.Item
                      name="knowledgeBaseId"
                      label="Knowledge Base ID"
                      rules={[{ required: true, message: '请输入Knowledge Base ID' }]}
                    >
                      <Input placeholder="请输入Bedrock Knowledge Base ID" />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'azure-cognitive-search') {
                return (
                  <>
                    <Form.Item
                      name="endpoint"
                      label="Search Service Endpoint"
                      rules={[{ required: true, message: '请输入Search Service Endpoint' }]}
                    >
                      <Input placeholder="例如: https://your-service.search.windows.net" />
                    </Form.Item>
                    <Form.Item
                      name="apiKey"
                      label="Admin API Key"
                      rules={[{ required: true, message: '请输入Admin API Key' }]}
                    >
                      <Input.Password placeholder="请输入Azure Cognitive Search的Admin API Key" />
                    </Form.Item>
                    <Form.Item
                      name="indexName"
                      label="Index Name"
                      rules={[{ required: true, message: '请输入Index Name' }]}
                    >
                      <Input placeholder="请输入搜索索引名称" />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'azure-cosmos-db') {
                return (
                  <>
                    <Form.Item
                      name="endpoint"
                      label="Cosmos DB Endpoint"
                      rules={[{ required: true, message: '请输入Cosmos DB Endpoint' }]}
                    >
                      <Input placeholder="例如: https://your-account.documents.azure.com:443/" />
                    </Form.Item>
                    <Form.Item
                      name="key"
                      label="Primary Key"
                      rules={[{ required: true, message: '请输入Primary Key' }]}
                    >
                      <Input.Password placeholder="请输入Azure Cosmos DB的Primary Key" />
                    </Form.Item>
                    <Form.Item
                      name="databaseName"
                      label="Database Name"
                      rules={[{ required: true, message: '请输入Database Name' }]}
                    >
                      <Input placeholder="请输入数据库名称" />
                    </Form.Item>
                    <Form.Item
                      name="containerName"
                      label="Container Name"
                      rules={[{ required: true, message: '请输入Container Name' }]}
                    >
                      <Input placeholder="请输入容器名称" />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'gcp-vertex-ai') {
                return (
                  <>
                    <Form.Item
                      name="projectId"
                      label="Project ID"
                      rules={[{ required: true, message: '请输入Project ID' }]}
                    >
                      <Input placeholder="请输入Google Cloud Project ID" />
                    </Form.Item>
                    <Form.Item
                      name="location"
                      label="Location"
                      rules={[{ required: true, message: '请输入Location' }]}
                    >
                      <Input placeholder="例如: us-central1" />
                    </Form.Item>
                    <Form.Item
                      name="indexEndpoint"
                      label="Index Endpoint"
                      rules={[{ required: true, message: '请输入Index Endpoint' }]}
                    >
                      <Input placeholder="请输入Vertex AI Vector Search Index Endpoint" />
                    </Form.Item>
                    <Form.Item
                      name="serviceAccountKey"
                      label="Service Account Key (JSON)"
                      rules={[{ required: true, message: '请输入Service Account Key' }]}
                    >
                      <Input.TextArea
                        placeholder="请粘贴Service Account Key的JSON内容"
                        rows={4}
                      />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'gcp-firestore') {
                return (
                  <>
                    <Form.Item
                      name="projectId"
                      label="Project ID"
                      rules={[{ required: true, message: '请输入Project ID' }]}
                    >
                      <Input placeholder="请输入Google Cloud Project ID" />
                    </Form.Item>
                    <Form.Item
                      name="databaseId"
                      label="Database ID"
                    >
                      <Input placeholder="请输入Firestore Database ID（默认为(default)）" />
                    </Form.Item>
                    <Form.Item
                      name="collectionName"
                      label="Collection Name"
                      rules={[{ required: true, message: '请输入Collection Name' }]}
                    >
                      <Input placeholder="请输入Firestore集合名称" />
                    </Form.Item>
                    <Form.Item
                      name="serviceAccountKey"
                      label="Service Account Key (JSON)"
                      rules={[{ required: true, message: '请输入Service Account Key' }]}
                    >
                      <Input.TextArea
                        placeholder="请粘贴Service Account Key的JSON内容"
                        rows={4}
                      />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'pinecone') {
                return (
                  <>
                    <Form.Item
                      name="apiKey"
                      label="API Key"
                      rules={[{ required: true, message: '请输入API Key' }]}
                    >
                      <Input.Password placeholder="请输入Pinecone的API Key" />
                    </Form.Item>
                    <Form.Item
                      name="environment"
                      label="Environment"
                      rules={[{ required: true, message: '请输入Environment' }]}
                    >
                      <Input placeholder="例如: us-west1-gcp" />
                    </Form.Item>
                    <Form.Item
                      name="indexName"
                      label="Index Name"
                      rules={[{ required: true, message: '请输入Index Name' }]}
                    >
                      <Input placeholder="请输入Pinecone索引名称" />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'milvus') {
                return (
                  <>
                    <Form.Item
                      name="endpoint"
                      label="Milvus Endpoint"
                      rules={[{ required: true, message: '请输入Milvus Endpoint' }]}
                    >
                      <Input placeholder="例如: localhost:19530" />
                    </Form.Item>
                    <Form.Item
                      name="username"
                      label="Username"
                    >
                      <Input placeholder="请输入用户名（如果需要认证）" />
                    </Form.Item>
                    <Form.Item
                      name="password"
                      label="Password"
                    >
                      <Input.Password placeholder="请输入密码（如果需要认证）" />
                    </Form.Item>
                    <Form.Item
                      name="collectionName"
                      label="Collection Name"
                      rules={[{ required: true, message: '请输入Collection Name' }]}
                    >
                      <Input placeholder="请输入Milvus集合名称" />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'elasticsearch') {
                return (
                  <>
                    <Form.Item
                      name="endpoint"
                      label="Elasticsearch Endpoint"
                      rules={[{ required: true, message: '请输入Elasticsearch Endpoint' }]}
                    >
                      <Input placeholder="例如: https://localhost:9200" />
                    </Form.Item>
                    <Form.Item
                      name="username"
                      label="Username"
                    >
                      <Input placeholder="请输入用户名（如果需要认证）" />
                    </Form.Item>
                    <Form.Item
                      name="password"
                      label="Password"
                    >
                      <Input.Password placeholder="请输入密码（如果需要认证）" />
                    </Form.Item>
                    <Form.Item
                      name="indexName"
                      label="Index Name"
                      rules={[{ required: true, message: '请输入Index Name' }]}
                    >
                      <Input placeholder="请输入Elasticsearch索引名称" />
                    </Form.Item>
                  </>
                );
              }

              if (provider === 'custom') {
                return (
                  <>
                    <Form.Item
                      name="endpoint"
                      label="服务端点"
                      rules={[{ required: true, message: '请输入服务端点' }]}
                    >
                      <Input placeholder="请输入自定义向量数据库的服务端点" />
                    </Form.Item>
                    <Form.Item
                      name="apiKey"
                      label="认证密钥"
                    >
                      <Input.Password placeholder="请输入认证密钥（如果需要）" />
                    </Form.Item>
                    <Form.Item
                      name="username"
                      label="用户名"
                    >
                      <Input placeholder="请输入用户名（如果需要）" />
                    </Form.Item>
                    <Form.Item
                      name="password"
                      label="密码"
                    >
                      <Input.Password placeholder="请输入密码（如果需要）" />
                    </Form.Item>
                  </>
                );
              }

              // 其他提供商的通用配置
              return (
                <>
                  <Form.Item
                    name="endpoint"
                    label="服务端点"
                    rules={[{ required: true, message: '请输入服务端点' }]}
                  >
                    <Input placeholder="请输入向量数据库的服务端点" />
                  </Form.Item>
                  <Form.Item
                    name="apiKey"
                    label="API Key"
                  >
                    <Input.Password placeholder="请输入API Key（如果需要）" />
                  </Form.Item>
                </>
              );
            }}
          </Form.Item>

          <Alert
            message="配置说明"
            description={
              <div style={{ fontSize: '12px', lineHeight: '1.4' }}>
                <p><strong>阿里云 DashVector:</strong> 需要API Key和Cluster Endpoint，可在阿里云控制台获取</p>
                <p><strong>TiDB Cloud:</strong> 只需要Connection String，可在TiDB Cloud控制台的Connect页面直接复制</p>
                <p><strong>AWS OpenSearch:</strong> 需要Access Key、Secret Key、Region和OpenSearch域名端点</p>
                <p><strong>AWS Bedrock:</strong> 需要Access Key、Secret Key、Region和Knowledge Base ID</p>
                <p><strong>Azure Cognitive Search:</strong> 需要Search Service端点、Admin API Key和索引名称</p>
                <p><strong>Azure Cosmos DB:</strong> 需要Cosmos DB端点、Primary Key、数据库名和容器名</p>
                <p><strong>Google Cloud Vertex AI:</strong> 需要Project ID、Location、Index Endpoint和Service Account Key</p>
                <p><strong>Google Cloud Firestore:</strong> 需要Project ID、Collection Name和Service Account Key</p>
                <p><strong>Pinecone:</strong> 需要API Key、Environment和Index Name</p>
                <p><strong>其他提供商:</strong> 请根据相应文档配置连接参数</p>
              </div>
            }
            type="warning"
            showIcon
            style={{ marginTop: '16px' }}
          />
        </Form>
      </Modal>

      {/* 测试步骤Modal */}
      <Modal
        title={`${testStepsModal.providerName} 连接测试`}
        open={testStepsModal.visible}
        onCancel={() => setTestStepsModal(prev => ({ ...prev, visible: false }))}
        footer={[
          <Button key="close" onClick={() => setTestStepsModal(prev => ({ ...prev, visible: false }))}>
            关闭
          </Button>,
          testStepsModal.result && testStepsModal.result.info && testStepsModal.result.info.test_levels && (
            <Button
              key="detail"
              type="primary"
              onClick={() => {
                setTestStepsModal(prev => ({ ...prev, visible: false }));
                showDetailedTestResult(testStepsModal.providerName, testStepsModal.result);
              }}
            >
              查看详情
            </Button>
          )
        ]}
        width={600}
      >
        <div style={{ padding: '20px 0' }}>
          <Steps
            direction="vertical"
            current={testStepsModal.currentStep}
            items={testStepsModal.stepsData.map((step) => ({
              title: step.title,
              description: (
                <div>
                  <div style={{ color: '#666', marginBottom: '4px' }}>
                    {step.description}
                  </div>
                  {step.message && (
                    <div style={{
                      fontSize: '12px',
                      color: step.status === 'error' ? '#ff4d4f' :
                             step.status === 'finish' ? '#52c41a' : '#1890ff',
                      marginTop: '4px'
                    }}>
                      {step.message}
                      {step.duration && ` (${step.duration}s)`}
                    </div>
                  )}
                </div>
              ),
              status: step.status,
              icon: step.status === 'process' ? <LoadingOutlined /> :
                    step.status === 'finish' ? <CheckCircleOutlined /> :
                    step.status === 'error' ? <CloseCircleOutlined /> :
                    step.icon
            }))}
          />

          {testStepsModal.result && (
            <div style={{
              marginTop: '24px',
              padding: '16px',
              backgroundColor: testStepsModal.result.success ? '#f6ffed' : '#fff2f0',
              border: `1px solid ${testStepsModal.result.success ? '#b7eb8f' : '#ffccc7'}`,
              borderRadius: '6px'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '8px',
                color: testStepsModal.result.success ? '#52c41a' : '#ff4d4f'
              }}>
                {testStepsModal.result.success ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
                <span style={{ marginLeft: '8px', fontWeight: 'bold' }}>
                  {testStepsModal.result.success ? '测试成功' : '测试失败'}
                </span>
              </div>
              <div style={{ fontSize: '14px', color: '#666' }}>
                {testStepsModal.result.message}
              </div>

              {testStepsModal.result.info && testStepsModal.result.info.performance_metrics && (
                <div style={{ marginTop: '12px' }}>
                  <div style={{ fontSize: '12px', color: '#999', marginBottom: '4px' }}>
                    性能指标:
                  </div>
                  {Object.entries(testStepsModal.result.info.performance_metrics).map(([key, value]) => (
                    <div key={key} style={{ fontSize: '12px', color: '#666' }}>
                      {key === 'embedding_time' && `嵌入耗时: ${value.toFixed(1)}ms`}
                      {key === 'vector_dimension' && `向量维度: ${value}`}
                      {key === 'similarity_score' && `相似度分数: ${Math.abs(value) < 0.0001 ? value.toExponential(2) : value.toFixed(4)}`}
                      {key === 'distance_score' && `原始距离: ${value.toFixed(4)}`}
                      {key === 'search_results_count' && `搜索结果数: ${value}`}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default GeneralSettingsPage;
