import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Typography, Card, Button, Table, Tabs, Form, Input, Modal, Select, Space, Empty, Spin, message, Radio, Collapse, Tag, Row, Col, Alert, Divider, Tooltip, Checkbox } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, CodeOutlined, PartitionOutlined, BugOutlined, ExperimentOutlined, SyncOutlined, QuestionCircleOutlined, DownOutlined, UpOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { actionSpaceAPI } from '../../services/api/actionspace';
import { api as apiInstance } from '../../services/api/index';
import { useNavigate } from 'react-router-dom';
import Editor from '@monaco-editor/react';
import { extractTemplateVariables, getTemplateVariableInfo, formatEnvironmentVariables } from '../../utils/templateUtils';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;

// 缓存数据，避免重复请求
let ruleSetsCache = [];
let allRulesCache = [];
let lastFetchRuleSetsTime = 0;
let lastFetchRulesTime = 0;
const CACHE_TIMEOUT = 300000; // 缓存5分钟

const ActionRules = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('ruleSets');
  const [ruleSets, setRuleSets] = useState([]);
  const [allRules, setAllRules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [rulesLoading, setRulesLoading] = useState(false);
  const [rulesLoaded, setRulesLoaded] = useState(false); // 标记规则是否已加载
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [ruleModalVisible, setRuleModalVisible] = useState(false);
  const [ruleEditType, setRuleEditType] = useState('llm'); // 'llm' or 'logic'

  const [form] = Form.useForm();
  const [ruleForm] = Form.useForm();

  // 规则编辑相关状态
  const [editingRule, setEditingRule] = useState(null); // 正在编辑的规则对象
  const [isEditMode, setIsEditMode] = useState(false); // 是否处于编辑模式

  // 保留这些状态因为它们在规则编辑弹窗中的测试功能仍然需要
  const [testContext, setTestContext] = useState(''); // 测试上下文
  const [testResults, setTestResults] = useState(null); // 测试结果
  const [isTestLoading, setIsTestLoading] = useState(false); // 测试加载状态
  const [roles, setRoles] = useState([]); // 可选角色列表
  const [selectedRoleId, setSelectedRoleId] = useState(null); // 选中的角色ID
  const [rolesLoading, setRolesLoading] = useState(false); // 角色加载状态
  const [testSectionCollapsed, setTestSectionCollapsed] = useState(true); // 测试区域折叠状态

  // Monaco Editor 相关状态
  const [editorValue, setEditorValue] = useState(''); // Monaco Editor的值
  const [editorLanguage, setEditorLanguage] = useState('javascript'); // 当前编辑器语言

  // 环境变量相关状态
  const [environmentVariables, setEnvironmentVariables] = useState({
    internal: [],
    external: []
  });
  const [variablesLoading, setVariablesLoading] = useState(false);
  const [currentRuleVariables, setCurrentRuleVariables] = useState([]); // 当前规则中使用的变量信息

  // Monaco Editor 编辑器引用
  const editorRef = useRef(null);

  // TextArea 引用（用于自然语言规则）
  const textAreaRef = useRef(null);

  // 获取规则集
  useEffect(() => {
    fetchRuleSets();
  }, []);

  // 当切换到规则列表Tab时才加载所有规则
  useEffect(() => {
    if (activeTab === 'ruleEditor' && !rulesLoaded) {
      fetchAllRules();
    }
  }, [activeTab, rulesLoaded]);

  // 检查URL参数中是否包含ruleSetId
  useEffect(() => {
    // 解析URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const ruleSetIdParam = urlParams.get('ruleSetId');

    if (ruleSetIdParam) {
      console.log('URL中指定了规则集ID:', ruleSetIdParam);
      // 如果指定了规则集ID，自动切换到规则列表Tab
      setActiveTab('ruleEditor');
      // 确保规则已加载
      if (!rulesLoaded) {
        fetchAllRules(true);
      }
    }
  }, []);

  // 当规则编辑弹窗显示时，也需要加载角色数据用于测试
  useEffect(() => {
    if (ruleModalVisible && roles.length === 0) {
      console.log('规则编辑弹窗显示，加载角色数据');
      fetchRoles();
    }
  }, [ruleModalVisible, roles.length]);

  // 当规则编辑弹窗显示时，加载环境变量数据
  useEffect(() => {
    if (ruleModalVisible && environmentVariables.internal.length === 0 && environmentVariables.external.length === 0) {
      console.log('规则编辑弹窗显示，加载环境变量数据');
      fetchEnvironmentVariables();
    }
  }, [ruleModalVisible, environmentVariables.internal.length, environmentVariables.external.length]);

  // 监听规则内容变化，分析使用的变量
  useEffect(() => {
    const content = ruleForm.getFieldValue('content') || '';
    if (content && (environmentVariables.internal.length > 0 || environmentVariables.external.length > 0)) {
      const variableInfo = getTemplateVariableInfo(content, environmentVariables.internal, environmentVariables.external);
      setCurrentRuleVariables(variableInfo);
    } else {
      setCurrentRuleVariables([]);
    }
  }, [editorValue, environmentVariables]);

  // 检查缓存是否过期
  const isCacheExpired = (lastFetchTime) => {
    return Date.now() - lastFetchTime > CACHE_TIMEOUT;
  };

  const fetchRuleSets = async (forceRefresh = false) => {
    // 如果缓存有效且不强制刷新，使用缓存
    if (ruleSetsCache.length > 0 && !isCacheExpired(lastFetchRuleSetsTime) && !forceRefresh) {
      console.log('使用规则集缓存数据，跳过API请求');
      setRuleSets(ruleSetsCache);
      return;
    }

    setLoading(true);
    try {
      // 从URL获取行动空间ID
      const urlParams = new URLSearchParams(window.location.search);
      const spaceId = urlParams.get('spaceId');

      // 检查spaceId是否存在且有效（为数字）
      const isValidSpaceId = spaceId && !isNaN(parseInt(spaceId));

      console.log('请求规则集数据，行动空间ID:', isValidSpaceId ? spaceId : '未指定');

      let ruleSetsData = [];

      // 使用新的API获取规则集统计信息
      if (isValidSpaceId) {
        const data = await actionSpaceAPI.getRuleSetsStats(spaceId);
        console.log('获取到规则集统计数据:', data);
        console.log('规则集数据结构示例:', data.length > 0 ? JSON.stringify(data[0], null, 2) : '无数据');
        ruleSetsData = data;
      } else {
        // 如果没有有效的行动空间ID，使用统计API获取所有规则集
        const data = await actionSpaceAPI.getRuleSetsStats(null);
        console.log('获取到规则集统计数据:', data);
        console.log('规则集数据结构示例:', data.length > 0 ? JSON.stringify(data[0], null, 2) : '无数据');
        ruleSetsData = data;
      }

      // 确保每个规则集都有必要的字段
      const processedRuleSets = ruleSetsData.map(ruleSet => ({
        ...ruleSet,
        rule_count: ruleSet.rule_count !== undefined ? ruleSet.rule_count : 0,
        related_spaces: Array.isArray(ruleSet.related_spaces) ? ruleSet.related_spaces : [],
        rules: Array.isArray(ruleSet.rules) ? ruleSet.rules : []
      }));

      // 更新缓存和状态
      ruleSetsCache = processedRuleSets;
      lastFetchRuleSetsTime = Date.now();
      setRuleSets(processedRuleSets);
    } catch (error) {
      console.error('获取规则集列表失败:', error);
      message.error('获取规则集列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取所有规则
  const fetchAllRules = async (forceRefresh = false) => {
    // 如果缓存有效且不强制刷新，使用缓存
    if (allRulesCache.length > 0 && !isCacheExpired(lastFetchRulesTime) && !forceRefresh) {
      console.log('使用规则缓存数据，跳过API请求');
      setAllRules(allRulesCache);
      setRulesLoaded(true);
      return;
    }

    setRulesLoading(true);
    try {
      const rulesData = await actionSpaceAPI.getAllRules();
      console.log('获取到所有规则数据:', rulesData);

      // 更新缓存和状态
      allRulesCache = rulesData;
      lastFetchRulesTime = Date.now();
      setAllRules(rulesData);
      setRulesLoaded(true); // 标记规则已加载
    } catch (error) {
      console.error('获取规则列表失败:', error);
      message.error('获取规则列表失败');
    } finally {
      setRulesLoading(false);
    }
  };

  // 获取所有角色
  const fetchRoles = async () => {
    setRolesLoading(true);
    try {
      // 从URL获取行动空间ID
      const urlParams = new URLSearchParams(window.location.search);
      const spaceId = urlParams.get('spaceId');

      // 检查spaceId是否存在且有效（为数字）
      const isValidSpaceId = spaceId && !isNaN(parseInt(spaceId));

      console.log('开始获取角色数据, 行动空间ID:', isValidSpaceId ? spaceId : '未指定');

      let rolesData = [];

      if (isValidSpaceId) {
        // 如果有行动空间ID，获取该行动空间的角色
        rolesData = await actionSpaceAPI.getRoles(spaceId);
        console.log('从行动空间获取到的角色数据:', rolesData);
      }

      // 如果从行动空间没有获取到角色，尝试获取所有角色
      if (!rolesData || rolesData.length === 0) {
        console.log('尝试获取所有角色...');
        try {
          const response = await apiInstance.get('/roles');
          rolesData = response.data.roles || [];
          console.log('获取所有角色成功:', rolesData);
        } catch (error) {
          console.error('获取所有角色失败:', error);
        }
      }

      if (!rolesData || rolesData.length === 0) {
        console.warn('未找到任何角色数据，尝试使用备用API');
        try {
          // 尝试使用直接API路径
          const response = await apiInstance.get('/api/roles');
          rolesData = response.data.roles || [];
          console.log('通过备用API获取角色数据:', rolesData);
        } catch (backupError) {
          console.error('备用API获取角色也失败:', backupError);
        }
      }

      setRoles(rolesData);
      console.log('角色数据设置完成, 共', rolesData.length, '个角色');

      if (rolesData.length === 0) {
        message.warning('未找到可用的角色数据，请确保系统中已创建角色');
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
    } finally {
      setRolesLoading(false);
    }
  };

  // 获取所有环境变量
  const fetchEnvironmentVariables = async () => {
    setVariablesLoading(true);
    try {
      console.log('开始获取环境变量数据...');
      const variables = await actionSpaceAPI.getAllEnvironmentVariablesByType();
      console.log('获取到环境变量数据:', variables);

      setEnvironmentVariables(variables);
    } catch (error) {
      console.error('获取环境变量失败:', error);
      message.error('获取环境变量失败');
    } finally {
      setVariablesLoading(false);
    }
  };

  // 在光标位置插入变量到 Monaco Editor 编辑器
  const insertVariableAtCursor = (variableName) => {
    const variableText = `{{${variableName}}}`;

    // 使用保存的编辑器引用
    if (editorRef.current) {
      const editor = editorRef.current;
      const selection = editor.getSelection();
      const range = new window.monaco.Range(
        selection.startLineNumber,
        selection.startColumn,
        selection.endLineNumber,
        selection.endColumn
      );

      editor.executeEdits('insert-variable', [{
        range: range,
        text: variableText,
        forceMoveMarkers: true
      }]);

      // 设置光标位置到插入文本的末尾
      const newPosition = new window.monaco.Position(
        selection.startLineNumber,
        selection.startColumn + variableText.length
      );
      editor.setPosition(newPosition);
      editor.focus();
    } else {
      // 如果无法获取编辑器引用，则简单地在末尾添加
      const currentContent = editorValue;
      const newContent = currentContent + variableText;
      setEditorValue(newContent);
      ruleForm.setFieldsValue({ content: newContent });
      debouncedAnalyzeVariables(newContent);
    }
  };

  // 在光标位置插入变量到 TextArea（用于自然语言规则）
  const insertVariableToTextArea = (variableName) => {
    const variableText = `{{${variableName}}}`;

    if (textAreaRef.current) {
      const textArea = textAreaRef.current.resizableTextArea.textArea;
      const start = textArea.selectionStart;
      const end = textArea.selectionEnd;
      const currentValue = textArea.value;

      // 在光标位置插入变量
      const newValue = currentValue.substring(0, start) + variableText + currentValue.substring(end);

      // 更新表单值
      ruleForm.setFieldsValue({ content: newValue });

      // 触发变量分析
      debouncedAnalyzeVariables(newValue);

      // 设置新的光标位置
      setTimeout(() => {
        const newCursorPos = start + variableText.length;
        textArea.setSelectionRange(newCursorPos, newCursorPos);
        textArea.focus();
      }, 0);
    } else {
      // 如果无法获取 TextArea 引用，则简单地在末尾添加
      const currentContent = ruleForm.getFieldValue('content') || '';
      const newContent = currentContent + variableText;
      ruleForm.setFieldsValue({ content: newContent });
      debouncedAnalyzeVariables(newContent);
    }
  };

  // 简单的防抖函数
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // 防抖的变量分析函数
  const debouncedAnalyzeVariables = useCallback(
    debounce((content) => {
      if (content && (environmentVariables.internal.length > 0 || environmentVariables.external.length > 0)) {
        const variableInfo = getTemplateVariableInfo(content, environmentVariables.internal, environmentVariables.external);
        setCurrentRuleVariables(variableInfo);
      } else {
        setCurrentRuleVariables([]);
      }
    }, 300), // 300ms 防抖
    [environmentVariables]
  );

  const handleCreateRuleSet = () => {
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
  };

  const handleModalSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 从URL获取行动空间ID
      const urlParams = new URLSearchParams(window.location.search);
      const spaceId = urlParams.get('spaceId');

      // 检查spaceId是否存在且有效（为数字）
      const isValidSpaceId = spaceId && !isNaN(parseInt(spaceId));

      // 构建规则集数据
      const ruleSetData = {
        name: values.name,
        description: values.description
      };

      // 如果行动空间ID有效，添加到数据中
      if (isValidSpaceId) {
        ruleSetData.action_space_id = parseInt(spaceId);
      }

      console.log('创建规则集:', ruleSetData);

      // 创建规则集
      await actionSpaceAPI.createRuleSet(ruleSetData);
      message.success('规则集创建成功');
      setIsModalVisible(false);
      // 重新获取规则集列表，强制刷新缓存
      fetchRuleSets(true);
    } catch (error) {
      console.error('创建规则集失败:', error);
      message.error('创建规则集失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRuleSet = async (id) => {
    try {
      await actionSpaceAPI.deleteRuleSet(id);
      message.success('规则集删除成功');
      // 重新获取规则集列表，强制刷新缓存
      fetchRuleSets(true);
    } catch (error) {
      console.error('删除规则集失败:', error);
      message.error('删除规则集失败');
    }
  };

  // 规则关联相关状态
  const [ruleAssociationModalVisible, setRuleAssociationModalVisible] = useState(false);
  const [currentRuleSet, setCurrentRuleSet] = useState(null);
  const [allRulesForAssociation, setAllRulesForAssociation] = useState([]);
  const [associatedRuleIds, setAssociatedRuleIds] = useState([]); // 当前已关联的规则ID
  const [selectedRuleIds, setSelectedRuleIds] = useState([]); // 用户选择的规则ID（临时状态）
  const [associationLoading, setAssociationLoading] = useState(false);
  const [associationSaving, setAssociationSaving] = useState(false);

  const handleRuleSetEdit = (ruleSet) => {
    // 修改为打开规则关联模态框
    console.log('编辑规则集关联:', ruleSet);
    setCurrentRuleSet(ruleSet);
    setRuleAssociationModalVisible(true);
    fetchRulesForAssociation(ruleSet.id);
  };

  // 获取所有规则和当前规则集的关联关系
  const fetchRulesForAssociation = async (ruleSetId) => {
    setAssociationLoading(true);
    try {
      // 获取所有规则
      const allRules = await actionSpaceAPI.getAllRules();
      setAllRulesForAssociation(allRules);

      // 获取当前规则集的详细信息，包括关联的规则
      const ruleSetDetail = await actionSpaceAPI.getRuleSetDetail(ruleSetId);
      const associatedIds = ruleSetDetail.rules ? ruleSetDetail.rules.map(rule => rule.id) : [];
      setAssociatedRuleIds(associatedIds);
      // 初始化选择状态为当前已关联的规则
      setSelectedRuleIds([...associatedIds]);
    } catch (error) {
      console.error('获取规则关联数据失败:', error);
      message.error('获取规则关联数据失败');
    } finally {
      setAssociationLoading(false);
    }
  };

  // 处理规则选择状态变化（仅更新临时状态，不立即执行API调用）
  const handleRuleSelectionChange = (ruleId, isSelected) => {
    if (isSelected) {
      setSelectedRuleIds(prev => [...prev, ruleId]);
    } else {
      setSelectedRuleIds(prev => prev.filter(id => id !== ruleId));
    }
  };

  // 保存规则关联变更
  const handleSaveRuleAssociation = async () => {
    if (!currentRuleSet) return;

    setAssociationSaving(true);
    try {
      // 计算需要添加和移除的规则
      const toAdd = selectedRuleIds.filter(id => !associatedRuleIds.includes(id));
      const toRemove = associatedRuleIds.filter(id => !selectedRuleIds.includes(id));

      // 执行添加操作
      for (const ruleId of toAdd) {
        await actionSpaceAPI.addRuleToRuleSet(currentRuleSet.id, ruleId);
      }

      // 执行移除操作
      for (const ruleId of toRemove) {
        await actionSpaceAPI.removeRuleFromRuleSet(currentRuleSet.id, ruleId);
      }

      // 更新状态
      setAssociatedRuleIds([...selectedRuleIds]);

      // 显示成功消息
      if (toAdd.length > 0 || toRemove.length > 0) {
        message.success(`规则关联更新成功：新增 ${toAdd.length} 条，移除 ${toRemove.length} 条`);
      } else {
        message.info('没有变更需要保存');
      }

      // 关闭modal
      handleRuleAssociationModalClose();
    } catch (error) {
      console.error('保存规则关联失败:', error);
      message.error('保存规则关联失败');
    } finally {
      setAssociationSaving(false);
    }
  };

  // 关闭规则关联模态框
  const handleRuleAssociationModalClose = () => {
    setRuleAssociationModalVisible(false);
    setCurrentRuleSet(null);
    setAllRulesForAssociation([]);
    setAssociatedRuleIds([]);
    setSelectedRuleIds([]);
    // 刷新规则集列表以更新规则数量
    fetchRuleSets(true);
  };

  // 取消规则关联编辑
  const handleCancelRuleAssociation = () => {
    setRuleAssociationModalVisible(false);
    setCurrentRuleSet(null);
    setAllRulesForAssociation([]);
    setAssociatedRuleIds([]);
    setSelectedRuleIds([]);
    // 取消时不刷新列表
  };

  const showAddRuleModal = () => {
    // 确保不是编辑模式
    setIsEditMode(false);
    setEditingRule(null);

    // 重置表单
    ruleForm.resetFields();

    // 默认设置为自然语言规则类型
    setRuleEditType('llm');

    // 重置Monaco Editor状态
    setEditorValue('');
    setEditorLanguage('javascript');

    // 显示弹窗
    setRuleModalVisible(true);
  };

  const handleRuleModalCancel = () => {
    setRuleModalVisible(false);
    setEditingRule(null);
    setIsEditMode(false);
    // 重置测试相关状态
    setTestContext('');
    setTestResults(null);
    setSelectedRoleId(null);
    setTestSectionCollapsed(true);
    // 重置Monaco Editor状态
    setEditorValue('');
    setEditorLanguage('javascript');
  };

  const handleRuleModalSubmit = async () => {
    try {
      const values = await ruleForm.validateFields();
      setLoading(true);

      // 构建规则数据
      const ruleData = {
        name: values.name,
        type: ruleEditType,
        content: values.content
      };

      // 如果是逻辑规则，添加解释器信息
      if (ruleEditType === 'logic') {
        ruleData.interpreter = values.interpreter || 'javascript';
        console.log('提交逻辑规则数据，解释器:', ruleData.interpreter);
      }

      if (isEditMode && editingRule) {
        // 编辑模式：更新现有规则
        await actionSpaceAPI.updateRule(editingRule.id, ruleData);
        message.success('规则更新成功');

        // 编辑逻辑规则后强制刷新数据
        if (ruleEditType === 'logic') {
          console.log('强制刷新规则列表...');
          // 清空缓存
          allRulesCache = [];
          lastFetchRulesTime = 0;
        }
      } else {
        // 新建模式：创建新规则
        await actionSpaceAPI.createRule(ruleData);
        message.success('规则添加成功');
      }

      setRuleModalVisible(false);
      setEditingRule(null);
      setIsEditMode(false);

      // 在规则集标签页时刷新规则集统计信息，在规则列表标签页时刷新规则列表
      if (activeTab === 'ruleSets') {
        fetchRuleSets(true); // 强制刷新缓存
      } else if (activeTab === 'ruleEditor') {
        fetchAllRules(true); // 强制刷新缓存
      }
    } catch (error) {
      console.error(isEditMode ? '编辑规则失败:' : '添加规则失败:', error);
      message.error(isEditMode ? '编辑规则失败' : '添加规则失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRule = async (ruleId) => {
    try {
      await actionSpaceAPI.deleteRule(ruleId);
      message.success('规则删除成功');

      // 在规则集标签页时刷新规则集统计信息，在规则列表标签页时刷新规则列表
      if (activeTab === 'ruleSets') {
        fetchRuleSets(true); // 强制刷新缓存
      } else if (activeTab === 'ruleEditor') {
        fetchAllRules(true); // 强制刷新缓存
      }
    } catch (error) {
      console.error('删除规则失败:', error);
      message.error('删除规则失败');
    }
  };

  // 规则集Tab渲染
  const renderRuleSetsTab = () => {
    const columns = [
      {
        title: '规则集名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: (a, b) => a.name.localeCompare(b.name),
        ellipsis: true,
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        width: 300,
        render: (description) => (
          <div style={{ maxHeight: '60px', overflow: 'hidden' }}>
            <Text style={{ fontSize: '12px', lineHeight: '1.4' }}>
              {description || '暂无描述'}
            </Text>
          </div>
        ),
      },
      {
        title: '规则数量',
        key: 'rule_count',
        dataIndex: 'rule_count',
        width: 100,
        align: 'center',
        render: (ruleCount) => (
          <Tag color="blue">
            {Number.isInteger(ruleCount) ? ruleCount : 0} 条
          </Tag>
        ),
        sorter: (a, b) => (a.rule_count || 0) - (b.rule_count || 0),
      },
      {
        title: '关联行动空间',
        key: 'related_spaces',
        dataIndex: 'related_spaces',
        width: 200,
        render: (relatedSpaces) => (
          <>
            {Array.isArray(relatedSpaces) && relatedSpaces.length > 0 ? (
              relatedSpaces.map(space => (
                <Tag key={space.id} color="green" style={{ marginBottom: 2 }}>
                  {space.name}
                </Tag>
              ))
            ) : (
              <Text type="secondary">未关联</Text>
            )}
          </>
        ),
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        render: (_, record) => (
          <Space>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleRuleSetEdit(record)}
              title="关联规则"
              size="small"
            />
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteRuleSet(record.id)}
              title="删除规则集"
              size="small"
            />
          </Space>
        ),
      },
    ];

    return (
      <Card
        title="规则集管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateRuleSet}
          >
            新建规则集
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={ruleSets}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
          }}
          size="middle"
        />
      </Card>
    );
  };

  // 规则列表Tab渲染
  const renderRuleListTab = () => {

    // 使用直接获取的所有规则，而不是从规则集中提取
    const columns = [
      {
        title: '规则名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: (a, b) => a.name.localeCompare(b.name),
        ellipsis: true,
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        width: 120,
        render: (type) => (
          <Tag color={type === 'llm' ? 'green' : 'blue'}>
            {type === 'llm' ? '自然语言' : '逻辑规则'}
          </Tag>
        ),
        filters: [
          { text: '自然语言规则', value: 'llm' },
          { text: '逻辑规则', value: 'logic' },
        ],
        onFilter: (value, record) => record.type === value,
      },
      {
        title: '所属规则集',
        dataIndex: 'rule_sets',
        key: 'rule_sets',
        width: 150,
        render: (ruleSets) => (
          <>
            {ruleSets && ruleSets.length > 0
              ? ruleSets.map(rs => (
                  <Tag key={rs.id} color="blue" style={{ marginBottom: 2 }}>{rs.name}</Tag>
                ))
              : <Text type="secondary">未分配</Text>
            }
          </>
        ),
        filters: ruleSets.map(rs => ({ text: rs.name, value: String(rs.id) })),
        onFilter: (value, record) => record.rule_sets?.some(rs => String(rs.id) === value),
      },
      {
        title: '状态',
        dataIndex: 'is_active',
        key: 'is_active',
        width: 80,
        render: isActive => (
          <Tag color={isActive === false ? 'error' : 'success'}>
            {isActive === false ? '禁用' : '启用'}
          </Tag>
        ),
        filters: [
          { text: '已启用', value: true },
          { text: '已禁用', value: false },
        ],
        onFilter: (value, record) => record.is_active === value,
      },
      {
        title: '规则内容',
        dataIndex: 'content',
        key: 'content',
        width: 300,
        render: (content, record) => (
          <div style={{ maxHeight: '60px', overflow: 'hidden' }}>
            <Text
              style={{
                fontSize: '12px',
                fontFamily: record.type === 'logic' ? 'monospace' : 'inherit',
                lineHeight: '1.4'
              }}
            >
              {content}
            </Text>
          </div>
        ),
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        render: (_, record) => (
          <Space>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showEditRuleModal(record)}
              size="small"
            />
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteRule(record.id)}
              size="small"
            />
          </Space>
        ),
      },
    ];

    return (
      <Card
        title="规则列表"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => showAddRuleModal()}
            disabled={rulesLoading}
          >
            添加规则
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={allRules}
          rowKey="id"
          loading={rulesLoading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
          }}

          size="middle"
        />
      </Card>
    );
  };

  // 显示编辑规则的弹窗
  const showEditRuleModal = (rule) => {
    // 设置编辑模式
    setIsEditMode(true);
    setEditingRule(rule);

    // 设置规则类型
    setRuleEditType(rule.type || 'llm');

    // 获取解释器信息
    let interpreter = 'javascript';
    if (rule.type === 'logic') {
      if (rule.interpreter) {
        // 如果API直接返回了interpreter字段
        interpreter = rule.interpreter;
        console.log('从API获取解释器:', interpreter);
      } else if (rule.settings && rule.settings.interpreter) {
        // 如果interpreter在settings中
        interpreter = rule.settings.interpreter;
        console.log('从settings获取解释器:', interpreter);
      }
    }

    // 预填充表单数据
    ruleForm.resetFields(); // 先重置表单，避免旧数据残留

    // 初始化Monaco Editor状态
    setEditorValue(rule.content || '');
    setEditorLanguage(interpreter === 'python' ? 'python' : 'javascript');

    // 延迟设置表单值，确保状态更新后再填充表单
    setTimeout(() => {
      ruleForm.setFieldsValue({
        name: rule.name,
        content: rule.content,
        ruleType: rule.type, // 明确设置规则类型字段
        interpreter: interpreter
      });

      console.log('编辑规则:', rule.id, rule.name);
      console.log('规则类型:', rule.type);
      console.log('解释器:', interpreter);
    }, 100);

    // 显示弹窗
    setRuleModalVisible(true);
  };

  return (
    <div>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16
      }}>
        <div>
          <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>行动规则</Title>
          <Text type="secondary">
            管理行动空间中的规则集和规则，支持自然语言规则和逻辑规则的创建、编辑和测试
          </Text>
        </div>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={key => {
          setActiveTab(key);
        }}
        items={[
          {
            key: 'ruleSets',
            label: <span><PartitionOutlined />规则集</span>,
            children: renderRuleSetsTab()
          },
          {
            key: 'ruleEditor',
            label: <span><CodeOutlined />规则列表</span>,
            children: renderRuleListTab()
          }
        ]}
      />

      {/* 创建规则集对话框 */}
      <Modal
        title="创建规则集"
        visible={isModalVisible}
        onCancel={handleModalCancel}
        onOk={handleModalSubmit}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入规则集名称' }]}
          >
            <Input placeholder="输入规则集名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入规则集描述' }]}
          >
            <TextArea rows={3} placeholder="输入规则集描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 规则关联对话框 */}
      <Modal
        title={`关联规则 - ${currentRuleSet?.name || ''}`}
        visible={ruleAssociationModalVisible}
        onCancel={handleCancelRuleAssociation}
        width={800}
        footer={[
          <Button
            key="save"
            type="primary"
            loading={associationSaving}
            onClick={handleSaveRuleAssociation}
          >
            确定
          </Button>,
          <Button key="cancel" onClick={handleCancelRuleAssociation}>
            取消
          </Button>
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            选择要关联到规则集 "{currentRuleSet?.name}" 的规则。勾选或取消勾选后，点击"确定"按钮保存更改。
          </Text>
        </div>

        {associationLoading ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin tip="加载规则数据中..." />
          </div>
        ) : (
          <Table
            dataSource={allRulesForAssociation}
            rowKey="id"
            pagination={{ pageSize: 10 }}
            columns={[
              {
                title: '选择',
                key: 'select',
                width: 60,
                render: (_, record) => (
                  <Checkbox
                    checked={selectedRuleIds.includes(record.id)}
                    onChange={(e) => handleRuleSelectionChange(record.id, e.target.checked)}
                  />
                )
              },
              {
                title: '规则名称',
                dataIndex: 'name',
                key: 'name',
              },
              {
                title: '类型',
                dataIndex: 'type',
                key: 'type',
                width: 120,
                render: (type) => (
                  <Tag color={type === 'llm' ? 'green' : 'blue'}>
                    {type === 'llm' ? '自然语言' : '逻辑规则'}
                  </Tag>
                )
              },
              {
                title: '描述',
                dataIndex: 'description',
                key: 'description',
                ellipsis: true,
              },
              {
                title: '状态',
                dataIndex: 'is_active',
                key: 'is_active',
                width: 80,
                render: (isActive) => (
                  <Tag color={isActive ? 'success' : 'default'}>
                    {isActive ? '启用' : '禁用'}
                  </Tag>
                )
              }
            ]}
          />
        )}
      </Modal>

      {/* 添加规则对话框 */}
      <Modal
        title={`${isEditMode ? '编辑' : '添加'}${ruleEditType === 'llm' ? '自然语言' : '逻辑'}规则`}
        visible={ruleModalVisible}
        onCancel={handleRuleModalCancel}
        width={900}
        style={{ top: 20 }}
        styles={{
          body: { maxHeight: 'calc(100vh - 200px)', overflowY: 'auto' }
        }}
        footer={[
          <Button
            key="save"
            type="primary"
            loading={loading}
            onClick={handleRuleModalSubmit}
          >
            {isEditMode ? '保存' : '创建'}
          </Button>,
          <Button key="cancel" onClick={handleRuleModalCancel}>
            取消
          </Button>
        ]}
      >
        {/* 编辑规则表单部分 */}
        <Form
          form={ruleForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="输入规则名称" />
          </Form.Item>

          <Form.Item
            name="ruleType"
            label="规则类型"
            initialValue={ruleEditType}
          >
            <Radio.Group onChange={e => setRuleEditType(e.target.value)} value={ruleEditType}>
              <Radio value="llm">自然语言规则</Radio>
              <Radio value="logic">逻辑规则</Radio>
            </Radio.Group>
          </Form.Item>



          {ruleEditType === 'llm' ? (
            <>
              <Form.Item
                name="content"
                label={
                  <span>
                    规则内容
                    <Tooltip title="可以使用 {{变量名}} 格式引用环境变量">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                    </Tooltip>
                  </span>
                }
                rules={[{ required: true, message: '请输入规则内容' }]}
              >
                <TextArea
                  ref={textAreaRef}
                  rows={8}
                  placeholder="使用自然语言描述规则，例如：如果玩家进入危险区域，则生命值每秒减少10点。可以使用 {{变量名}} 引用环境变量。"
                  onChange={(e) => {
                    // 触发变量分析
                    const content = e.target.value;
                    debouncedAnalyzeVariables(content);
                  }}
                />
              </Form.Item>

              {/* 显示当前规则中使用的变量信息 */}
              {currentRuleVariables.length > 0 && (
                <div style={{ marginBottom: 16, padding: '12px', backgroundColor: '#f6ffed', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong style={{ fontSize: '13px' }}>
                      <InfoCircleOutlined style={{ marginRight: 4, color: '#52c41a' }} />
                      检测到的模板变量:
                    </Text>
                  </div>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                    {currentRuleVariables.map((variable, index) => (
                      <Tooltip
                        key={index}
                        title={
                          <div>
                            <div><strong>变量名:</strong> {variable.name}</div>
                            <div><strong>标签:</strong> {variable.label}</div>
                            <div><strong>来源:</strong> {variable.source}</div>
                            <div><strong>当前值:</strong> {variable.value || '未设置'}</div>
                            {variable.description && <div><strong>描述:</strong> {variable.description}</div>}
                          </div>
                        }
                      >
                        <Tag
                          color={variable.type === 'internal' ? 'blue' : variable.type === 'external' ? 'green' : 'red'}
                          style={{ cursor: 'help' }}
                        >
                          {variable.name}
                        </Tag>
                      </Tooltip>
                    ))}
                  </div>
                </div>
              )}

              {/* 可用环境变量列表 */}
              {(environmentVariables.internal.length > 0 || environmentVariables.external.length > 0) && (
                <Collapse
                  size="small"
                  style={{ marginBottom: 16 }}
                  items={[
                    {
                      key: 'available-variables',
                      label: (
                        <span>
                          <InfoCircleOutlined style={{ marginRight: 4, color: '#1677ff' }} />
                          可用环境变量 (点击插入)
                        </span>
                      ),
                      children: (
                        <div>
                          {/* 按行动空间分组显示内部环境变量 */}
                          {(() => {
                            // 按行动空间分组内部环境变量
                            const groupedInternalVars = environmentVariables.internal.reduce((groups, variable) => {
                              const spaceName = variable.action_space_name || '未分类';
                              if (!groups[spaceName]) {
                                groups[spaceName] = [];
                              }
                              groups[spaceName].push(variable);
                              return groups;
                            }, {});

                            return Object.keys(groupedInternalVars).map(spaceName => (
                              <div key={`internal-space-${spaceName}`} style={{ marginBottom: 12 }}>
                                <div style={{ marginBottom: 6 }}>
                                  <Text strong style={{ fontSize: '12px', color: '#1677ff' }}>
                                    📁 {spaceName} (内部变量)
                                  </Text>
                                </div>
                                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px', marginLeft: 12 }}>
                                  {groupedInternalVars[spaceName].map((variable, index) => (
                                    <Tooltip
                                      key={`internal-${spaceName}-${index}`}
                                      title={
                                        <div>
                                          <div><strong>变量名:</strong> {variable.name}</div>
                                          <div><strong>标签:</strong> {variable.label}</div>
                                          <div><strong>行动空间:</strong> {variable.action_space_name}</div>
                                          <div><strong>来源:</strong> 内部变量</div>
                                          <div><strong>默认值:</strong> {variable.default_value || '未设置'}</div>
                                          {variable.description && <div><strong>描述:</strong> {variable.description}</div>}
                                          <div style={{ marginTop: 4, fontSize: '12px', color: '#999' }}>点击插入到规则中</div>
                                        </div>
                                      }
                                    >
                                      <Tag
                                        color="blue"
                                        style={{ cursor: 'pointer', fontSize: '11px' }}
                                        onClick={() => insertVariableToTextArea(variable.name)}
                                      >
                                        {variable.name}
                                      </Tag>
                                    </Tooltip>
                                  ))}
                                </div>
                              </div>
                            ));
                          })()}

                          {/* 外部环境变量 */}
                          {environmentVariables.external.length > 0 && (
                            <div style={{ marginBottom: 8 }}>
                              <div style={{ marginBottom: 6 }}>
                                <Text strong style={{ fontSize: '12px', color: '#52c41a' }}>
                                  🌐 外部环境变量
                                </Text>
                              </div>
                              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px', marginLeft: 12 }}>
                                {environmentVariables.external.map((variable, index) => (
                                  <Tooltip
                                    key={`external-${index}`}
                                    title={
                                      <div>
                                        <div><strong>变量名:</strong> {variable.name}</div>
                                        <div><strong>标签:</strong> {variable.label}</div>
                                        <div><strong>来源:</strong> 外部变量</div>
                                        <div><strong>当前值:</strong> {variable.current_value || '未设置'}</div>
                                        {variable.description && <div><strong>描述:</strong> {variable.description}</div>}
                                        <div style={{ marginTop: 4, fontSize: '12px', color: '#999' }}>点击插入到规则中</div>
                                      </div>
                                    }
                                  >
                                    <Tag
                                      color="green"
                                      style={{ cursor: 'pointer', fontSize: '11px' }}
                                      onClick={() => insertVariableToTextArea(variable.name)}
                                    >
                                      {variable.name}
                                    </Tag>
                                  </Tooltip>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )
                    }
                  ]}
                />
              )}
            </>
          ) : (
            <>
              <Form.Item
                name="interpreter"
                label={
                  <span>
                    规则解释器
                    <Tooltip title="选择用于执行规则代码的解释器">
                      <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                    </Tooltip>
                  </span>
                }
                initialValue="javascript"
                rules={[{ required: true, message: '请选择规则解释器' }]}
              >
                <Select
                  placeholder="选择规则解释器"
                  onChange={(value) => {
                    // 当解释器改变时，更新Monaco Editor的语言
                    setEditorLanguage(value === 'python' ? 'python' : 'javascript');
                  }}
                >
                  <Option value="javascript">JavaScript</Option>
                  <Option value="python">Python</Option>
                </Select>
              </Form.Item>
              <Form.Item
                name="content"
                label={
                  <span>
                    规则代码
                    <Tooltip
                      title={
                        <div>
                          <div>JavaScript示例: return context.age {'>'}= 18;</div>
                          <div>Python示例: return context['age'] {'>'}= 18</div>
                          <div>规则代码需要返回布尔值表示是否通过测试</div>
                          <div>可以使用 {'{'}{'{'} 变量名 {'}'}{'}'}  格式引用环境变量</div>
                        </div>
                      }
                    >
                      <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                    </Tooltip>
                  </span>
                }
                rules={[{ required: true, message: '请输入规则代码' }]}
              >
                <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', overflow: 'hidden' }}>
                  <Editor
                    height="200px"
                    defaultLanguage={editorLanguage}
                    language={editorLanguage}
                    theme="vs-dark"
                    value={editorValue}
                    onChange={(value) => {
                      const newValue = value || '';
                      setEditorValue(newValue);
                      ruleForm.setFieldsValue({ content: newValue });
                      // 使用防抖的变量分析
                      debouncedAnalyzeVariables(newValue);
                    }}
                    onMount={(editor, monaco) => {
                      // 保存编辑器引用
                      editorRef.current = editor;
                    }}
                    options={{
                      fontSize: 14,
                      fontFamily: 'JetBrains Mono, Consolas, Menlo, Monaco, monospace',
                      lineNumbers: 'on',
                      minimap: { enabled: false },
                      scrollBeyondLastLine: false,
                      automaticLayout: true,
                      tabSize: 2,
                      insertSpaces: true,
                      wordWrap: 'on',
                      formatOnPaste: true,
                      formatOnType: true,
                      folding: true,
                      bracketPairColorization: { enabled: true },
                      autoClosingBrackets: 'always',
                      autoClosingQuotes: 'always',
                      autoIndent: 'full',
                      cursorBlinking: 'blink',
                      cursorSmoothCaretAnimation: 'on',
                      smoothScrolling: true,
                      mouseWheelZoom: true,
                      contextmenu: true,
                      find: {
                        addExtraSpaceOnTop: false,
                        autoFindInSelection: 'never',
                        seedSearchStringFromSelection: 'always'
                      }
                    }}
                  />
                </div>
              </Form.Item>

              {/* 显示当前规则中使用的变量信息 */}
              {currentRuleVariables.length > 0 && (
                <div style={{ marginBottom: 16, padding: '12px', backgroundColor: '#f6ffed', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong style={{ fontSize: '13px' }}>
                      <InfoCircleOutlined style={{ marginRight: 4, color: '#52c41a' }} />
                      检测到的模板变量:
                    </Text>
                  </div>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                    {currentRuleVariables.map((variable, index) => (
                      <Tooltip
                        key={index}
                        title={
                          <div>
                            <div><strong>变量名:</strong> {variable.name}</div>
                            <div><strong>标签:</strong> {variable.label}</div>
                            <div><strong>来源:</strong> {variable.source}</div>
                            <div><strong>当前值:</strong> {variable.value || '未设置'}</div>
                            {variable.description && <div><strong>描述:</strong> {variable.description}</div>}
                          </div>
                        }
                      >
                        <Tag
                          color={variable.type === 'internal' ? 'blue' : variable.type === 'external' ? 'green' : 'red'}
                          style={{ cursor: 'help' }}
                        >
                          {variable.name}
                        </Tag>
                      </Tooltip>
                    ))}
                  </div>
                </div>
              )}

              {/* 可用环境变量列表 */}
              {(environmentVariables.internal.length > 0 || environmentVariables.external.length > 0) && (
                <Collapse
                  size="small"
                  style={{ marginBottom: 16 }}
                  items={[
                    {
                      key: 'available-variables-logic',
                      label: (
                        <span>
                          <InfoCircleOutlined style={{ marginRight: 4, color: '#1677ff' }} />
                          可用环境变量 (点击插入)
                        </span>
                      ),
                      children: (
                        <div>
                          {/* 按行动空间分组显示内部环境变量 */}
                          {(() => {
                            // 按行动空间分组内部环境变量
                            const groupedInternalVars = environmentVariables.internal.reduce((groups, variable) => {
                              const spaceName = variable.action_space_name || '未分类';
                              if (!groups[spaceName]) {
                                groups[spaceName] = [];
                              }
                              groups[spaceName].push(variable);
                              return groups;
                            }, {});

                            return Object.keys(groupedInternalVars).map(spaceName => (
                              <div key={`internal-space-${spaceName}`} style={{ marginBottom: 12 }}>
                                <div style={{ marginBottom: 6 }}>
                                  <Text strong style={{ fontSize: '12px', color: '#1677ff' }}>
                                    📁 {spaceName} (内部变量)
                                  </Text>
                                </div>
                                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px', marginLeft: 12 }}>
                                  {groupedInternalVars[spaceName].map((variable, index) => (
                                    <Tooltip
                                      key={`internal-${spaceName}-${index}`}
                                      title={
                                        <div>
                                          <div><strong>变量名:</strong> {variable.name}</div>
                                          <div><strong>标签:</strong> {variable.label}</div>
                                          <div><strong>行动空间:</strong> {variable.action_space_name}</div>
                                          <div><strong>来源:</strong> 内部变量</div>
                                          <div><strong>默认值:</strong> {variable.default_value || '未设置'}</div>
                                          {variable.description && <div><strong>描述:</strong> {variable.description}</div>}
                                          <div style={{ marginTop: 4, fontSize: '12px', color: '#999' }}>点击插入到代码中</div>
                                        </div>
                                      }
                                    >
                                      <Tag
                                        color="blue"
                                        style={{ cursor: 'pointer', fontSize: '11px' }}
                                        onClick={() => insertVariableAtCursor(variable.name)}
                                      >
                                        {variable.name}
                                      </Tag>
                                    </Tooltip>
                                  ))}
                                </div>
                              </div>
                            ));
                          })()}

                          {/* 外部环境变量 */}
                          {environmentVariables.external.length > 0 && (
                            <div style={{ marginBottom: 8 }}>
                              <div style={{ marginBottom: 6 }}>
                                <Text strong style={{ fontSize: '12px', color: '#52c41a' }}>
                                  🌐 外部环境变量
                                </Text>
                              </div>
                              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px', marginLeft: 12 }}>
                                {environmentVariables.external.map((variable, index) => (
                                  <Tooltip
                                    key={`external-${index}`}
                                    title={
                                      <div>
                                        <div><strong>变量名:</strong> {variable.name}</div>
                                        <div><strong>标签:</strong> {variable.label}</div>
                                        <div><strong>来源:</strong> 外部变量</div>
                                        <div><strong>当前值:</strong> {variable.current_value || '未设置'}</div>
                                        {variable.description && <div><strong>描述:</strong> {variable.description}</div>}
                                        <div style={{ marginTop: 4, fontSize: '12px', color: '#999' }}>点击插入到代码中</div>
                                      </div>
                                    }
                                  >
                                    <Tag
                                      color="green"
                                      style={{ cursor: 'pointer', fontSize: '11px' }}
                                      onClick={() => insertVariableAtCursor(variable.name)}
                                    >
                                      {variable.name}
                                    </Tag>
                                  </Tooltip>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )
                    }
                  ]}
                />
              )}
            </>
          )}
        </Form>

        {/* 可折叠的测试区域 */}
        <div style={{ marginTop: 16 }}>
          <Button
            type="text"
            icon={testSectionCollapsed ? <DownOutlined /> : <UpOutlined />}
            onClick={() => setTestSectionCollapsed(!testSectionCollapsed)}
            style={{
              padding: 0,
              height: 'auto',
              fontSize: '14px',
              fontWeight: 500,
              color: '#1677ff'
            }}
          >
            规则测试 {testSectionCollapsed ? '(点击展开)' : '(点击收起)'}
          </Button>
        </div>

        {/* 测试规则部分 */}
        {!testSectionCollapsed && (
          <div style={{ overflow: 'hidden', marginTop: 16, padding: '16px', backgroundColor: '#fafafa', borderRadius: '8px' }}>
          <Row gutter={[16, 0]} wrap>
            <Col xs={24} md={12}>
              <div style={{ marginBottom: 16 }}>
                {ruleEditType === 'llm' ? (
                  <div style={{ marginBottom: 16 }}>
                    <Title level={5}>选择测试角色</Title>
                    <Select
                      style={{ width: '100%' }}
                      placeholder="选择用于测试的角色"
                      value={selectedRoleId}
                      onChange={setSelectedRoleId}
                      loading={rolesLoading}
                      optionFilterProp="children"
                    >
                      {roles.map(role => (
                        <Option key={role.id} value={role.id}>
                          {role.name}
                        </Option>
                      ))}
                    </Select>
                    <div style={{ marginTop: 4 }}>
                      <Text type="secondary">
                        自然语言规则测试需要选择一个角色进行测试
                      </Text>
                    </div>
                  </div>
                ) : (
                  <div style={{ marginBottom: 16 }}>
                    <Title level={5}>逻辑规则测试</Title>
                    <div style={{ padding: '8px 12px', backgroundColor: '#f6ffed', borderRadius: '6px', border: '1px solid #b7eb8f', marginBottom: 12 }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        系统将在安全沙箱中执行规则代码，规则需返回布尔值，有5秒超时限制。
                      </Text>
                    </div>
                  </div>
                )}

                {ruleEditType === 'llm' && (
                  <div>
                    <Title level={5}>输入测试场景</Title>
                    <TextArea
                      rows={6}
                      value={testContext}
                      onChange={e => setTestContext(e.target.value)}
                      placeholder="描述一个测试场景，例如: 用户尝试在没有足够权限的情况下删除一个文件"
                    />
                  </div>
                )}

                <div style={{ marginTop: 16 }}>
                  <Button
                    type="primary"
                    onClick={() => {
                      // 获取当前编辑的规则数据用于测试
                      const currentRuleData = {
                        id: editingRule?.id || 'temp-id',
                        name: ruleForm.getFieldValue('name') || '临时规则',
                        type: ruleEditType,
                        // 始终使用表单中当前编辑的内容，确保测试的是最新修改
                        content: ruleForm.getFieldValue('content') || '',
                        interpreter: ruleForm.getFieldValue('interpreter') || 'javascript'
                      };

                      // 记录测试内容和解释器信息
                      console.log('测试规则数据:', {
                        content: currentRuleData.content,
                        interpreter: currentRuleData.interpreter,
                        type: currentRuleData.type,
                        editorLanguage: editorLanguage
                      });

                      // 只测试当前编辑的规则
                      const testingRuleList = [currentRuleData];

                      // 执行测试
                      const handleTestCurrentRule = async () => {
                        // 对于自然语言规则，需要检查测试场景是否填写
                        if (ruleEditType === 'llm' && !testContext.trim()) {
                          message.warning('请输入测试场景描述');
                          return;
                        }

                        // 检查是否有自然语言规则且未选择角色
                        if (ruleEditType === 'llm' && !selectedRoleId) {
                          message.warning('测试自然语言规则时请选择一个角色');
                          return;
                        }

                        setIsTestLoading(true);
                        try {
                          // 为逻辑规则提供默认测试上下文
                          const testData = ruleEditType === 'logic' ?
                            { scenario: '默认测试场景' } :
                            testContext;

                          // 准备环境变量用于模板替换
                          const variables = formatEnvironmentVariables(
                            environmentVariables.internal,
                            environmentVariables.external
                          );

                          // 使用API调用执行规则测试，传入选中的角色ID和环境变量
                          // 注意: 此处传入的是表单中当前编辑的规则内容，而非原始保存的规则
                          const results = await actionSpaceAPI.testRules(testingRuleList, testData, selectedRoleId, variables);
                          setTestResults(results);
                          message.success('规则测试完成');
                        } catch (error) {
                          console.error('规则测试失败:', error);
                          message.error('规则测试失败');
                        } finally {
                          setIsTestLoading(false);
                        }
                      };

                      handleTestCurrentRule();
                    }}
                    loading={isTestLoading}
                    disabled={
                      (ruleEditType === 'llm' && (!testContext.trim() || !selectedRoleId))
                    }
                  >
                    执行测试
                  </Button>
                  <Button
                    style={{ marginLeft: 8 }}
                    onClick={() => {
                      setTestContext('');
                      setTestResults(null);
                      setSelectedRoleId(null);
                    }}
                  >
                    重置
                  </Button>
                </div>
              </div>
            </Col>

            <Col xs={24} md={12}>
              <div>
                <Title level={5}>测试结果</Title>
                {isTestLoading ? (
                  <div style={{ textAlign: 'center', padding: '20px 0' }}>
                    <Spin tip="测试执行中..." />
                  </div>
                ) : testResults ? (
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      <Text>测试时间: {new Date(testResults.timestamp).toLocaleString()}</Text>
                    </div>

                    {testResults.results.map((result, index) => (
                      <Card
                        key={index}
                        size="small"
                        style={{
                          marginBottom: 8,
                          borderLeft: `4px solid ${result.passed ? '#52c41a' : '#f5222d'}`
                        }}
                      >
                        <div>
                          <Space>
                            <Text strong>{result.rule_name}</Text>
                            <Tag color={result.rule_type === 'llm' ? 'green' : 'blue'}>
                              {result.rule_type === 'llm' ? '自然语言规则' : '逻辑规则'}
                            </Tag>
                            <Tag color={result.passed ? 'success' : 'error'}>
                              {result.passed ? '通过' : '失败'}
                            </Tag>
                          </Space>
                        </div>
                        <div style={{ marginTop: 8 }}>
                          <Text>{result.message}</Text>
                        </div>
                        {result.details && (
                          <div style={{ marginTop: 4 }}>
                            <Text type="secondary" style={{ fontSize: '12px' }}>{result.details}</Text>
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Empty description="尚未执行测试" />
                )}
              </div>
            </Col>
          </Row>

            <div style={{ marginTop: 16, padding: '12px', backgroundColor: '#f0f8ff', borderRadius: '6px', border: '1px solid #d6e4ff' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                <strong>测试提示：</strong>
                {ruleEditType === 'llm' ?
                  '自然语言规则测试将从选择的角色视角评估场景是否符合规则，请描述具体场景以获得准确结果。' :
                  '逻辑规则在安全沙盒中执行，需返回布尔值，有5秒超时限制。'
                }
              </Text>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ActionRules;