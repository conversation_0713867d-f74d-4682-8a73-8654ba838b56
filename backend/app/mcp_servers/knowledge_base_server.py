#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库MCP服务器

提供统一的知识库查询服务，支持：
- 基于智能体ID的知识库查询
- 自动推断角色并查询绑定的知识库
- 支持Dify、RAGFlow等多种知识库类型
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional

from mcp.server.fastmcp import FastMCP

# 设置日志
logger = logging.getLogger(__name__)

# 创建MCP服务器
mcp = FastMCP("knowledge-base")

# 知识库工具定义
KNOWLEDGE_BASE_TOOLS = [
    {
        "name": "query_knowledge",
        "description": "智能体查询知识库（系统自动根据agent_id推断role_id并查询绑定的知识库）",
        "inputSchema": {
            "type": "object",
            "properties": {
                "agent_id": {
                    "type": "integer",
                    "description": "智能体ID"
                },
                "query": {
                    "type": "string",
                    "description": "查询文本"
                },
                "max_results": {
                    "type": "integer",
                    "description": "最大返回结果数",
                    "default": 20
                },
                "query_params": {
                    "type": "object",
                    "description": "可选的查询参数，用于覆盖知识库默认配置",
                    "default": {}
                }
            },
            "required": ["agent_id", "query"]
        }
    }
]

def get_tools() -> List[Dict]:
    """获取工具列表（为了与MCPServerManager兼容）"""
    return KNOWLEDGE_BASE_TOOLS

@mcp.tool()
async def query_knowledge(
    agent_id: int,
    query: str,
    max_results: int = 20,
    query_params: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    智能体查询知识库
    
    Args:
        agent_id: 智能体ID
        query: 查询文本
        max_results: 最大返回结果数
        query_params: 可选的查询参数
        
    Returns:
        dict: 聚合查询结果
    """
    try:
        # 导入必要的模块
        from app.models import Agent, db
        from app.services.external_knowledge import ExternalKnowledgeService
        
        # 根据agent_id获取对应的role_id
        agent = Agent.query.get(agent_id)
        if not agent:
            return {
                "success": False,
                "error": f"智能体ID {agent_id} 不存在",
                "results": [],
                "total_count": 0,
                "query_time": 0,
                "queried_knowledge_bases": 0,
                "metadata": {
                    "agent_id": agent_id,
                    "query_text": query,
                    "error": "智能体不存在"
                }
            }
        
        # 获取角色信息
        role_id = agent.role_id
        if not role_id:
            return {
                "success": False,
                "error": f"智能体 {agent_id} 没有关联的角色",
                "results": [],
                "total_count": 0,
                "query_time": 0,
                "queried_knowledge_bases": 0,
                "metadata": {
                    "agent_id": agent_id,
                    "agent_name": agent.name,
                    "query_text": query,
                    "error": "智能体没有关联角色"
                }
            }
        
        # 调用现有的服务方法查询知识库
        if query_params is None:
            query_params = {}
            
        # 添加max_results到查询参数中
        query_params["max_results"] = max_results
        
        result = ExternalKnowledgeService.query_knowledge_for_role(
            role_id, query, query_params
        )
        
        # 增强返回结果的元数据
        if "metadata" not in result:
            result["metadata"] = {}
            
        result["metadata"].update({
            "agent_id": agent_id,
            "agent_name": agent.name,
            "role_id": role_id,
            "role_name": agent.role.name if agent.role else None
        })
        
        return result
        
    except Exception as e:
        logger.error(f"查询知识库失败: {e}")
        return {
            "success": False,
            "error": f"查询失败: {str(e)}",
            "results": [],
            "total_count": 0,
            "query_time": 0,
            "queried_knowledge_bases": 0,
            "metadata": {
                "agent_id": agent_id,
                "query_text": query,
                "error": str(e)
            }
        }

def handle_request(request_data: Dict) -> Dict:
    """
    处理MCP工具调用请求（为了与MCPServerManager兼容）

    Args:
        request_data: MCP请求数据

    Returns:
        Dict: MCP响应数据
    """
    try:
        # 从请求中提取工具名称和参数
        tool_name = request_data.get('name')
        tool_input = request_data.get('input', {})
        tool_use_id = request_data.get('id', 'unknown_id')

        if not tool_name:
            return {
                "type": "tool_result",
                "tool_use_id": tool_use_id,
                "is_error": True,
                "error": "缺少工具名称"
            }

        if tool_name == "query_knowledge":
            # 运行异步函数
            import asyncio
            import nest_asyncio

            # 允许嵌套事件循环
            nest_asyncio.apply()

            try:
                # 获取或创建事件循环
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                # 执行异步函数
                result = loop.run_until_complete(query_knowledge(**tool_input))

                return {
                    "type": "tool_result",
                    "tool_use_id": tool_use_id,
                    "content": json.dumps(result, ensure_ascii=False, indent=2)
                }

            except Exception as e:
                logger.error(f"执行查询知识库工具时出错: {str(e)}")
                return {
                    "type": "tool_result",
                    "tool_use_id": tool_use_id,
                    "is_error": True,
                    "error": f"执行工具失败: {str(e)}"
                }
        else:
            return {
                "type": "tool_result",
                "tool_use_id": tool_use_id,
                "is_error": True,
                "error": f"未找到工具: {tool_name}"
            }

    except Exception as e:
        logger.error(f"处理MCP请求失败: {str(e)}")
        return {
            "type": "tool_result",
            "tool_use_id": request_data.get('id', 'unknown_id'),
            "is_error": True,
            "error": f"处理请求失败: {str(e)}"
        }

if __name__ == "__main__":
    # 作为独立服务器运行
    import uvicorn
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("启动知识库MCP服务器...")
    
    # 运行服务器
    try:
        mcp.run()
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器运行失败: {e}")
        sys.exit(1)
