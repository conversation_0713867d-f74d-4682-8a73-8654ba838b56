import api from '../axios';

/**
 * Pinecone向量数据库API服务
 * 提供与Pinecone向量数据库相关的API函数
 */
const pineconeAPI = {
  /**
   * 测试Pinecone连接
   * @param {Object} config Pinecone配置
   * @returns {Promise<Object>} 连接测试结果
   */
  testConnection: async (config) => {
    try {
      const response = await api.post('/vector-db/test-connection', {
        provider: 'pinecone',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('测试Pinecone连接失败:', error);
      throw error;
    }
  },

  /**
   * 验证Pinecone配置
   * @param {Object} config Pinecone配置
   * @returns {Promise<Object>} 验证结果
   */
  validateConfig: async (config) => {
    try {
      const response = await api.post('/vector-db/validate-config', {
        provider: 'pinecone',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('验证Pinecone配置失败:', error);
      throw error;
    }
  },

  /**
   * 获取Pinecone索引列表
   * @param {Object} config Pinecone配置
   * @returns {Promise<Object>} 索引列表
   */
  listIndexes: async (config) => {
    try {
      const response = await api.post('/vector-db/list-indexes', {
        provider: 'pinecone',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('获取Pinecone索引列表失败:', error);
      throw error;
    }
  },

  /**
   * 创建Pinecone索引
   * @param {Object} config Pinecone配置
   * @param {string} indexName 索引名称
   * @param {Object} indexConfig 索引配置
   * @returns {Promise<Object>} 创建结果
   */
  createIndex: async (config, indexName, indexConfig) => {
    try {
      const response = await api.post('/vector-db/create-index', {
        provider: 'pinecone',
        config: config,
        index_name: indexName,
        index_config: indexConfig
      });
      return response.data;
    } catch (error) {
      console.error('创建Pinecone索引失败:', error);
      throw error;
    }
  },

  /**
   * 删除Pinecone索引
   * @param {Object} config Pinecone配置
   * @param {string} indexName 索引名称
   * @returns {Promise<Object>} 删除结果
   */
  deleteIndex: async (config, indexName) => {
    try {
      const response = await api.post('/vector-db/delete-index', {
        provider: 'pinecone',
        config: config,
        index_name: indexName
      });
      return response.data;
    } catch (error) {
      console.error('删除Pinecone索引失败:', error);
      throw error;
    }
  },

  /**
   * 获取索引信息
   * @param {Object} config Pinecone配置
   * @param {string} indexName 索引名称
   * @returns {Promise<Object>} 索引信息
   */
  getIndexInfo: async (config, indexName) => {
    try {
      const response = await api.post('/vector-db/index-info', {
        provider: 'pinecone',
        config: config,
        index_name: indexName
      });
      return response.data;
    } catch (error) {
      console.error('获取Pinecone索引信息失败:', error);
      throw error;
    }
  },

  /**
   * 向量搜索
   * @param {Object} config Pinecone配置
   * @param {string} indexName 索引名称
   * @param {Object} searchParams 搜索参数
   * @returns {Promise<Object>} 搜索结果
   */
  vectorSearch: async (config, indexName, searchParams) => {
    try {
      const response = await api.post('/vector-db/vector-search', {
        provider: 'pinecone',
        config: config,
        index_name: indexName,
        search_params: searchParams
      });
      return response.data;
    } catch (error) {
      console.error('Pinecone向量搜索失败:', error);
      throw error;
    }
  },

  /**
   * 插入向量数据
   * @param {Object} config Pinecone配置
   * @param {string} indexName 索引名称
   * @param {Array} vectors 向量数据
   * @returns {Promise<Object>} 插入结果
   */
  upsertVectors: async (config, indexName, vectors) => {
    try {
      const response = await api.post('/vector-db/upsert-vectors', {
        provider: 'pinecone',
        config: config,
        index_name: indexName,
        vectors: vectors
      });
      return response.data;
    } catch (error) {
      console.error('插入Pinecone向量数据失败:', error);
      throw error;
    }
  },

  /**
   * 删除向量数据
   * @param {Object} config Pinecone配置
   * @param {string} indexName 索引名称
   * @param {Array} ids 向量ID列表
   * @returns {Promise<Object>} 删除结果
   */
  deleteVectors: async (config, indexName, ids) => {
    try {
      const response = await api.post('/vector-db/delete-vectors', {
        provider: 'pinecone',
        config: config,
        index_name: indexName,
        ids: ids
      });
      return response.data;
    } catch (error) {
      console.error('删除Pinecone向量数据失败:', error);
      throw error;
    }
  }
};

export default pineconeAPI;
