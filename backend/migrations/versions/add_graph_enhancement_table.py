"""Add graph enhancement table

Revision ID: add_graph_enhancement
Revises: 
Create Date: 2025-01-25 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'add_graph_enhancement'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('graph_enhancements',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('enabled', sa.<PERSON>(), nullable=True),
    sa.Column('framework', sa.String(length=50), nullable=True),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('working_dir', sa.String(length=255), nullable=True),
    sa.Column('llm_config', sa.String(length=50), nullable=True),
    sa.Column('embedding_config', sa.String(length=50), nullable=True),
    sa.Column('default_query_mode', sa.String(length=50), nullable=True),
    sa.Column('top_k', sa.Integer(), nullable=True),
    sa.Column('chunk_top_k', sa.Integer(), nullable=True),
    sa.Column('max_entity_tokens', sa.Integer(), nullable=True),
    sa.Column('max_relation_tokens', sa.Integer(), nullable=True),
    sa.Column('framework_config', sa.JSON(), nullable=True),
    sa.Column('status', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('graph_enhancements')
    # ### end Alembic commands ###
