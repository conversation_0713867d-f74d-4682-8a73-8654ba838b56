import React, { useState, useCallback } from 'react';
import { Modal, Typography, Spin, Button, Dropdown, message } from 'antd';
import { DownloadOutlined, ExportOutlined } from '@ant-design/icons';
import { MarkdownRenderer } from '../../actiontask/components/ConversationExtraction';
import { workspaceAPI } from '../../../services/api/workspace';

const { Title } = Typography;

/**
 * 工作空间文件查看器
 * 用于查看和编辑工作空间文件内容
 */
const WorkspaceFileViewer = ({ visible, file, onClose, onSave }) => {
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [editMode, setEditMode] = useState(false);

  // 加载文件内容
  const loadFileContent = useCallback(async () => {
    if (!file) return;

    setLoading(true);
    try {
      const data = await workspaceAPI.getWorkspaceFileContent(file.file_path);
      setContent(data.content || '');
    } catch (error) {
      console.error('加载文件内容失败:', error);
      setContent('加载文件内容失败');
    } finally {
      setLoading(false);
    }
  }, [file]);

  // 当文件变化时加载内容
  React.useEffect(() => {
    if (visible && file) {
      loadFileContent();
      setEditMode(false);
    }
  }, [visible, file, loadFileContent]);

  const handleSave = async () => {
    if (!file) return;

    try {
      await workspaceAPI.updateWorkspaceFileContent(file.file_path, content);
      setEditMode(false);
      if (onSave) onSave();
    } catch (error) {
      console.error('保存文件失败:', error);
    }
  };

  // 根据文件扩展名获取MIME类型
  const getMimeType = (fileName) => {
    const extension = fileName.toLowerCase().split('.').pop();

    switch (extension) {
      case 'md':
      case 'markdown':
        return 'text/markdown;charset=utf-8';
      case 'txt':
      case 'log':
        return 'text/plain;charset=utf-8';
      case 'html':
      case 'htm':
        return 'text/html;charset=utf-8';
      case 'css':
        return 'text/css;charset=utf-8';
      case 'js':
        return 'application/javascript;charset=utf-8';
      case 'json':
        return 'application/json;charset=utf-8';
      case 'xml':
        return 'application/xml;charset=utf-8';
      case 'csv':
        return 'text/csv;charset=utf-8';
      case 'py':
        return 'text/x-python;charset=utf-8';
      case 'java':
        return 'text/x-java-source;charset=utf-8';
      case 'cpp':
      case 'c':
        return 'text/x-c;charset=utf-8';
      case 'php':
        return 'text/x-php;charset=utf-8';
      case 'sql':
        return 'text/x-sql;charset=utf-8';
      default:
        return 'text/plain;charset=utf-8';
    }
  };

  // 下载文件
  const handleDownload = async () => {
    if (!file) return;

    try {
      const data = await workspaceAPI.getWorkspaceFileContent(file.file_path);
      const mimeType = getMimeType(file.file_name);
      const blob = new Blob([data.content || ''], { type: mimeType });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.file_name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      message.success('文件下载成功');
    } catch (error) {
      console.error('下载文件失败:', error);
      message.error('下载文件失败');
    }
  };

  // 在新标签打开
  const handleOpenInNewTab = async () => {
    if (!file) return;

    try {
      const data = await workspaceAPI.getWorkspaceFileContent(file.file_path);
      const mimeType = getMimeType(file.file_name);
      const blob = new Blob([data.content || ''], { type: mimeType });
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');
      message.success('已在新标签页打开文件');
    } catch (error) {
      console.error('打开文件失败:', error);
      message.error('打开文件失败');
    }
  };

  return (
    <Modal
      title={
        <div>
          <Title level={4} style={{ margin: 0 }}>
            {file?.file_name || '文件查看器'}
          </Title>
          <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
            {file?.typeName} • {file?.file_path}
          </div>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={
        <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
          {editMode ? (
            <>
              <Button key="cancel" onClick={() => setEditMode(false)} style={{ marginRight: 8 }}>
                取消
              </Button>
              <Button key="save" type="primary" onClick={handleSave}>
                保存
              </Button>
            </>
          ) : (
            <>
              <Button key="edit" type="primary" onClick={() => setEditMode(true)} style={{ marginRight: 8 }}>
                编辑
              </Button>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'download',
                      label: '下载文件',
                      icon: <DownloadOutlined />,
                      onClick: handleDownload
                    },
                    {
                      key: 'openInNewTab',
                      label: '在新标签打开',
                      icon: <ExportOutlined />,
                      onClick: handleOpenInNewTab
                    }
                  ]
                }}
                trigger={['click']}
              >
                <Button style={{ marginRight: 8 }}>
                  操作
                </Button>
              </Dropdown>
              <Button key="close" onClick={onClose}>
                关闭
              </Button>
            </>
          )}
        </div>
      }
      styles={{
        body: { 
          maxHeight: '60vh', 
          overflow: 'auto',
          padding: '16px'
        }
      }}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
        </div>
      ) : editMode ? (
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          style={{
            width: '100%',
            height: '400px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            padding: '8px',
            fontFamily: 'Monaco, Consolas, monospace',
            fontSize: '14px',
            resize: 'vertical'
          }}
          placeholder="请输入文件内容..."
        />
      ) : (
        <div
          style={{
            border: '1px solid #f0f0f0',
            borderRadius: '4px',
            padding: '16px',
            backgroundColor: '#fafafa',
            minHeight: '200px'
          }}
        >
          {(() => {
            // 检查文件扩展名，只有.md文件才使用MarkdownRenderer
            const fileName = file?.file_name || '';
            const isMarkdownFile = fileName.toLowerCase().endsWith('.md') || fileName.toLowerCase().endsWith('.markdown');

            if (isMarkdownFile) {
              return <MarkdownRenderer content={content || '文件内容为空'} />;
            } else {
              // 非markdown文件显示为纯文本
              return (
                <pre style={{
                  fontFamily: 'Monaco, Consolas, monospace',
                  fontSize: '14px',
                  lineHeight: '1.5',
                  margin: 0,
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word'
                }}>
                  {content || '文件内容为空'}
                </pre>
              );
            }
          })()}
        </div>
      )}
    </Modal>
  );
};

export default WorkspaceFileViewer;
