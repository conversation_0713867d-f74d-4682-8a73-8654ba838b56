[{"key": "timezone", "value": "Asia/Shanghai", "value_type": "string", "description": "平台时区设置", "category": "system", "is_secret": false}, {"key": "streaming_enabled", "value": "true", "value_type": "boolean", "description": "是否启用流式输出", "category": "system", "is_secret": false}, {"key": "max_conversation_history_length", "value": "10", "value_type": "number", "description": "上下文历史消息长度", "category": "conversation", "is_secret": false}, {"key": "store_llm_error_messages", "value": "true", "value_type": "boolean", "description": "是否在会话记录中存储LLM错误消息", "category": "conversation", "is_secret": false}, {"key": "includeThinkingContentInContext", "value": "false", "value_type": "boolean", "description": "是否在上下文中包含思考内容", "category": "conversation", "is_secret": false}, {"key": "splitToolCallsInHistory", "value": "true", "value_type": "boolean", "description": "是否将工具调用拆分为独立的历史消息", "category": "conversation", "is_secret": false}, {"key": "use_builtin_vector_db", "value": "true", "value_type": "boolean", "description": "是否使用内置向量数据库（Mil<PERSON>s）", "category": "vector_database", "is_secret": false}, {"key": "vector_db_provider", "value": "<PERSON><PERSON><PERSON>", "value_type": "string", "description": "外部向量数据库提供商（aliyun, tidb, aws-opensearch, aws-bedrock, azure-cognitive-search, azure-cosmos-db, gcp-vertex-ai, gcp-firestore, pinecone, weaviate, qdrant, chroma, milvus, elasticsearch, custom）", "category": "vector_database", "is_secret": false}, {"key": "enableAssistantGeneration", "value": "true", "value_type": "boolean", "description": "是否启用辅助生成功能", "category": "assistant", "is_secret": false}, {"key": "assistant<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "default", "value_type": "string", "description": "辅助生成使用的模型", "category": "assistant", "is_secret": false}, {"key": "prompt_template_roleSystemPrompt", "value": "请根据以下角色信息生成一个专业的系统提示词：\n\n角色名称：{{name}}\n角色描述：{{description}}\n\n要求：\n1. 系统提示词应该清晰地定义角色的身份、专业领域和能力\n2. 包含角色的行为准则和回答风格\n3. 明确角色的职责范围和限制\n4. 使用专业、准确的语言\n5. 长度适中，既要详细又要简洁\n\n请直接返回系统提示词内容，不需要额外的解释。", "value_type": "string", "description": "角色系统提示词生成模板", "category": "assistant_generation", "is_secret": false}, {"key": "prompt_template_actionSpaceBackground", "value": "请根据以下多智能体行动空间信息生成专业的背景设定：\n\n行动空间名称：{{name}}\n行动空间描述：{{description}}\n\n要求：\n1. 背景设定应该详细描述行动空间的环境、场景和上下文\n2. 包含相关的历史背景、现状分析和发展趋势\n3. 明确行动空间的目标和意义\n4. 使用生动、具体的语言描述\n5. 为参与者提供充分的情境信息\n\n请直接返回背景设定内容，不需要额外的解释。", "value_type": "string", "description": "行动空间背景设定生成模板", "category": "assistant_generation", "is_secret": false}, {"key": "prompt_template_actionSpaceRules", "value": "请根据以下行动空间信息生成专业的基本规则：\n\n行动空间名称：{{name}}\n行动空间描述：{{description}}\n\n要求：\n1. 基本规则应该明确定义行动空间内的行为准则和约束条件\n2. 包含参与者的权限和责任范围\n3. 规定交互方式和协作机制\n4. 明确决策流程和执行标准\n5. 包含风险控制和异常处理规则\n6. 使用清晰、准确的语言表述\n7. 条理清晰，便于理解和执行\n\n请直接返回基本规则内容，不需要额外的解释。", "value_type": "string", "description": "行动空间基本规则生成模板", "category": "assistant_generation", "is_secret": false}, {"key": "prompt_template_actionTaskDescription", "value": "请根据以下信息生成详细的行动任务描述：\n\n任务名称：{{title}}\n行动空间名称：{{action_space_name}}\n行动空间描述：{{action_space_description}}\n参与角色：{{roles}}\n\n要求：\n1. 任务描述应该明确任务的目标和预期成果\n2. 详细说明任务的执行步骤和关键节点\n3. 明确各角色的职责分工和协作方式\n4. 包含任务的评估标准和成功指标\n5. 考虑可能的风险和应对措施\n6. 使用清晰、具体的语言描述\n7. 确保任务的可执行性和可衡量性\n\n请直接返回任务描述内容，不需要额外的解释。", "value_type": "string", "description": "行动任务描述生成模板", "category": "assistant_generation", "is_secret": false}, {"key": "http_connection_timeout", "value": "30", "value_type": "number", "description": "HTTP请求建立连接的超时时间（秒）", "category": "timeout", "is_secret": false}, {"key": "http_read_timeout", "value": "300", "value_type": "number", "description": "HTTP请求读取数据的超时时间（秒）", "category": "timeout", "is_secret": false}, {"key": "stream_socket_timeout", "value": "60", "value_type": "number", "description": "流式响应Socket读取的超时时间（秒）", "category": "timeout", "is_secret": false}, {"key": "default_model_timeout", "value": "60", "value_type": "number", "description": "模型配置的默认请求超时时间（秒）", "category": "timeout", "is_secret": false}]