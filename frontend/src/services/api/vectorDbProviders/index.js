/**
 * 向量数据库提供商API服务入口
 * 统一管理所有向量数据库提供商的API服务
 */

import tidbVectorAPI from './tidb';
import milvusAPI from './milvus';
import pineconeAPI from './pinecone';
import weaviateAPI from './weaviate';
import qdrantAPI from './qdrant';
import chromaAPI from './chroma';

// 向量数据库提供商映射
export const vectorDbProviders = {
  tidb: tidbVectorAPI,
  milvus: milvusAPI,
  pinecone: pineconeAPI,
  weaviate: weaviateAPI,
  qdrant: qdrantAPI,
  chroma: chromaAPI
};

// 支持的提供商列表
export const supportedProviders = [
  {
    id: 'tidb',
    name: 'TiDB Cloud',
    description: 'TiDB向量数据库服务',
    icon: 'database',
    status: 'stable'
  },
  {
    id: 'milvus',
    name: 'Mil<PERSON><PERSON>',
    description: '开源向量数据库',
    icon: 'cloud',
    status: 'stable'
  },
  {
    id: 'pinecone',
    name: 'Pinecone',
    description: '托管向量数据库服务',
    icon: 'cloud-server',
    status: 'beta'
  },
  {
    id: 'weaviate',
    name: 'Weaviate',
    description: '开源向量搜索引擎',
    icon: 'search',
    status: 'beta'
  },
  {
    id: 'qdrant',
    name: 'Qdrant',
    description: '向量相似度搜索引擎',
    icon: 'radar-chart',
    status: 'beta'
  },
  {
    id: 'chroma',
    name: 'Chroma',
    description: 'AI原生开源嵌入数据库',
    icon: 'palette',
    status: 'beta'
  }
];

/**
 * 获取指定提供商的API服务
 * @param {string} providerId 提供商ID
 * @returns {Object} API服务对象
 */
export const getProviderAPI = (providerId) => {
  const api = vectorDbProviders[providerId];
  if (!api) {
    throw new Error(`不支持的向量数据库提供商: ${providerId}`);
  }
  return api;
};

/**
 * 获取提供商信息
 * @param {string} providerId 提供商ID
 * @returns {Object} 提供商信息
 */
export const getProviderInfo = (providerId) => {
  return supportedProviders.find(provider => provider.id === providerId);
};

/**
 * 检查提供商是否支持
 * @param {string} providerId 提供商ID
 * @returns {boolean} 是否支持
 */
export const isProviderSupported = (providerId) => {
  return providerId in vectorDbProviders;
};

// 默认导出
const vectorDbProvidersAPI = {
  providers: vectorDbProviders,
  supportedProviders,
  getProviderAPI,
  getProviderInfo,
  isProviderSupported
};

export default vectorDbProvidersAPI;
