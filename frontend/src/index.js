import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import './index.css';
import App from './App';
import { startLicenseChecker } from './services/licenseChecker';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1677ff',
          borderRadius: 6,
          colorBgContainer: '#ffffff',
          colorBgLayout: '#f0f2f5',
          colorText: 'rgba(0, 0, 0, 0.85)',
          colorTextSecondary: 'rgba(0, 0, 0, 0.45)',
          colorBorder: '#f0f0f0',
          boxShadow: '0 1px 4px rgba(0, 0, 0, 0.08)',
        },
      }}
    >
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </ConfigProvider>
  </React.StrictMode>
);

// 启动许可证检查服务（仅作为备用机制，主要依赖后端中间件和axios拦截器）
// 可以通过设置 performInitialCheck: false 来禁用初始检查，减少不必要的请求
startLicenseChecker(false);