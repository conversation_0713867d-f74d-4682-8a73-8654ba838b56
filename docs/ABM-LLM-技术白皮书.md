# ABM-LLM 多智能体专家决策与执行系统技术白皮书

## 1. 执行摘要

### 1.1 项目概述与愿景

ABM-LLM 多智能体专家决策与执行系统是一个创新性的智能协作平台，融合了大语言模型技术与传统智能体建模(ABM)的优势，专注于解决复杂的多方协作决策问题。本系统通过构建虚拟专家团队，模拟真实世界中的专业人员协作过程，为企业决策、项目管理、研发创新等领域提供智能化解决方案。

本项目的核心愿景是打造一个能够真正理解业务场景、遵循专业规则、协调多方观点的智能决策平台，使复杂决策过程更加高效、透明和可靠。通过将大语言模型的语义理解能力与传统ABM的结构化建模优势相结合，本系统能够在保持高度灵活性的同时，确保决策过程的专业性和可控性。

### 1.2 核心技术亮点

ABM-LLM系统的核心技术亮点包括：

1. **双引擎规则系统**：创新性地结合自然语言规则引擎和逻辑规则引擎，使系统既能处理复杂的语义规则，又能执行精确的逻辑计算，实现了规则表达的灵活性与执行的精确性的完美结合。

2. **行动空间监督者机制**：引入监督者角色，实时监控智能体行为和规则执行，确保模拟过程符合预设条件，并能在必要时进行干预和调整。

3. **环境变量模板-实例架构**：采用变量定义与实例化分离的设计，支持公共环境变量和角色专属变量，使模拟环境更加灵活和真实。

4. **面向对话的交互设计**：基于自然语言对话的智能体交互模式，支持多种对话模式（顺序、小组、辩论、协作），使智能体之间的互动更加自然和高效。

5. **MCP插件系统**：通过Model-Control-Protocol标准，实现智能体与外部系统和工具的无缝交互，将决策直接转化为执行指令，扩展系统的应用范围。

### 1.3 应用场景与价值

本系统适用于多种复杂的多方协作决策场景：

- **企业决策与管理**：战略制定、团队协作、项目管理等场景，帮助企业提升决策质量和效率。
- **医疗会诊**：多专家诊断讨论和治疗方案制定，辅助医疗团队做出更全面的诊疗决策。
- **教育培训**：模拟课堂讨论、案例分析、辩论训练，为教育工作者提供智能化教学工具。
- **政策分析**：模拟政策影响、公共讨论和多方协商，辅助政策制定者评估政策效果。
- **研发创新**：产品开发讨论、创意碰撞、工艺改进，加速创新过程并提高成功率。
- **智能系统控制**：通过MCP插件控制智能家居、工业设备或软件系统，实现智能化控制。
- **自动化工作流**：智能体基于对话结果执行实际操作，如数据分析、报告生成等。

系统的核心价值在于：

1. **降低决策成本**：减少人力投入，加速决策过程，降低沟通成本。
2. **提升决策质量**：多角度分析问题，避免认知偏差，全面考虑各种因素。
3. **知识沉淀与传承**：将专家经验和决策逻辑沉淀为可复用的规则和模型。
4. **流程标准化**：将复杂决策过程规范化、标准化，提高一致性。
5. **创新能力增强**：打破思维局限，激发创新思路，促进跨领域知识融合。

### 1.4 技术创新与市场定位

相比传统ABM软件（如NetLogo、Mesa、AnyLogic）和现代LLM平台（如Dify、Langflow、RAGFlow），本系统具有显著的创新优势：

**对比传统ABM软件**：
- 支持自然语言规则和程序逻辑规则的双引擎架构，而非仅支持编程式规则
- 适合技术和非技术背景用户，降低了使用门槛
- 内置监督者角色，实现自动化监控和干预
- 以对话和沟通为中心，而非仅关注空间移动和状态变化
- 通过MCP插件实现对话+控制混合模式，增强了系统的应用范围

**对比现代LLM平台**：
- 内置多角色协作框架，支持复杂互动模式，而非仅面向单一智能体或简单对话
- 双引擎混合规则系统，结合语义规则与逻辑规则，实现更复杂的规则表达和执行
- 完整的环境变量架构，支持公共变量与角色变量，构建更真实的模拟环境
- 基于ABM的行动空间模型，精确控制智能体交互，实现更结构化的交互环境
- 内置监督者机制，能监控和干预模拟过程，确保过程符合预期

本系统在市场上定位为专业的多智能体协作决策平台，面向需要复杂多方协作决策的企业和组织，提供比通用AI平台更专业、更深入的决策支持能力，同时比传统ABM软件更易用、更灵活的用户体验。

## 3. 核心技术创新

### 3.1 双引擎规则系统

ABM-LLM系统的核心创新之一是双引擎规则系统，它结合了自然语言规则引擎和逻辑规则引擎的优势，为智能体行为提供了更加灵活和强大的约束机制。

#### 3.1.1 自然语言规则引擎

自然语言规则引擎允许用户使用自然语言描述复杂的业务规则，无需编程知识即可定义智能体行为约束。这种方式特别适合处理以下类型的规则：

- **模糊条件规则**：如"当市场情况不稳定时，投资决策应更加保守"
- **专业领域规则**：如"如果患者出现多器官功能衰竭症状，应立即启动多学科会诊流程"
- **伦理与价值判断**：如"在资源分配决策中，应优先考虑弱势群体的基本需求"

自然语言规则引擎的工作流程：
1. **规则解析**：将自然语言规则转换为结构化表示
2. **上下文匹配**：将当前情境与规则条件进行语义匹配
3. **规则评估**：使用大语言模型评估规则条件是否满足
4. **执行建议**：生成符合规则的行动建议

系统实现了基于角色视角的规则评估机制，同一规则可以从不同专业角色的视角进行解读和执行，使规则应用更加灵活和符合实际业务场景。

#### 3.1.2 逻辑规则引擎

逻辑规则引擎支持使用JavaScript或Python等编程语言定义精确的计算逻辑和确定性规则，适用于以下场景：

- **数值计算规则**：如"if (investment_amount > budget * 0.2) then require_additional_approval()"
- **阈值触发规则**：如"if (risk_score > 0.7) then reject_proposal()"
- **复杂条件组合**：如"if (market_volatility > 0.3 && cash_reserve < target * 0.5) then reduce_investment_exposure(0.3)"

逻辑规则引擎的执行流程：
1. **代码解析**：将规则代码解析为可执行结构
2. **安全沙箱**：在隔离环境中执行规则代码，防止安全风险
3. **变量绑定**：将环境变量和智能体变量绑定到执行上下文
4. **结果验证**：验证执行结果并应用到系统状态

系统支持多种规则解释器，包括JavaScript和Python，并实现了超时控制和错误处理机制，确保规则执行的安全性和可靠性。

#### 3.1.3 规则冲突解决机制

在复杂决策场景中，不同规则之间可能存在冲突。系统实现了多层次的规则冲突解决机制：

- **优先级机制**：通过RuleSetRule关联表中的priority字段定义规则优先级
- **规则分类**：将规则分为强制规则（必须遵守）和指导规则（可灵活应用）
- **上下文敏感评估**：根据当前情境动态调整规则应用权重
- **监督者干预**：监督者角色可以在规则冲突时进行人工干预

#### 3.1.4 规则评估与执行流程

规则评估与执行的完整流程如下：

1. **规则激活**：根据当前行动空间和任务状态，激活相关规则集
2. **前置条件检查**：评估规则的前置条件是否满足
3. **规则应用**：
   - 对于自然语言规则，使用大语言模型进行语义评估
   - 对于逻辑规则，在安全沙箱中执行代码逻辑
4. **结果整合**：将多个规则的评估结果整合，解决可能的冲突
5. **行动生成**：基于规则评估结果，生成符合规则的行动建议
6. **执行反馈**：记录规则执行结果，用于后续优化

系统还实现了规则测试功能，允许用户在应用规则前进行测试和验证，确保规则的正确性和有效性。

### 3.2 行动空间与ODD框架

#### 3.2.1 行动空间概念与实现

行动空间是ABM-LLM系统的核心概念，它定义了智能体交互的环境、规则和约束条件。每个行动空间代表一个特定的决策或协作场景，如"产品研发会议"、"医疗会诊"或"投资决策委员会"。

行动空间的核心组成部分：
- **基本信息**：名称、描述、创建时间等元数据
- **背景设定**：场景背景、目标和约束条件
- **规则集**：适用于该空间的规则集合
- **角色定义**：参与该空间的专业角色
- **环境变量**：影响决策的关键参数
- **监督者设置**：监控和干预机制

系统通过ActionSpace模型实现行动空间，并通过关联表建立与规则集、角色、标签等实体的多对多关系，形成灵活的行动空间配置机制。

#### 3.2.2 ODD框架（Overview, Design concepts, Details）

ABM-LLM系统采用ODD（Overview, Design concepts, Details）框架来规范行动空间的定义，这是一种源自传统智能体建模领域的标准化描述框架，有助于确保模型的完整性和可复现性。

ODD框架在系统中的实现：

**Overview（概述）**：
- **目的**：明确行动空间的目标和用途
- **实体**：定义参与的智能体类型和角色
- **状态变量**：确定关键的环境和智能体变量
- **尺度**：设定时间和空间范围

**Design concepts（设计概念）**：
- **基本原则**：指导智能体行为的核心原则
- **涌现特性**：期望从交互中产生的复杂现象
- **适应性**：智能体如何调整行为以适应环境变化
- **目标**：智能体追求的目标和优化方向
- **学习**：智能体如何从经验中学习
- **预测**：智能体如何预测未来状态
- **感知**：智能体如何感知环境和其他智能体
- **交互**：智能体之间的交互方式和规则
- **随机性**：系统中的随机因素
- **集体**：智能体如何形成集体行为

**Details（细节）**：
- **初始化**：系统初始状态的设置
- **输入数据**：系统使用的外部数据
- **子模型**：组成系统的子模型和算法

ODD框架作为JSON结构存储在行动空间的settings字段中，为行动空间提供了标准化的描述和配置方式。

#### 3.2.3 行动空间与规则集关联

行动空间可以关联多个规则集，每个规则集代表一种特定的交互模式或决策流程。系统通过ActionSpaceRuleSet关联表实现行动空间与规则集的多对多关系，使同一行动空间可以支持多种不同的交互模式。

典型的规则集类型包括：
- **顺序模式规则集**：智能体按顺序发言，适合正式会议场景
- **小组模式规则集**：智能体自由讨论，适合头脑风暴场景
- **辩论模式规则集**：智能体分为正反两方，适合决策评估场景
- **协作模式规则集**：智能体共同解决问题，适合团队协作场景

每个规则集包含多条规则，通过RuleSetRule关联表建立规则集与具体规则的多对多关系，并通过priority字段定义规则的优先级。

#### 3.2.4 行动空间监督者机制

行动空间监督者是一种特殊的智能体角色，负责监控行动空间中的交互过程，确保规则得到遵守，并在必要时进行干预。监督者机制是ABM-LLM系统的重要创新，它增强了系统的可控性和安全性。

监督者的主要职责：
- **规则执行监控**：检查智能体行为是否符合规则要求
- **冲突调解**：在智能体之间出现冲突时进行调解
- **进度管理**：控制讨论进度，确保按计划进行
- **总结与引导**：在适当时机总结讨论内容，引导讨论方向
- **异常处理**：处理系统中出现的异常情况

系统通过ActionSpaceObserver关联表实现行动空间与监督者角色的关联，监督者可以是预设的系统角色，也可以是用户自定义的专业角色。

### 3.3 多智能体协作模式

#### 3.3.1 顺序模式（Sequential）

顺序模式是一种结构化的交互模式，智能体按照预定顺序依次发言，适合正式会议、汇报等场景。

**实现机制**：
- 系统维护一个发言队列，按照预定顺序安排智能体发言
- 每个智能体完成发言后，系统自动通知下一个智能体
- 支持轮次控制，可设定多轮讨论
- 提供发言时间限制，确保讨论效率

**应用场景**：
- 项目进度汇报会议
- 学术研讨会
- 正式决策委员会
- 结构化面试

#### 3.3.2 小组模式（Panel）

小组模式允许智能体自由发言，模拟自然讨论过程，适合头脑风暴、创意讨论等场景。

**实现机制**：
- 智能体可以自主决定何时发言
- 系统根据讨论内容和智能体特性动态调整发言机会
- 支持打断和回应机制，使讨论更加自然
- 主持人角色可以引导讨论方向

**应用场景**：
- 创意头脑风暴
- 非正式讨论
- 团队协作会议
- 开放式问题探讨

#### 3.3.3 辩论模式（Debate）

辩论模式将智能体分为正反两方，进行结构化辩论，适合决策评估、方案比较等场景。

**实现机制**：
- 智能体被分配到正方或反方阵营
- 系统按照辩论流程（开篇陈述、反驳、交叉质询、总结）组织发言
- 支持裁判角色评估双方论点
- 提供辩论结果分析和总结

**应用场景**：
- 投资决策评估
- 产品方案比较
- 政策影响分析
- 风险评估讨论

#### 3.3.4 协作模式（Collaborative）

协作模式强调智能体之间的合作，共同解决问题，适合团队协作、共创等场景。

**实现机制**：
- 智能体共享目标和资源
- 系统鼓励互补性贡献和知识共享
- 支持任务分解和协同工作
- 提供协作进度跟踪和成果整合

**应用场景**：
- 产品设计协作
- 研发团队协作
- 多学科问题解决
- 共创工作坊

#### 3.3.5 自动讨论机制

ABM-LLM系统实现了自动讨论机制，使智能体能够在没有人类干预的情况下自主进行多轮讨论，这一功能大大提高了系统的自主性和效率。

**实现机制**：
- **轮次控制**：支持设定讨论轮数，每轮讨论包含所有智能体的发言
- **主题引导**：可设定讨论主题，引导智能体围绕特定问题展开讨论
- **上下文维护**：系统维护完整的讨论上下文，确保讨论的连贯性
- **总结生成**：讨论结束后自动生成讨论总结，提炼关键观点和结论
- **流式输出**：支持讨论过程的实时流式输出，使用户能够实时监控讨论进展

自动讨论功能通过app/services/conversation/auto_conversation.py模块实现，支持不同的讨论模式和参数配置，为用户提供了灵活的自动化讨论工具。

### 3.4 MCP工具集成

#### 3.4.1 Model-Control-Protocol概述

Model-Control-Protocol (MCP) 是一种标准化的协议，用于实现AI模型与外部工具和系统的交互。ABM-LLM系统通过MCP实现了智能体与外部世界的连接，使智能体能够执行实际操作，而不仅限于对话。

MCP的核心优势：
- **标准化接口**：提供统一的工具调用接口，简化集成过程
- **安全沙箱**：在受控环境中执行工具调用，确保系统安全
- **双向通信**：支持同步和异步的工具调用和结果返回
- **多种通信方式**：支持stdio和http两种通信方式，适应不同场景需求

系统使用官方的MCP SDK (mcp[cli]) 实现MCP服务器，并通过mcp_server_manager模块管理多个MCP服务器的生命周期。

#### 3.4.2 环境变量与智能体变量管理

ABM-LLM系统实现了完整的环境变量和智能体变量管理机制，作为智能体与系统环境之间的桥梁，提供安全、可控的变量访问机制。

**变量类型**：
- **任务环境变量**：特定于某个行动任务的公共变量，所有智能体可访问
- **智能体变量**：特定于某个智能体的内部状态变量，可设置为公开或私有

**MCP工具集**：
系统通过variables-server MCP服务器提供以下工具：
- `get_task_var`：获取任务环境变量的值
- `set_task_var`：设置任务环境变量的值
- `list_task_vars`：列出任务的所有环境变量
- `get_agent_var`：获取智能体变量的值
- `set_agent_var`：设置智能体变量的值
- `list_agent_vars`：列出智能体的所有变量

变量管理实现了类型转换、历史记录和访问控制等功能，确保变量操作的安全性和可追溯性。

#### 3.4.3 外部工具集成架构

ABM-LLM系统支持集成多种外部工具，扩展智能体的能力范围。系统通过mcp_config.json配置文件管理外部工具集成，支持以下类型的工具：

- **文件系统工具**：允许智能体读写文件，如filesystem服务器
- **记忆工具**：提供长期记忆能力，如memory服务器
- **网络工具**：支持网络访问和API调用，如fetch服务器
- **数据库工具**：连接向量数据库，如milvus服务器
- **可视化工具**：生成图表和可视化内容，如markmap服务器
- **专业领域工具**：如金融分析、医疗诊断等特定领域工具

外部工具通过统一的MCP接口集成到系统中，智能体可以通过标准化的工具调用语法使用这些工具。

#### 3.4.4 MCP服务器管理系统

系统实现了完整的MCP服务器管理功能，通过app/services/mcp_server_manager.py模块提供以下功能：

- **配置管理**：加载、编辑和保存MCP服务器配置
- **生命周期管理**：启动、停止和监控MCP服务器
- **状态监控**：实时监控服务器状态和健康状况
- **错误处理**：处理服务器启动和运行过程中的错误
- **自动清理**：应用退出时自动关闭所有服务器

MCP服务器管理系统支持两种类型的服务器：
- **内部服务器**：由系统直接管理的服务器，如variables-server
- **外部服务器**：独立运行的第三方服务器，如Playwright、SearXNG等

系统提供了RESTful API和前端界面，方便用户管理和配置MCP服务器。

### 3.5 环境变量模板-实例架构

#### 3.5.1 变量层次结构设计

ABM-LLM系统采用层次化的变量结构设计，实现了变量定义与实例化分离的模板-实例架构：

**变量层次结构**：
- **行动空间环境变量模板**：定义行动空间级别的变量模板
- **行动任务环境变量**：基于模板实例化的任务级公共变量
- **角色变量模板**：定义角色级别的变量模板
- **智能体变量**：基于角色模板实例化的智能体私有变量

这种层次结构使变量管理更加灵活和可复用，同时保持了变量的一致性和规范性。

#### 3.5.2 任务环境变量实现

任务环境变量是特定于某个行动任务的公共变量，所有参与该任务的智能体都可以访问。系统通过ActionTaskEnvironmentVariable模型实现任务环境变量，支持以下功能：

- **变量类型**：支持文本、数值、布尔、JSON等多种数据类型
- **访问控制**：可设置变量的读写权限
- **历史记录**：记录变量值的变更历史
- **单位标注**：支持为数值变量添加单位信息
- **标签定制**：支持自定义变量标签，提高可读性

任务环境变量通过MCP工具（get_task_var、set_task_var、list_task_vars）提供给智能体访问，确保变量操作的安全性和可控性。

#### 3.5.3 智能体变量实现

智能体变量是特定于某个智能体的内部状态变量，可以设置为公开（其他智能体可见）或私有（仅自己可见）。系统通过AgentVariable模型实现智能体变量，支持以下功能：

- **变量类型**：支持文本、数值、布尔、JSON等多种数据类型
- **可见性控制**：通过is_public字段控制变量的可见性
- **历史记录**：记录变量值的变更历史
- **类型转换**：自动进行字符串与实际类型之间的转换
- **默认标签**：根据变量名自动生成可读的标签

智能体变量通过MCP工具（get_agent_var、set_agent_var、list_agent_vars）提供给智能体访问，使智能体能够维护自己的内部状态。

#### 3.5.4 变量历史记录与追踪

系统为环境变量和智能体变量实现了完整的历史记录功能，记录变量值的每次变更，包括变更时间和变更后的值。这一功能使系统能够：

- **追踪变量演变**：观察变量值如何随时间变化
- **回溯决策过程**：理解变量变化与决策之间的关系
- **审计与分析**：分析变量变化模式，发现潜在问题
- **可视化展示**：支持变量历史的图表展示

变量历史记录存储在变量模型的history字段中，采用JSON数组格式，每个记录包含timestamp和value两个字段。

前端界面提供了变量历史的可视化展示，使用户能够直观地了解变量的变化过程，增强了系统的可解释性和透明度。
