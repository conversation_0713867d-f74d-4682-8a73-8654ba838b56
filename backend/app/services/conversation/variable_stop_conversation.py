"""
变量停止会话模块

提供基于变量条件停止的自主行动功能，智能体会持续进行轮次行动，直到满足预设的变量停止条件为止

函数与关键变量说明:
---------------------------------------

* start_variable_stop_conversation - 启动变量停止会话
  - task_id: 行动任务ID
  - conversation_id: 会话ID
  - config: 配置参数，包含停止条件、主题等
  - streaming: 是否使用流式输出
  - app_context: 应用上下文，用于流式处理
  - result_queue: 结果队列，用于流式处理

* _start_variable_stop_impl - 变量停止会话实现方法
  - task_id: 行动任务ID
  - conversation_id: 会话ID
  - config: 配置参数
  - streaming: 是否使用流式输出
  - result_queue: 结果队列，用于流式处理

* stop_variable_stop_conversation - 停止变量停止会话
  - task_id: 行动任务ID
  - conversation_id: 会话ID

* _check_stop_conditions - 检查停止条件
  - task_id: 行动任务ID
  - conversation_id: 会话ID
  - conditions: 停止条件列表
  - logic: 条件逻辑（and/or）

* _evaluate_condition - 评估单个条件
  - condition: 条件配置
  - task_id: 行动任务ID
"""
import json
import logging
import queue
import traceback
import time
import threading
import jwt
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from flask import request, current_app

from app.models import db, Conversation, ConversationAgent, Message, Agent, Role, AutonomousTask, AutonomousTaskExecution, User, ActionTaskEnvironmentVariable, AgentVariable
from app.utils.datetime_utils import get_current_time_with_timezone
from app.services.conversation.message_formater import format_agent_error_done, format_all_agents_done, serialize_message
# 避免循环导入，在需要时动态导入
# from app.services.conversation_service import ConversationService

logger = logging.getLogger(__name__)

def get_current_user():
    """获取当前登录用户信息"""
    try:
        # 从请求头中获取JWT令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return None
        
        token = auth_header.split(' ')[1]
        
        # 解码JWT令牌
        payload = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
        user_id = payload.get('user_id')
        
        if not user_id:
            return None
        
        # 查询用户信息
        user = User.query.get(user_id)
        if not user:
            return None
        
        return {
            'id': user.id,
            'username': user.username,
            'email': user.email
        }

    except jwt.ExpiredSignatureError:
        logger.warning("JWT令牌已过期")
        return None
    except jwt.InvalidTokenError:
        logger.warning("无效的JWT令牌")
        return None
    except Exception as e:
        logger.error(f"获取当前用户失败: {str(e)}")
        return None

def start_variable_stop_conversation(task_id: int, conversation_id: int, config: Dict[str, Any],
                                   streaming: bool = False, app_context = None,
                                   result_queue: queue.Queue = None) -> Dict:
    """
    启动变量停止会话

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID
        config: 配置参数，包含停止条件、主题等
        streaming: 是否使用流式输出，默认为False
        app_context: 应用上下文，用于流式处理
        result_queue: 结果队列，用于流式处理

    Returns:
        Dict: 会话结果信息
    """
    # 如果是流式处理模式，需要应用上下文
    if streaming and app_context:
        with app_context:
            return _start_variable_stop_impl(
                task_id, conversation_id, config, streaming, result_queue
            )
    else:
        # 非流式处理不需要应用上下文
        return _start_variable_stop_impl(
            task_id, conversation_id, config, streaming, result_queue
        )

# 全局变量，用于跟踪活动的变量停止任务
_active_variable_stop_tasks = {}


def stop_variable_stop_conversation(task_id: int, conversation_id: int) -> bool:
    """
    停止正在进行的变量停止任务

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID

    Returns:
        bool: 是否成功停止任务
    """
    task_key = f"{task_id}:{conversation_id}"

    # 记录开始停止任务
    logger.info(f"尝试停止变量停止任务: {task_key}")

    if task_key in _active_variable_stop_tasks:
        try:
            # 获取任务信息
            task_info = _active_variable_stop_tasks[task_key]

            # 如果有结果队列，发送取消信号
            if 'result_queue' in task_info and task_info['result_queue']:
                result_queue = task_info['result_queue']

                # 发送取消信号
                result_queue.put(json.dumps({
                    'connectionStatus': 'done',
                    'message': '变量停止任务被用户手动停止'
                }))

                # 结束流
                result_queue.put(None)

            # 从活动任务中移除
            del _active_variable_stop_tasks[task_key]

            # 更新自主任务状态为停止
            try:
                # 查找对应的自主任务记录
                autonomous_task = AutonomousTask.query.filter_by(
                    conversation_id=conversation_id,
                    status='active'
                ).order_by(AutonomousTask.created_at.desc()).first()

                if autonomous_task:
                    autonomous_task.status = 'stopped'

                    # 更新执行记录状态
                    latest_execution = AutonomousTaskExecution.query.filter_by(
                        autonomous_task_id=autonomous_task.id,
                        status='running'
                    ).order_by(AutonomousTaskExecution.created_at.desc()).first()

                    if latest_execution:
                        latest_execution.status = 'stopped'
                        latest_execution.end_time = get_current_time_with_timezone()
                        latest_execution.result = {
                            'status': 'stopped',
                            'message': '任务被用户手动停止'
                        }

                    db.session.commit()
                    logger.info(f"已更新自主任务状态为stopped: {autonomous_task.id}")
            except Exception as e:
                logger.error(f"更新自主任务状态失败: {str(e)}")

            # 创建系统消息，记录任务被停止
            try:
                end_msg = Message(
                    conversation_id=conversation_id,
                    action_task_id=task_id,
                    content="提示：变量停止任务被用户手动停止。",
                    role="system",
                    created_at=get_current_time_with_timezone()
                )
                db.session.add(end_msg)
                db.session.commit()

                logger.info(f"已添加任务停止系统消息: {end_msg.id}")
            except Exception as e:
                logger.error(f"添加任务停止系统消息失败: {str(e)}")

            logger.info(f"成功停止变量停止任务: {task_key}")
            return True
        except Exception as e:
            logger.error(f"停止变量停止任务出错: {str(e)}")
            return False
    else:
        logger.info(f"未找到活动的变量停止任务: {task_key}")
        return False


def _start_variable_stop_impl(task_id: int, conversation_id: int, config: Dict[str, Any],
                            streaming: bool = False, result_queue: queue.Queue = None) -> Dict:
    """变量停止会话实现方法"""
    # 构建任务键
    task_key = f"{task_id}:{conversation_id}"

    # 注册变量停止任务
    _active_variable_stop_tasks[task_key] = {
        'task_id': task_id,
        'conversation_id': conversation_id,
        'config': config,
        'streaming': streaming,
        'result_queue': result_queue,
        'start_time': get_current_time_with_timezone(),
        'round_count': 0
    }

    logger.info(f"已注册变量停止任务: {task_key}")
    try:
        # 流式模式初始化
        if streaming and result_queue:
            # 在队列中添加连接成功事件
            result_queue.put(json.dumps({
                'connectionStatus': 'connected'
            }))

        # 检查会话是否存在且属于该行动任务
        conversation = Conversation.query.get(conversation_id)
        if not conversation or conversation.action_task_id != task_id:
            error_msg = f"会话未找到或不属于行动任务: {task_id}"
            logger.error(error_msg)

            if streaming and result_queue:
                result_queue.put(json.dumps({
                    'connectionStatus': 'error',
                    'error': error_msg
                }))
                result_queue.put(None)
                return {'status': 'error', 'message': error_msg}
            else:
                raise ValueError(error_msg)

        # 获取会话中的智能体
        conv_agents = ConversationAgent.query.filter_by(conversation_id=conversation_id).all()
        if not conv_agents:
            error_msg = f"会话中没有智能体: {conversation_id}"
            logger.error(error_msg)

            if streaming and result_queue:
                result_queue.put(json.dumps({
                    'connectionStatus': 'error',
                    'error': error_msg
                }))
                result_queue.put(None)
                return {'status': 'error', 'message': error_msg}
            else:
                raise ValueError(error_msg)

        # 过滤掉监督者智能体
        task_agents = []
        for conv_agent in conv_agents:
            agent = Agent.query.get(conv_agent.agent_id)
            if agent and not agent.is_observer:
                task_agents.append(conv_agent)

        if not task_agents:
            error_msg = f"会话中没有可用的任务智能体（监督者智能体不参与变量停止任务）: {conversation_id}"
            logger.error(error_msg)

            if streaming and result_queue:
                result_queue.put(json.dumps({
                    'connectionStatus': 'error',
                    'error': error_msg
                }))
                result_queue.put(None)
                return {'status': 'error', 'message': error_msg}
            else:
                raise ValueError(error_msg)

        # 创建自主任务记录
        autonomous_task = AutonomousTask(
            conversation_id=conversation_id,
            type='conditional_stop',
            status='active',
            config=config
        )
        db.session.add(autonomous_task)
        db.session.flush()  # 获取ID但不提交事务

        # 获取当前登录用户信息（后台任务中无法获取请求上下文，使用默认值）
        try:
            current_user = get_current_user()
            trigger_source = current_user['username'] if current_user else 'system'
        except Exception as e:
            logger.debug(f"后台任务中无法获取用户上下文，使用默认触发源: {str(e)}")
            trigger_source = 'system'

        # 创建自主任务执行记录
        autonomous_execution = AutonomousTaskExecution(
            autonomous_task_id=autonomous_task.id,
            execution_type='manual',  # 手动触发
            trigger_source=trigger_source,
            trigger_data={
                'task_id': task_id,
                'conversation_id': conversation_id,
                'user_action': 'start_variable_stop_conversation',
                'user_id': current_user['id'] if current_user else None,
                'username': current_user['username'] if current_user else None
            },
            status='running'
        )
        db.session.add(autonomous_execution)
        db.session.commit()

        logger.info(f"已创建变量停止任务记录: autonomous_task_id={autonomous_task.id}, execution_id={autonomous_execution.id}")

        # 创建开始任务的系统消息
        topic = config.get('topic', '请基于各自角色和知识，持续进行行动，直到满足停止条件')

        system_msg = Message(
            conversation_id=conversation_id,
            action_task_id=task_id,
            content=f"提示：现在开始变量停止模式的自主行动，智能体将持续轮流行动，直到满足停止条件。\n任务主题：{topic}",
            role="system",
            created_at=get_current_time_with_timezone()
        )
        db.session.add(system_msg)
        db.session.commit()

        # 创建流式模式的回调函数（需要在计划阶段之前定义）
        from app.services.conversation.callback_utils import create_standard_sse_callback
        sse_callback = create_standard_sse_callback(streaming, result_queue)

        # 流式模式发送系统消息
        if streaming and result_queue:
            from app.services.conversation.message_formater import format_system_message
            system_msg_formatted = format_system_message(
                message_id=str(system_msg.id),
                content=system_msg.content,
                created_at=system_msg.created_at.isoformat() if system_msg.created_at else None
            )
            result_queue.put(json.dumps(system_msg_formatted))

        # 如果启用计划功能，先进行计划阶段
        enable_planning = config.get('enable_planning', False)
        planner_agent_id = config.get('planner_agent_id')

        if enable_planning:
            # 确定计划智能体
            planner_agent = None
            if planner_agent_id:
                planner_agent = Agent.query.get(planner_agent_id)

            if not planner_agent and task_agents:
                # 使用第一个智能体作为计划者
                planner_agent = Agent.query.get(task_agents[0].agent_id)

            if planner_agent:
                logger.info(f"开始计划阶段，计划智能体: {planner_agent.name}")

                # 创建计划提示词
                planning_prompt = f"<div style='color: #A0A0A0;'>@{planner_agent.name} 请为即将开始的变量停止模式自主行动制定详细计划。请分析任务主题和停止条件，制定行动策略，并将完整的计划写入共享记忆中，以便其他智能体参考。\n任务主题：{topic}</div>\n"

                if streaming:
                    # 流式模式通知用户计划阶段开始
                    agent_role = Role.query.get(planner_agent.role_id) if hasattr(planner_agent, 'role_id') and planner_agent.role_id else None
                    role_name = agent_role.name if agent_role else "智能助手"
                    sse_callback({
                        "type": "agentInfo",
                        "turnPrompt": f"由智能体 {planner_agent.name}({role_name}) 制定计划",
                        "agentId": str(planner_agent.id),
                        "agentName": f"{planner_agent.name}({role_name})",
                        "round": 0,  # 计划阶段在正式轮次之前
                        "totalRounds": 999,  # 变量停止模式没有固定轮数
                        "responseOrder": 1,
                        "totalAgents": 1,
                        "isPlanning": True
                    })

                    # 流式模式处理计划
                    from app.services.conversation_service import ConversationService
                    response_completed, error_info = ConversationService._process_single_agent_response(
                        task_id=task_id,
                        conversation_id=conversation_id,
                        human_message=None,  # 虚拟消息
                        agent_id=planner_agent.id,
                        content=planning_prompt,
                        sse_callback=sse_callback,
                        result_queue=None
                    )

                    if not response_completed:
                        logger.warning(f"计划阶段失败: {error_info}")
                else:
                    # 非流式模式处理计划
                    from app.services.conversation_service import ConversationService
                    planning_virtual_message = {
                        'content': planning_prompt,
                        'target_agent_id': planner_agent.id
                    }

                    _, planning_message = ConversationService.add_message_to_conversation(
                        conversation_id,
                        planning_virtual_message,
                        is_virtual=True
                    )

                    if planning_message:
                        logger.info(f"计划已由{planner_agent.name}完成")
                    else:
                        logger.warning("计划生成失败")

                logger.info("计划阶段完成，开始正式行动")

        # 开始变量停止循环
        return _execute_variable_stop_loop(task_key, task_id, conversation_id, task_agents, config, streaming, result_queue, autonomous_execution, sse_callback)

    except Exception as e:
        # 清理任务注册
        if task_key in _active_variable_stop_tasks:
            del _active_variable_stop_tasks[task_key]

        error_msg = f"启动变量停止任务失败: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        if streaming and result_queue:
            result_queue.put(json.dumps({
                'connectionStatus': 'error',
                'error': error_msg
            }))
            result_queue.put(None)
            return {'status': 'error', 'message': error_msg}
        else:
            raise


def _execute_variable_stop_loop(task_key: str, task_id: int, conversation_id: int,
                               task_agents: List, config: Dict[str, Any],
                               streaming: bool, result_queue: queue.Queue,
                               autonomous_execution: AutonomousTaskExecution, sse_callback) -> Dict:
    """执行变量停止循环"""
    try:
        # 获取配置参数
        topic = config.get('topic', '请基于各自角色和知识，持续进行行动，直到满足停止条件')
        stop_conditions = config.get('stopConditions', [])
        condition_logic = config.get('conditionLogic', 'and')
        max_runtime = config.get('maxRuntime', 0)  # 最大运行时间（分钟）

        # 获取智能体信息映射
        agent_map = {}
        for conv_agent in task_agents:
            agent = Agent.query.get(conv_agent.agent_id)
            if agent:
                role = Role.query.get(agent.role_id) if agent.role_id else None
                agent_map[conv_agent.agent_id] = {
                    'name': agent.name,
                    'role_name': role.name if role else None
                }

        start_time = get_current_time_with_timezone()
        round_count = 0

        # 主循环：持续进行轮次行动，直到满足停止条件
        while task_key in _active_variable_stop_tasks:
            round_count += 1
            logger.info(f"开始第 {round_count} 轮行动，任务: {task_key}")

            # 更新任务状态
            if task_key in _active_variable_stop_tasks:
                _active_variable_stop_tasks[task_key]['round_count'] = round_count

            # 再次检查任务是否仍然活跃（防止在智能体响应过程中任务被停止）
            if task_key not in _active_variable_stop_tasks:
                logger.info(f"任务已被停止，退出循环: {task_key}")
                break

            # 每个智能体轮流发言
            for i, conv_agent in enumerate(task_agents):
                # 检查任务是否已被停止
                if task_key not in _active_variable_stop_tasks:
                    logger.info(f"变量停止任务已被停止，退出智能体循环: {task_key}")
                    return {'status': 'stopped', 'message': '变量停止任务被用户手动停止'}

                # 在每个智能体发言前检查时间限制
                if max_runtime > 0:
                    elapsed_time = (get_current_time_with_timezone() - start_time).total_seconds() / 60
                    if elapsed_time >= max_runtime:
                        logger.info(f"达到最大运行时间限制 {max_runtime} 分钟，停止任务: {task_key}")

                        # 发送时间限制达到的消息
                        timeout_msg = Message(
                            conversation_id=conversation_id,
                            action_task_id=task_id,
                            content=f"提示：达到最大运行时间限制 {max_runtime} 分钟，变量停止任务结束。共进行了 {round_count} 轮行动，第 {i+1} 个智能体发言前停止。",
                            role="system",
                            created_at=get_current_time_with_timezone()
                        )
                        db.session.add(timeout_msg)
                        db.session.commit()

                        if streaming and result_queue:
                            from app.services.conversation.message_formater import format_system_message
                            timeout_msg_formatted = format_system_message(
                                message_id=str(timeout_msg.id),
                                content=timeout_msg.content,
                                created_at=timeout_msg.created_at.isoformat() if timeout_msg.created_at else None
                            )
                            result_queue.put(json.dumps(timeout_msg_formatted))

                        # 直接跳出循环，结束任务
                        return _finalize_variable_stop_task(task_key, task_id, conversation_id, autonomous_execution, round_count, streaming, result_queue)

                # 在每个智能体发言前检查停止条件
                if stop_conditions:
                    logger.info(f"第 {round_count} 轮第 {i+1} 个智能体发言前，检查停止条件")
                    conditions_met = _check_stop_conditions(task_id, conversation_id, stop_conditions, condition_logic)
                    if conditions_met:
                        logger.info(f"停止条件已满足，结束变量停止任务: {task_key}")

                        # 发送停止条件满足的消息
                        stop_msg = Message(
                            conversation_id=conversation_id,
                            action_task_id=task_id,
                            content=f"提示：停止条件已满足，变量停止任务结束。共进行了 {round_count} 轮行动，第 {i+1} 个智能体发言前停止。",
                            role="system",
                            created_at=get_current_time_with_timezone()
                        )
                        db.session.add(stop_msg)
                        db.session.commit()

                        if streaming and result_queue:
                            from app.services.conversation.message_formater import format_system_message
                            stop_msg_formatted = format_system_message(
                                message_id=str(stop_msg.id),
                                content=stop_msg.content,
                                created_at=stop_msg.created_at.isoformat() if stop_msg.created_at else None
                            )
                            result_queue.put(json.dumps(stop_msg_formatted))

                        # 直接跳出循环，结束任务
                        return _finalize_variable_stop_task(task_key, task_id, conversation_id, autonomous_execution, round_count, streaming, result_queue)

                agent_id = conv_agent.agent_id
                agent_info = agent_map.get(agent_id, {'name': f'智能体{agent_id}'})

                # 构建发言提示
                prompt = f"<div style='color: #A0A0A0;'>@{agent_info.get('name')} 请你基于之前的信息，继续执行你的任务。你是该任务中的第{round_count}轮行动的第{i+1}个行动者。有任何进展请更新共享记忆。\n任务主题：{topic}</div>\n"

                # 如果是第一轮第一个发言者，提示不同
                if round_count == 1 and i == 0:
                    prompt = f"<div style='color: #A0A0A0;'>@{agent_info.get('name')} 你是任务的第一个行动者，请就任务主题开始你的任务。请首先在共享记忆中指定计划。\n任务主题：{topic}</div>\n"

                # 流式模式通知用户当前发言智能体信息，在通知横幅中显示
                if streaming and result_queue:
                    agent = Agent.query.get(agent_id)
                    if agent:
                        agent_role = Role.query.get(agent.role_id) if hasattr(agent, 'role_id') and agent.role_id else None
                        role_name = agent_role.name if agent_role else "智能助手"
                        sse_callback({
                            "type": "agentInfo",
                            "turnPrompt": f"轮到智能体 {agent.name}({role_name}) 行动",
                            "agentId": str(agent_id),
                            "agentName": f"{agent.name}({role_name})",
                            "round": round_count,
                            "totalRounds": 999,  # 变量停止模式没有固定轮数
                            "responseOrder": i + 1,
                            "totalAgents": len(task_agents)
                        })

                try:
                    # 动态导入避免循环导入
                    from app.services.conversation_service import ConversationService

                    # 调用智能体服务处理单个智能体响应
                    # 传递None作为human_message，表示这是虚拟消息（自主任务中的消息）
                    response_completed, error_msg = ConversationService._process_single_agent_response(
                        task_id=task_id,
                        conversation_id=conversation_id,
                        human_message=None,  # 虚拟消息
                        agent_id=agent_id,
                        content=prompt,
                        sse_callback=sse_callback,
                        result_queue=None,  # 不在这里结束流
                        response_order=i + 1
                    )

                    # 构建响应对象
                    if response_completed:
                        response = {
                            'status': 'success',
                            'message': {'id': f'virtual-{agent_id}-{round_count}-{i+1}', 'content': '智能体响应完成'}
                        }
                    else:
                        response = {
                            'status': 'error',
                            'message': error_msg or '智能体响应失败'
                        }

                    if streaming and result_queue:
                        # 流式模式下，检查响应是否成功
                        if response_completed:
                            logger.info(f"智能体 {agent_id} 完成发言")
                        else:
                            logger.warning(f"智能体 {agent_id} 发言失败: {error_msg}")
                    else:
                        # 非流式模式
                        if response and response.get('status') == 'success':
                            logger.info(f"智能体 {agent_id} 完成发言")
                        else:
                            logger.warning(f"智能体 {agent_id} 发言失败: {response}")

                except Exception as e:
                    error_msg = f"智能体 {agent_id} 发言时出错: {str(e)}"
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())

                    if streaming and result_queue:
                        # 获取智能体信息用于错误格式化
                        agent_info = agent_map.get(agent_id, {'name': f'智能体{agent_id}', 'role_name': '未知角色'})
                        result_queue.put(json.dumps(format_agent_error_done(
                            agent_id=str(agent_id),
                            agent_name=agent_info['name'],
                            role_name=agent_info.get('role_name', '未知角色'),
                            error_content=error_msg
                        )))

                    # 继续下一个智能体，不中断整个流程
                    continue

            # 一轮结束后，再次检查任务是否仍然活跃
            if task_key not in _active_variable_stop_tasks:
                logger.info(f"任务已被停止，退出循环: {task_key}")
                break

            # 轮次完成后触发监督者检查
            try:
                from app.services.supervisor_event_manager import supervisor_event_manager
                supervisor_event_manager.on_round_completed(
                    conversation_id=conversation_id,
                    round_number=round_count
                )
            except Exception as e:
                logger.error(f"触发轮次完成监督者检查时出错: {str(e)}")
                # 不影响主流程，继续执行

            # 注意：停止条件检测已移至每个智能体发言前进行
            logger.info(f"第 {round_count} 轮行动完成，继续下一轮")

            if not stop_conditions:
                logger.warning(f"未配置停止条件，任务将无限期运行: {task_key}")

        # 任务完成，清理和更新状态
        return _finalize_variable_stop_task(task_key, task_id, conversation_id, autonomous_execution, round_count, streaming, result_queue)

    except Exception as e:
        error_msg = f"执行变量停止循环时出错: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        # 清理任务注册
        if task_key in _active_variable_stop_tasks:
            del _active_variable_stop_tasks[task_key]

        if streaming and result_queue:
            result_queue.put(json.dumps({
                'connectionStatus': 'error',
                'error': error_msg
            }))
            result_queue.put(None)

        return {'status': 'error', 'message': error_msg}


def _check_stop_conditions(task_id: int, conversation_id: int, conditions: List[Dict], logic: str) -> bool:
    """
    检查停止条件是否满足

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID
        conditions: 停止条件列表
        logic: 条件逻辑（and/or）

    Returns:
        bool: 是否满足停止条件
    """
    if not conditions:
        return False

    results = []

    for condition in conditions:
        try:
            result = _evaluate_condition(condition, task_id)
            results.append(result)
            logger.debug(f"条件评估结果: {condition} -> {result}")
        except Exception as e:
            logger.error(f"评估条件时出错: {condition}, 错误: {str(e)}")
            results.append(False)  # 出错时视为条件不满足

    # 根据逻辑运算符组合结果
    if logic == 'or':
        return any(results)
    else:  # 默认为 'and'
        return all(results)


def _evaluate_condition(condition: Dict[str, Any], task_id: int) -> bool:
    """
    评估单个停止条件

    Args:
        condition: 条件配置，包含 type, variable, operator, value
        task_id: 行动任务ID

    Returns:
        bool: 条件是否满足
    """
    var_type = condition.get('type')
    var_name = condition.get('variable')
    operator = condition.get('operator')
    threshold_value = condition.get('value')

    if not all([var_type, var_name, operator, threshold_value]):
        logger.warning(f"条件配置不完整: {condition}")
        return False

    # 获取变量当前值
    current_value = None

    if var_type == 'environment':
        # 环境变量 - 使用新的数据库会话以获取最新值
        from app import db as app_db

        # 创建新的数据库会话来避免事务隔离问题
        with app_db.engine.connect() as connection:
            result = connection.execute(
                app_db.text("SELECT value FROM action_task_environment_variables WHERE action_task_id = :task_id AND name = :name"),
                {"task_id": task_id, "name": var_name}
            ).fetchone()

            if result:
                current_value = result[0]
                logger.debug(f"获取内部环境变量 {var_name} 当前值: {current_value}")
            else:
                logger.warning(f"未找到内部环境变量: {var_name} (task_id: {task_id})")
                return False
    elif var_type == 'external':
        # 外部环境变量 - 使用新的数据库会话以获取最新值
        from app import db as app_db

        # 创建新的数据库会话来避免事务隔离问题
        with app_db.engine.connect() as connection:
            result = connection.execute(
                app_db.text("SELECT current_value FROM external_environment_variables WHERE name = :name"),
                {"name": var_name}
            ).fetchone()

            if result:
                current_value = result[0]
                logger.debug(f"获取外部环境变量 {var_name} 当前值: {current_value}")
            else:
                logger.warning(f"未找到外部环境变量: {var_name}")
                return False
    elif var_type == 'agent':
        # 智能体变量，格式为 "agent_id.variable_name"
        if '.' in var_name:
            agent_id_str, agent_var_name = var_name.split('.', 1)
            try:
                agent_id = int(agent_id_str)

                # 使用新的数据库会话来避免事务隔离问题
                from app import db as app_db

                with app_db.engine.connect() as connection:
                    result = connection.execute(
                        app_db.text("SELECT value FROM agent_variables WHERE agent_id = :agent_id AND name = :name"),
                        {"agent_id": agent_id, "name": agent_var_name}
                    ).fetchone()

                    if result:
                        current_value = result[0]
                        logger.debug(f"获取智能体变量 {var_name} 当前值: {current_value}")
                    else:
                        logger.warning(f"未找到智能体变量: {var_name}")
                        return False

            except ValueError:
                logger.error(f"无效的智能体变量格式: {var_name}")
                return False

    if current_value is None:
        logger.warning(f"未找到变量: {var_type}.{var_name}")
        return False

    # 尝试将值转换为数字进行比较
    try:
        current_num = float(current_value)
        threshold_num = float(threshold_value)

        logger.debug(f"数字比较: {current_num} {operator} {threshold_num}")

        # 执行数字比较
        result = False
        if operator == '>':
            result = current_num > threshold_num
        elif operator == '>=':
            result = current_num >= threshold_num
        elif operator == '=':
            result = current_num == threshold_num
        elif operator == '<=':
            result = current_num <= threshold_num
        elif operator == '<':
            result = current_num < threshold_num
        else:
            logger.warning(f"不支持的运算符: {operator}")
            return False

        logger.debug(f"比较结果: {result}")
        return result

    except ValueError:
        # 如果无法转换为数字，进行字符串比较
        current_str = str(current_value)
        threshold_str = str(threshold_value)

        if operator == '=':
            return current_str == threshold_str
        elif operator == '!=':
            return current_str != threshold_str
        else:
            logger.warning(f"字符串类型不支持运算符: {operator}")
            return False


def _finalize_variable_stop_task(task_key: str, task_id: int, conversation_id: int,
                                autonomous_execution: AutonomousTaskExecution, round_count: int,
                                streaming: bool, result_queue: queue.Queue) -> Dict:
    """
    完成变量停止任务的清理工作

    Args:
        task_key: 任务键
        task_id: 行动任务ID
        conversation_id: 会话ID
        autonomous_execution: 自主任务执行记录
        round_count: 执行的轮数
        streaming: 是否流式处理
        result_queue: 结果队列

    Returns:
        Dict: 任务完成结果
    """
    try:
        # 清理任务注册
        if task_key in _active_variable_stop_tasks:
            del _active_variable_stop_tasks[task_key]

        # 更新自主任务执行记录
        autonomous_execution.status = 'completed'
        autonomous_execution.end_time = get_current_time_with_timezone()
        autonomous_execution.result = {
            'rounds_completed': round_count,
            'completion_reason': 'conditions_met'
        }

        # 更新自主任务状态
        autonomous_task = AutonomousTask.query.get(autonomous_execution.autonomous_task_id)
        if autonomous_task:
            autonomous_task.status = 'completed'

        db.session.commit()

        logger.info(f"变量停止任务完成: {task_key}, 共执行 {round_count} 轮")

        # 发送完成事件（不再发送重复的系统消息，因为在检测到停止条件时已经发送过了）
        if streaming and result_queue:
            result_queue.put(json.dumps(format_all_agents_done()))
            result_queue.put(None)  # 结束流式传输

        return {
            'status': 'completed',
            'message': f'变量停止任务完成，共执行 {round_count} 轮',
            'rounds_completed': round_count
        }

    except Exception as e:
        error_msg = f"完成变量停止任务时出错: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        if streaming and result_queue:
            result_queue.put(json.dumps({
                'connectionStatus': 'error',
                'error': error_msg
            }))
            result_queue.put(None)

        return {'status': 'error', 'message': error_msg}


def start_variable_stop_conversation_stream(app_context, task_id: int, conversation_id: int,
                                          config: Dict[str, Any], result_queue: queue.Queue = None):
    """启动变量停止会话（流式版本）"""
    return start_variable_stop_conversation(
        task_id=task_id,
        conversation_id=conversation_id,
        config=config,
        streaming=True,
        app_context=app_context,
        result_queue=result_queue
    )
