"""
内部知识库API路由

处理与内部知识库相关的所有API请求，包括：
- 知识库管理（增删改查）
- 文件管理（上传、删除、查看）
- 向量化处理
- 语义搜索
"""

import os
import json
import uuid
import re
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from sqlalchemy.exc import IntegrityError

from app.models import Knowledge, RoleKnowledge, SystemSetting, Role, db
from app.services.vector_db.embedding_service import embedding_service

# 创建Blueprint
knowledge_bp = Blueprint('knowledge_api', __name__)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'doc', 'docx', 'md', 'json'}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def safe_filename_with_unicode(filename):
    """
    生成支持中文的安全文件名
    保留中文字符、英文字母、数字、下划线、连字符和点号
    移除或替换其他可能有害的字符
    """
    if not filename:
        return 'unnamed_file'

    # 移除路径分隔符和其他危险字符
    dangerous_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|', '\0']
    safe_name = filename

    for char in dangerous_chars:
        safe_name = safe_name.replace(char, '_')

    # 移除开头和结尾的空格和点号
    safe_name = safe_name.strip(' .')

    # 如果文件名为空或只包含点号，使用默认名称
    if not safe_name or safe_name == '.' or safe_name == '..':
        safe_name = 'unnamed_file'

    # 限制文件名长度（考虑到文件系统限制，通常为255字节）
    # 对于UTF-8编码，一个中文字符通常占3字节
    max_length = 200  # 保守估计，留出扩展名和时间戳的空间
    if len(safe_name.encode('utf-8')) > max_length:
        # 截断文件名，但保留扩展名
        name, ext = os.path.splitext(safe_name)
        while len(name.encode('utf-8')) + len(ext.encode('utf-8')) > max_length and len(name) > 1:
            name = name[:-1]
        safe_name = name + ext

    return safe_name

def get_knowledge_base_path(knowledge_id):
    """获取知识库文件存储路径"""
    base_path = os.path.join(current_app.root_path, '..', 'knowledgebase')
    return os.path.join(base_path, str(knowledge_id))

def ensure_knowledge_base_dirs(knowledge_id):
    """确保知识库目录存在"""
    kb_path = get_knowledge_base_path(knowledge_id)
    dirs = ['files', 'processed']
    
    for dir_name in dirs:
        dir_path = os.path.join(kb_path, dir_name)
        os.makedirs(dir_path, exist_ok=True)
    
    return kb_path

# ==================== 知识库管理接口 ====================

@knowledge_bp.route('/knowledges', methods=['GET'])
def get_knowledges():
    """获取所有内部知识库"""
    try:
        knowledges = Knowledge.query.all()
        
        result = []
        for kb in knowledges:
            # 获取知识库统计信息
            kb_path = get_knowledge_base_path(kb.id)
            files_path = os.path.join(kb_path, 'files')
            
            document_count = 0
            total_size = 0
            
            if os.path.exists(files_path):
                for filename in os.listdir(files_path):
                    file_path = os.path.join(files_path, filename)
                    if os.path.isfile(file_path):
                        document_count += 1
                        total_size += os.path.getsize(file_path)
            
            # 格式化文件大小
            if total_size < 1024:
                size_str = f"{total_size} B"
            elif total_size < 1024 * 1024:
                size_str = f"{total_size / 1024:.1f} KB"
            else:
                size_str = f"{total_size / (1024 * 1024):.1f} MB"
            
            result.append({
                'id': kb.id,
                'name': kb.name,
                'description': kb.description,
                'type': kb.type,
                'document_count': document_count,
                'size': size_str,
                'created_at': kb.created_at.isoformat() if kb.created_at else None,
                'updated_at': kb.updated_at.isoformat() if kb.updated_at else None
            })
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        current_app.logger.error(f"获取知识库列表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取知识库列表失败: {str(e)}'
        }), 500

@knowledge_bp.route('/knowledges', methods=['POST'])
def create_knowledge():
    """创建新的内部知识库"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查名称是否重复
        existing = Knowledge.query.filter_by(name=data['name']).first()
        if existing:
            return jsonify({
                'success': False,
                'message': '知识库名称已存在'
            }), 400
        
        # 创建知识库
        knowledge = Knowledge(
            name=data['name'],
            description=data.get('description', ''),
            type='knowledge',  # 统一设置为knowledge类型
            content='',
            settings=data.get('settings', {})
        )
        
        db.session.add(knowledge)
        db.session.commit()
        
        # 创建文件存储目录
        ensure_knowledge_base_dirs(knowledge.id)
        
        # 创建元数据文件
        metadata = {
            'id': knowledge.id,
            'name': knowledge.name,
            'description': knowledge.description,
            'type': knowledge.type,
            'created_at': knowledge.created_at.isoformat(),
            'vector_config': {
                'embedding_model': None,
                'vector_db_provider': SystemSetting.get('vector_db_provider', 'aliyun'),
                'use_builtin_vector_db': SystemSetting.get('use_builtin_vector_db', True)
            },
            'statistics': {
                'document_count': 0,
                'total_chunks': 0,
                'total_vectors': 0
            }
        }
        
        metadata_path = os.path.join(get_knowledge_base_path(knowledge.id), 'metadata.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        return jsonify({
            'success': True,
            'message': '知识库创建成功',
            'data': {
                'id': knowledge.id,
                'name': knowledge.name,
                'description': knowledge.description,
                'type': knowledge.type,
                'created_at': knowledge.created_at.isoformat()
            }
        })
        
    except IntegrityError:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '知识库名称已存在'
        }), 400
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'创建知识库失败: {str(e)}'
        }), 500

@knowledge_bp.route('/knowledges/<int:knowledge_id>', methods=['GET'])
def get_knowledge(knowledge_id):
    """获取知识库详情"""
    try:
        knowledge = Knowledge.query.get_or_404(knowledge_id)
        
        # 读取元数据
        metadata_path = os.path.join(get_knowledge_base_path(knowledge_id), 'metadata.json')
        metadata = {}
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
        
        return jsonify({
            'success': True,
            'data': {
                'id': knowledge.id,
                'name': knowledge.name,
                'description': knowledge.description,
                'type': knowledge.type,
                'settings': knowledge.settings,
                'created_at': knowledge.created_at.isoformat() if knowledge.created_at else None,
                'updated_at': knowledge.updated_at.isoformat() if knowledge.updated_at else None,
                'metadata': metadata
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取知识库详情失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取知识库详情失败: {str(e)}'
        }), 500

@knowledge_bp.route('/knowledges/<int:knowledge_id>', methods=['PUT'])
def update_knowledge(knowledge_id):
    """更新知识库信息"""
    try:
        knowledge = Knowledge.query.get_or_404(knowledge_id)
        data = request.get_json()
        
        # 更新字段
        if 'name' in data:
            # 检查名称是否重复（排除自己）
            existing = Knowledge.query.filter(
                Knowledge.name == data['name'],
                Knowledge.id != knowledge_id
            ).first()
            if existing:
                return jsonify({
                    'success': False,
                    'message': '知识库名称已存在'
                }), 400
            knowledge.name = data['name']
        
        if 'description' in data:
            knowledge.description = data['description']

        if 'settings' in data:
            knowledge.settings = data['settings']
        
        db.session.commit()
        
        # 更新元数据文件
        metadata_path = os.path.join(get_knowledge_base_path(knowledge_id), 'metadata.json')
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            metadata.update({
                'name': knowledge.name,
                'description': knowledge.description,
                'type': knowledge.type,
                'updated_at': datetime.utcnow().isoformat()
            })
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        return jsonify({
            'success': True,
            'message': '知识库更新成功',
            'data': {
                'id': knowledge.id,
                'name': knowledge.name,
                'description': knowledge.description,
                'type': knowledge.type,
                'updated_at': knowledge.updated_at.isoformat()
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新知识库失败: {str(e)}'
        }), 500

@knowledge_bp.route('/knowledges/<int:knowledge_id>', methods=['DELETE'])
def delete_knowledge(knowledge_id):
    """删除知识库"""
    try:
        knowledge = Knowledge.query.get_or_404(knowledge_id)

        # 删除关联的角色知识库关系
        RoleKnowledge.query.filter_by(knowledge_id=knowledge_id).delete()

        # 删除知识库记录
        db.session.delete(knowledge)
        db.session.commit()

        # 删除文件存储目录
        import shutil
        kb_path = get_knowledge_base_path(knowledge_id)
        if os.path.exists(kb_path):
            shutil.rmtree(kb_path)

        return jsonify({
            'success': True,
            'message': '知识库删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除知识库失败: {str(e)}'
        }), 500

# ==================== 文件管理接口 ====================

@knowledge_bp.route('/knowledges/files', methods=['GET'])
def get_all_knowledge_files():
    """获取所有知识库中的文件列表"""
    try:
        knowledges = Knowledge.query.all()
        all_files = []

        for knowledge in knowledges:
            files_path = os.path.join(get_knowledge_base_path(knowledge.id), 'files')

            if os.path.exists(files_path):
                for filename in os.listdir(files_path):
                    file_path = os.path.join(files_path, filename)
                    if os.path.isfile(file_path):
                        stat = os.stat(file_path)

                        # 获取文件类型
                        ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

                        # 格式化文件大小
                        size = stat.st_size
                        if size < 1024:
                            size_str = f"{size} B"
                        elif size < 1024 * 1024:
                            size_str = f"{size / 1024:.1f} KB"
                        else:
                            size_str = f"{size / (1024 * 1024):.1f} MB"

                        all_files.append({
                            'id': f"{knowledge.id}_{filename}",  # 使用知识库ID和文件名组合作为唯一ID
                            'name': filename,
                            'type': ext,
                            'size': size_str,
                            'upload_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                            'status': 'completed',
                            'chunks': 0,  # TODO: 从处理结果中获取
                            'tokens': 0,  # TODO: 从处理结果中获取
                            'knowledge_id': knowledge.id,
                            'knowledge_name': knowledge.name
                        })

        return jsonify({
            'success': True,
            'data': all_files
        })

    except Exception as e:
        current_app.logger.error(f"获取所有文件列表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取所有文件列表失败: {str(e)}'
        }), 500

@knowledge_bp.route('/knowledges/<int:knowledge_id>/files', methods=['GET'])
def get_knowledge_files(knowledge_id):
    """获取知识库中的文件列表"""
    try:
        knowledge = Knowledge.query.get_or_404(knowledge_id)

        files_path = os.path.join(get_knowledge_base_path(knowledge_id), 'files')
        files = []

        if os.path.exists(files_path):
            for filename in os.listdir(files_path):
                file_path = os.path.join(files_path, filename)
                if os.path.isfile(file_path):
                    stat = os.stat(file_path)

                    # 获取文件类型
                    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

                    # 格式化文件大小
                    size = stat.st_size
                    if size < 1024:
                        size_str = f"{size} B"
                    elif size < 1024 * 1024:
                        size_str = f"{size / 1024:.1f} KB"
                    else:
                        size_str = f"{size / (1024 * 1024):.1f} MB"

                    files.append({
                        'id': filename,  # 使用文件名作为ID
                        'name': filename,
                        'type': ext,
                        'size': size_str,
                        'upload_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'status': 'completed',
                        'chunks': 0,  # TODO: 从处理结果中获取
                        'tokens': 0   # TODO: 从处理结果中获取
                    })

        return jsonify({
            'success': True,
            'data': files
        })

    except Exception as e:
        current_app.logger.error(f"获取文件列表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取文件列表失败: {str(e)}'
        }), 500

@knowledge_bp.route('/knowledges/<int:knowledge_id>/files', methods=['POST'])
def upload_knowledge_file(knowledge_id):
    """上传文件到知识库"""
    try:
        knowledge = Knowledge.query.get_or_404(knowledge_id)

        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '未找到上传文件'
            }), 400

        file = request.files['file']

        # 检查文件名
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '未选择文件'
            }), 400

        # 检查文件类型
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'message': f'不支持的文件类型，支持的类型: {", ".join(ALLOWED_EXTENSIONS)}'
            }), 400

        # 确保目录存在
        kb_path = ensure_knowledge_base_dirs(knowledge_id)
        files_path = os.path.join(kb_path, 'files')

        # 生成支持中文的安全文件名
        filename = safe_filename_with_unicode(file.filename)

        # 如果文件已存在，添加时间戳
        if os.path.exists(os.path.join(files_path, filename)):
            name, ext = os.path.splitext(filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{name}_{timestamp}{ext}"

        # 保存文件
        file_path = os.path.join(files_path, filename)
        file.save(file_path)

        # 触发向量化处理
        try:
            from app.services.document_processor import knowledge_processor
            success, result = knowledge_processor.process_file_for_knowledge_base(
                knowledge_id, file_path
            )

            if success:
                # 保存处理结果
                processed_path = os.path.join(kb_path, 'processed', f"{filename}.json")
                with open(processed_path, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)

                processing_info = result.get('processing_summary', {})
            else:
                current_app.logger.warning(f"文件向量化处理失败: {result.get('error', '未知错误')}")
                processing_info = {'error': result.get('error', '处理失败')}

        except Exception as e:
            current_app.logger.error(f"文件向量化处理异常: {e}")
            processing_info = {'error': f'处理异常: {str(e)}'}

        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'data': {
                'filename': filename,
                'size': os.path.getsize(file_path),
                'upload_time': datetime.now().isoformat(),
                'processing_info': processing_info
            }
        })

    except Exception as e:
        current_app.logger.error(f"文件上传失败: {e}")
        return jsonify({
            'success': False,
            'message': f'文件上传失败: {str(e)}'
        }), 500

@knowledge_bp.route('/knowledges/<int:knowledge_id>/files/<filename>', methods=['DELETE'])
def delete_knowledge_file(knowledge_id, filename):
    """删除知识库中的文件"""
    try:
        knowledge = Knowledge.query.get_or_404(knowledge_id)

        # 尝试直接使用文件名（支持中文）
        files_dir = os.path.join(get_knowledge_base_path(knowledge_id), 'files')
        file_path = os.path.join(files_dir, filename)

        # 如果文件不存在，尝试使用secure_filename处理后的文件名（向后兼容）
        if not os.path.exists(file_path):
            safe_filename = secure_filename(filename)
            file_path = os.path.join(files_dir, safe_filename)

        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '文件不存在'
            }), 404

        # 删除文件
        os.remove(file_path)

        # TODO: 这里应该删除对应的向量数据

        return jsonify({
            'success': True,
            'message': '文件删除成功'
        })

    except Exception as e:
        current_app.logger.error(f"删除文件失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除文件失败: {str(e)}'
        }), 500

@knowledge_bp.route('/knowledges/<int:knowledge_id>/files/<filename>/content', methods=['GET'])
def get_file_content(knowledge_id, filename):
    """获取文件内容（用于预览）"""
    try:
        knowledge = Knowledge.query.get_or_404(knowledge_id)

        # 尝试直接使用文件名（支持中文）
        files_dir = os.path.join(get_knowledge_base_path(knowledge_id), 'files')
        file_path = os.path.join(files_dir, filename)

        # 如果文件不存在，尝试使用secure_filename处理后的文件名（向后兼容）
        if not os.path.exists(file_path):
            safe_filename = secure_filename(filename)
            file_path = os.path.join(files_dir, safe_filename)

        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '文件不存在'
            }), 404

        # 根据文件类型读取内容
        ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

        try:
            if ext in ['txt', 'md', 'json']:
                # 文本文件直接读取
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            else:
                # 其他文件类型暂时不支持预览
                content = f"文件类型 .{ext} 暂不支持预览"

            return jsonify({
                'success': True,
                'data': {
                    'filename': filename,
                    'content': content,
                    'type': ext
                }
            })

        except UnicodeDecodeError:
            return jsonify({
                'success': False,
                'message': '文件编码不支持或文件已损坏'
            }), 400

    except Exception as e:
        current_app.logger.error(f"获取文件内容失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取文件内容失败: {str(e)}'
        }), 500

@knowledge_bp.route('/knowledges/<int:knowledge_id>/search', methods=['POST'])
def search_knowledge(knowledge_id):
    """搜索知识库内容"""
    try:
        knowledge = Knowledge.query.get_or_404(knowledge_id)
        data = request.get_json()

        query = data.get('query', '')
        if not query:
            return jsonify({
                'success': False,
                'message': '查询内容不能为空'
            }), 400

        # 使用向量数据库进行语义搜索
        from app.services.vector_db_service import get_vector_db_service
        vector_db_service = get_vector_db_service()
        kb_name = f"knowledge_{knowledge_id}"
        top_k = data.get('top_k', 5)

        if vector_db_service.is_available():
            success, search_results, search_info = vector_db_service.search(
                kb_name, query, top_k
            )

            if success:
                # 格式化搜索结果
                formatted_results = []
                for result in search_results:
                    formatted_results.append({
                        'id': result.get('id', ''),
                        'content': result.get('text', ''),
                        'score': result.get('score', 0.0),
                        'source': result.get('metadata', {}).get('file_name', ''),
                        'metadata': result.get('metadata', {})
                    })

                return jsonify({
                    'success': True,
                    'data': {
                        'query': query,
                        'results': formatted_results,
                        'total_count': len(formatted_results),
                        'search_info': search_info
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f'搜索失败: {search_results}'
                }), 500
        else:
            # 向量数据库不可用，返回提示
            return jsonify({
                'success': False,
                'message': '向量搜索服务不可用，请检查向量数据库配置'
            }), 503

    except Exception as e:
        current_app.logger.error(f"搜索知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'搜索知识库失败: {str(e)}'
        }), 500

# ==================== 向量化处理接口 ====================

@knowledge_bp.route('/knowledges/<int:knowledge_id>/vectorize', methods=['POST'])
def vectorize_knowledge(knowledge_id):
    """对知识库进行向量化处理"""
    try:
        knowledge = Knowledge.query.get_or_404(knowledge_id)

        # 获取知识库文件目录
        kb_path = get_knowledge_base_path(knowledge_id)
        files_path = os.path.join(kb_path, 'files')

        if not os.path.exists(files_path):
            return jsonify({
                'success': False,
                'message': '知识库文件目录不存在'
            }), 404

        # 处理所有文件
        from app.services.document_processor import knowledge_processor
        processed_files = []
        failed_files = []

        for filename in os.listdir(files_path):
            file_path = os.path.join(files_path, filename)
            if os.path.isfile(file_path):
                try:
                    success, result = knowledge_processor.process_file_for_knowledge_base(
                        knowledge_id, file_path
                    )

                    if success:
                        # 保存处理结果
                        processed_path = os.path.join(kb_path, 'processed', f"{filename}.json")
                        with open(processed_path, 'w', encoding='utf-8') as f:
                            json.dump(result, f, ensure_ascii=False, indent=2)

                        processed_files.append({
                            'filename': filename,
                            'chunks': result.get('processing_summary', {}).get('total_chunks', 0),
                            'chars': result.get('processing_summary', {}).get('total_chars', 0)
                        })
                    else:
                        failed_files.append({
                            'filename': filename,
                            'error': result.get('error', '处理失败')
                        })

                except Exception as e:
                    failed_files.append({
                        'filename': filename,
                        'error': f'处理异常: {str(e)}'
                    })

        return jsonify({
            'success': True,
            'message': f'向量化处理完成，成功处理 {len(processed_files)} 个文件',
            'data': {
                'processed_files': processed_files,
                'failed_files': failed_files,
                'total_files': len(processed_files) + len(failed_files),
                'success_count': len(processed_files),
                'failed_count': len(failed_files)
            }
        })

    except Exception as e:
        current_app.logger.error(f"向量化处理失败: {e}")
        return jsonify({
            'success': False,
            'message': f'向量化处理失败: {str(e)}'
        }), 500

# ==================== 角色知识库绑定接口 ====================

@knowledge_bp.route('/roles/<int:role_id>/knowledges', methods=['GET'])
def get_role_knowledges(role_id):
    """获取角色绑定的内部知识库"""
    try:
        # 验证角色是否存在
        role = Role.query.get_or_404(role_id)

        # 查询角色绑定的内部知识库
        query = db.session.query(
            RoleKnowledge, Knowledge
        ).join(
            Knowledge,
            RoleKnowledge.knowledge_id == Knowledge.id
        ).filter(RoleKnowledge.role_id == role_id)

        results = query.all()

        data = []
        for role_knowledge, knowledge in results:
            data.append({
                'id': knowledge.id,
                'name': knowledge.name,
                'description': knowledge.description,
                'type': knowledge.type,
                'created_at': knowledge.created_at.isoformat() if knowledge.created_at else None,
                'updated_at': knowledge.updated_at.isoformat() if knowledge.updated_at else None,
                'binding_created_at': role_knowledge.created_at.isoformat() if role_knowledge.created_at else None
            })

        return jsonify({
            'success': True,
            'data': data,
            'total': len(data)
        })

    except Exception as e:
        current_app.logger.error(f"获取角色内部知识库绑定失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取绑定失败: {str(e)}'
        }), 500

@knowledge_bp.route('/roles/<int:role_id>/knowledges/<int:knowledge_id>', methods=['POST'])
def bind_role_knowledge(role_id, knowledge_id):
    """为角色绑定内部知识库"""
    try:
        current_app.logger.info(f"开始绑定内部知识库: role_id={role_id}, knowledge_id={knowledge_id}")

        # 验证角色和知识库是否存在
        role = Role.query.get_or_404(role_id)
        knowledge = Knowledge.query.get_or_404(knowledge_id)

        current_app.logger.info(f"角色和知识库验证成功: role={role.name}, knowledge={knowledge.name}")

        # 检查是否已经绑定
        existing = RoleKnowledge.query.filter_by(
            role_id=role_id,
            knowledge_id=knowledge_id
        ).first()

        if existing:
            current_app.logger.warning(f"角色已绑定此内部知识库: role_id={role_id}, knowledge_id={knowledge_id}")
            return jsonify({
                'success': False,
                'message': '该角色已绑定此内部知识库'
            }), 400

        # 创建绑定关系
        binding = RoleKnowledge(
            role_id=role_id,
            knowledge_id=knowledge_id
        )

        db.session.add(binding)
        db.session.commit()

        current_app.logger.info(f"内部知识库绑定成功: binding_id={binding.id}")

        return jsonify({
            'success': True,
            'message': '内部知识库绑定成功',
            'data': {
                'id': binding.id,
                'role_id': binding.role_id,
                'knowledge_id': binding.knowledge_id,
                'created_at': binding.created_at.isoformat()
            }
        })

    except IntegrityError:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '该角色已绑定此内部知识库'
        }), 400
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"绑定角色内部知识库失败: {e}")
        return jsonify({
            'success': False,
            'message': f'绑定失败: {str(e)}'
        }), 500

@knowledge_bp.route('/roles/<int:role_id>/knowledges/<int:knowledge_id>', methods=['DELETE'])
def unbind_role_knowledge(role_id, knowledge_id):
    """解除角色内部知识库绑定"""
    try:
        # 查找绑定关系
        binding = RoleKnowledge.query.filter_by(
            role_id=role_id,
            knowledge_id=knowledge_id
        ).first()

        if not binding:
            return jsonify({
                'success': False,
                'message': '绑定关系不存在'
            }), 404

        db.session.delete(binding)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '内部知识库绑定解除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"解除角色内部知识库绑定失败: {e}")
        return jsonify({
            'success': False,
            'message': f'解除绑定失败: {str(e)}'
        }), 500
