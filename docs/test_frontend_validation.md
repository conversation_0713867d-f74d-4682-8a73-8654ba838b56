# 前端验证功能测试指南

## 测试目标

验证前端在启动变量停止任务前是否正确检查了停止条件的设置。

## 测试场景

### 场景1：没有可用变量时
**预期行为**：
- 变量停止模式的Radio选项应该被禁用
- 显示提示信息："(需要环境变量或智能体变量)"
- 无法选择变量停止模式

**测试步骤**：
1. 创建一个新的行动任务
2. 不添加任何环境变量或智能体变量
3. 点击"自主行动"按钮
4. 查看"变量停止"选项是否被禁用

### 场景2：有可用变量但未设置停止条件
**预期行为**：
- 可以选择变量停止模式
- 点击确认时显示错误消息："变量停止模式必须设置至少一个完整的停止条件"
- 阻止任务启动

**测试步骤**：
1. 创建一个行动任务并添加至少一个环境变量
2. 点击"自主行动"按钮
3. 选择"变量停止"模式
4. 不添加任何停止条件，直接点击"确定"
5. 应该看到错误提示

### 场景3：设置了不完整的停止条件
**预期行为**：
- 点击确认时显示错误消息："变量停止模式必须设置至少一个完整的停止条件"
- 阻止任务启动

**测试步骤**：
1. 创建一个行动任务并添加环境变量
2. 选择"变量停止"模式
3. 添加停止条件但只填写部分字段（如只选择变量类型，不选择变量名）
4. 点击"确定"
5. 应该看到错误提示

### 场景4：设置了完整的停止条件
**预期行为**：
- 验证通过，任务正常启动

**测试步骤**：
1. 创建一个行动任务并添加环境变量（如：progress = 0）
2. 选择"变量停止"模式
3. 添加完整的停止条件：
   - 变量类型：环境变量
   - 变量名：progress
   - 运算符：>=
   - 阈值：100
4. 点击"确定"
5. 任务应该正常启动

## 验证要点

### 前端验证逻辑
```javascript
// 检查是否至少有一个完整的停止条件
const validConditions = stopConditions.filter(condition => 
  condition && 
  condition.type && 
  condition.variable && 
  condition.operator && 
  condition.value !== undefined && 
  condition.value !== ''
);

if (validConditions.length === 0) {
  message.error('变量停止模式必须设置至少一个完整的停止条件');
  return;
}
```

### UI状态检查
1. **Radio选项禁用**：
   - `disabled={!hasAvailableVariables}`
   - 显示提示文本

2. **警告提示**：
   - 当没有可用变量时显示Alert组件
   - 提示用户先创建变量

3. **按钮状态**：
   - "添加停止条件"按钮在没有可用变量时被禁用

## 错误消息验证

### 应该显示的错误消息
1. `"变量停止模式必须设置至少一个完整的停止条件"`
2. `"当前任务没有可用的环境变量或智能体变量，无法使用变量停止模式"`
3. `"变量触发模式必须设置至少一个完整的触发条件"`

### 错误消息触发条件
- 选择变量停止模式但没有设置停止条件
- 选择变量停止模式但停止条件不完整
- 没有可用变量但尝试使用变量停止模式

## 测试检查清单

- [ ] 没有变量时，变量停止选项被禁用
- [ ] 没有变量时，显示相应提示信息
- [ ] 没有变量时，显示警告Alert
- [ ] 没有停止条件时，阻止任务启动
- [ ] 停止条件不完整时，阻止任务启动
- [ ] 完整停止条件时，允许任务启动
- [ ] 错误消息显示正确
- [ ] 错误消息样式正确（红色，显眼）

## 预期改进效果

### 用户体验改进
1. **提前预防**：在用户尝试启动任务前就提示问题
2. **清晰指导**：明确告诉用户需要做什么
3. **视觉反馈**：通过禁用状态和颜色提示问题

### 系统稳定性改进
1. **减少无效请求**：避免发送无效的任务启动请求
2. **错误处理前移**：在前端就处理配置错误
3. **用户友好**：提供清晰的错误信息而不是技术错误

## 注意事项

1. **变量检查逻辑**：
   ```javascript
   const hasAvailableVariables = environmentVariables?.length > 0 ||
     Object.values(agentVariables || {}).some(vars => vars?.length > 0) ||
     externalVariables?.length > 0;
   ```

2. **条件完整性检查**：
   - 必须有变量类型（type）
   - 必须有变量名（variable）
   - 必须有运算符（operator）
   - 必须有阈值（value），且不能为空字符串

3. **错误处理**：
   - 使用 `message.error()` 显示错误
   - 使用 `return` 阻止表单提交
   - 保持表单状态，让用户可以修正错误

---

*测试完成后，变量停止功能的前端验证应该能够有效防止用户启动无效的任务配置。*
