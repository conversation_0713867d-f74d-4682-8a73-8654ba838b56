import api from '../axios';

/**
 * Weaviate向量数据库API服务
 * 提供与Weaviate向量数据库相关的API函数
 */
const weaviateAPI = {
  /**
   * 测试Weaviate连接
   * @param {Object} config Weaviate配置
   * @returns {Promise<Object>} 连接测试结果
   */
  testConnection: async (config) => {
    try {
      const response = await api.post('/vector-db/test-connection', {
        provider: 'weaviate',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('测试Weaviate连接失败:', error);
      throw error;
    }
  },

  /**
   * 验证Weaviate配置
   * @param {Object} config Weaviate配置
   * @returns {Promise<Object>} 验证结果
   */
  validateConfig: async (config) => {
    try {
      const response = await api.post('/vector-db/validate-config', {
        provider: 'weaviate',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('验证Weaviate配置失败:', error);
      throw error;
    }
  },

  /**
   * 获取Weaviate类列表
   * @param {Object} config Weaviate配置
   * @returns {Promise<Object>} 类列表
   */
  listClasses: async (config) => {
    try {
      const response = await api.post('/vector-db/list-classes', {
        provider: 'weaviate',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('获取Weaviate类列表失败:', error);
      throw error;
    }
  },

  /**
   * 创建Weaviate类
   * @param {Object} config Weaviate配置
   * @param {Object} classSchema 类schema
   * @returns {Promise<Object>} 创建结果
   */
  createClass: async (config, classSchema) => {
    try {
      const response = await api.post('/vector-db/create-class', {
        provider: 'weaviate',
        config: config,
        class_schema: classSchema
      });
      return response.data;
    } catch (error) {
      console.error('创建Weaviate类失败:', error);
      throw error;
    }
  },

  /**
   * 向量搜索
   * @param {Object} config Weaviate配置
   * @param {string} className 类名称
   * @param {Object} searchParams 搜索参数
   * @returns {Promise<Object>} 搜索结果
   */
  vectorSearch: async (config, className, searchParams) => {
    try {
      const response = await api.post('/vector-db/vector-search', {
        provider: 'weaviate',
        config: config,
        class_name: className,
        search_params: searchParams
      });
      return response.data;
    } catch (error) {
      console.error('Weaviate向量搜索失败:', error);
      throw error;
    }
  }
};

export default weaviateAPI;
