import React, { useState, useEffect } from 'react';
import { Card, Table, Typography, Spin, Empty, message, Button, Space, Breadcrumb } from 'antd';
import { EyeOutlined, DeleteOutlined, HomeOutlined, ReloadOutlined } from '@ant-design/icons';
import WorkspaceFileViewer from '../../workspace/components/WorkspaceFileViewer';
import { workspaceAPI } from '../../../services/api/workspace';
import { actionTaskAPI } from '../../../services/api/actionTask';
import { getFileIcon, getDisplayName, formatFileSize, processFileData } from '../../../utils/workspaceUtils';

const { Text } = Typography;

/**
 * 行动任务工作空间组件
 * 直接引用工作空间浏览器的文件展示逻辑，展示当前任务下的所有文件
 */
const ActionTaskWorkspace = ({ task }) => {
  const [workspaceFiles, setWorkspaceFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [isViewerVisible, setIsViewerVisible] = useState(false);
  const [agentInfo, setAgentInfo] = useState({}); // 智能体信息缓存
  const [currentPath, setCurrentPath] = useState(''); // 当前浏览的路径
  const [breadcrumbs, setBreadcrumbs] = useState([]); // 面包屑导航









  // 获取智能体信息
  const loadAgentInfo = async (task) => {
    try {
      // 获取任务的智能体信息
      const agents = await actionTaskAPI.getAgents(task.id);
      const agentMap = {};
      agents.forEach(agent => {
        agentMap[agent.id] = {
          name: agent.name,
          role_name: agent.role_name
        };
      });
      setAgentInfo(agentMap);
    } catch (error) {
      console.error('获取智能体信息失败:', error);
    }
  };

  // 加载工作空间文件
  const loadWorkspaceFiles = async (path = '') => {
    if (!task || !task.id) return;

    setLoading(true);
    try {
      // 检查是否需要重新加载智能体信息
      // 如果当前任务ID与上次加载的不同，或者智能体信息为空，则重新加载
      let currentAgentInfo = agentInfo;
      const needReloadAgentInfo = Object.keys(agentInfo).length === 0 ||
                                  !agentInfo._taskId ||
                                  agentInfo._taskId !== task.id;

      if (needReloadAgentInfo) {
        await loadAgentInfo(task);
        // 重新获取任务的智能体信息，确保我们有最新的数据
        const agents = await actionTaskAPI.getAgents(task.id);
        const agentMap = { _taskId: task.id }; // 添加任务ID标记
        agents.forEach(agent => {
          agentMap[agent.id] = {
            name: agent.name,
            role_name: agent.role_name
          };
        });
        currentAgentInfo = agentMap;
        setAgentInfo(agentMap);
      }

      console.log('正在加载工作空间文件，任务ID:', task.id, '路径:', path);
      const data = await workspaceAPI.getWorkspaceFiles(task.id, path);
      console.log('API返回数据:', data);

      let processedFiles = [];

      // 使用共用的数据处理函数，使用当前的智能体信息
      processedFiles = processFileData(data, currentAgentInfo);

      // 设置当前路径和面包屑导航
      setCurrentPath(path);

      if (path) {
        // 子目录：添加面包屑导航
        const pathParts = path.split('/');
        const breadcrumbs = [{ name: task.title, path: '' }];

        // 构建面包屑路径
        let currentPath = '';
        pathParts.forEach((part) => {
          currentPath = currentPath ? `${currentPath}/${part}` : part;
          breadcrumbs.push({
            name: part,
            path: currentPath
          });
        });

        setBreadcrumbs(breadcrumbs);
      } else {
        // 根目录
        setBreadcrumbs([{ name: task.title, path: '' }]);
      }

      console.log('处理后的文件列表:', processedFiles);
      setWorkspaceFiles(processedFiles);

      // 后端已经提供了文件大小，不需要前端异步加载

    } catch (error) {
      console.error('加载工作空间文件失败:', error);
      message.error('加载工作空间文件失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理目录点击
  const handleDirectoryClick = (record) => {
    if (record.isDirectory) {
      // 从完整路径中提取相对于任务目录的子路径
      // 例如：ActionTask-5/Agent-5 -> Agent-5
      const taskPrefix = `ActionTask-${task.id}/`;
      let subPath = record.file_path;
      if (subPath.startsWith(taskPrefix)) {
        subPath = subPath.substring(taskPrefix.length);
      }
      console.log('点击目录，原始路径:', record.file_path, '子路径:', subPath);
      loadWorkspaceFiles(subPath);
    }
  };

  // 处理面包屑导航
  const handleBreadcrumbClick = (breadcrumb) => {
    console.log('面包屑导航，路径:', breadcrumb.path);
    loadWorkspaceFiles(breadcrumb.path);
  };

  // 查看文件
  const handleViewFile = (file) => {
    setSelectedFile(file);
    setIsViewerVisible(true);
  };

  // 删除文件
  const handleDeleteFile = async (file) => {
    try {
      await workspaceAPI.deleteWorkspaceFile(file.file_path);
      message.success('文件删除成功');
      loadWorkspaceFiles(currentPath); // 重新加载当前目录
    } catch (error) {
      console.error('删除文件失败:', error);
      message.error('删除文件失败');
    }
  };



  // 初始化时加载工作空间文件
  useEffect(() => {
    if (task && task.id) {
      loadWorkspaceFiles();
    }
  }, [task?.id]);

  // 文件列表的列定义
  const columns = [
    {
      title: '文件名',
      dataIndex: 'file_name',
      key: 'file_name',
      width: '50%',
      render: (text, record) => (
        <div
          style={{
            display: 'flex',
            alignItems: 'flex-start',
            cursor: record.isDirectory ? 'pointer' : 'default',
            minHeight: '40px',
            paddingTop: '4px'
          }}
          onClick={() => record.isDirectory && handleDirectoryClick(record)}
        >
          {/* 文件夹图标区域 */}
          <div style={{
            width: 16,
            height: 20, // 与第一行文本高度一致
            marginRight: 8,
            display: 'flex',
            alignItems: 'center', // 垂直居中对齐标题文本
            justifyContent: 'center',
            flexShrink: 0
          }}>
            {record.icon || getFileIcon(record.file_name, record.isDirectory)}
          </div>

          {/* 文件信息区域 */}
          <div style={{ flex: 1, minWidth: 0 }}>
            <div style={{
              fontWeight: 500,
              fontSize: '14px',
              lineHeight: '20px',
              color: record.isDirectory ? '#1890ff' : '#262626',
              marginBottom: record.display_name && record.display_name !== record.file_name ? '2px' : 0,
              wordBreak: 'break-word'
            }}>
              {record.file_name}
            </div>
            {record.display_name && record.display_name !== record.file_name && (
              <div style={{
                fontSize: '12px',
                color: '#8c8c8c',
                lineHeight: '16px'
              }}>
                {record.display_name}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: '15%',
      render: (size, record) => (
        <Text type="secondary" style={{ fontSize: '12px' }}>
          {record.isDirectory ? '' : size}
        </Text>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: '20%',
      render: (_, record) => (
        <Space size="small">
          {!record.isDirectory && (
            <>
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => handleViewFile(record)}
                title="查看"
              />

              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteFile(record)}
                title="删除"
                danger
              />
            </>
          )}
        </Space>
      )
    }
  ];

  if (!task) {
    return <Empty description="请先选择一个任务" />;
  }

  return (
    <div className="action-task-workspace">
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text strong>{task.title} - 工作空间</Text>
            </div>
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => loadWorkspaceFiles(currentPath)}
              loading={loading}
              title="刷新"
            />
          </div>
        }
        size="small"
        style={{ marginBottom: 16 }}
      >
        {/* 面包屑导航 */}
        {breadcrumbs.length > 0 && (
          <div style={{
            padding: '8px 16px',
            borderBottom: '1px solid #f0f0f0',
            backgroundColor: '#fafafa'
          }}>
            <Breadcrumb>
              {breadcrumbs.map((crumb, index) => (
                <Breadcrumb.Item
                  key={index}
                  onClick={() => handleBreadcrumbClick(crumb)}
                  style={{
                    cursor: index < breadcrumbs.length - 1 ? 'pointer' : 'default'
                  }}
                >
                  {index === 0 ? (
                    <span style={{
                      color: index < breadcrumbs.length - 1 ? '#1890ff' : 'inherit'
                    }}>
                      <HomeOutlined style={{ marginRight: 4 }} />
                      {crumb.name}
                    </span>
                  ) : (
                    <span style={{
                      color: index < breadcrumbs.length - 1 ? '#1890ff' : 'inherit'
                    }}>
                      {crumb.name}
                    </span>
                  )}
                </Breadcrumb.Item>
              ))}
            </Breadcrumb>
          </div>
        )}

        <Table
          columns={columns}
          dataSource={workspaceFiles}
          loading={loading}
          pagination={false}
          size="middle"
          rowClassName={() => 'workspace-table-row'}
          locale={{
            emptyText: '该任务暂无工作空间文件'
          }}
        />


      </Card>

      {/* 文件查看器 */}
      <WorkspaceFileViewer
        visible={isViewerVisible}
        file={selectedFile}
        onClose={() => setIsViewerVisible(false)}
        onSave={() => {
          setIsViewerVisible(false);
          loadWorkspaceFiles(currentPath); // 重新加载当前目录
        }}
      />
    </div>
  );
};

export default ActionTaskWorkspace;
