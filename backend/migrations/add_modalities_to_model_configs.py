#!/usr/bin/env python3
"""
数据库迁移脚本：为模型配置表添加modalities字段
"""

import os
import sys
import sqlite3
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def migrate_database():
    """执行数据库迁移"""
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查modalities字段是否已存在
        cursor.execute("PRAGMA table_info(model_configs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'modalities' in columns:
            print("modalities字段已存在，跳过迁移")
            conn.close()
            return True
        
        print("开始添加modalities字段...")
        
        # 添加modalities字段
        cursor.execute("ALTER TABLE model_configs ADD COLUMN modalities JSON")
        
        # 为现有记录设置默认的modalities值
        # 根据现有的capabilities推断modalities
        cursor.execute("SELECT id, capabilities FROM model_configs")
        records = cursor.fetchall()
        
        for record_id, capabilities_json in records:
            capabilities = json.loads(capabilities_json) if capabilities_json else []
            
            # 根据能力推断模态
            modalities = []
            
            # 基本文本模态（所有模型都有）
            modalities.extend(['text_input', 'text_output'])
            
            # 根据能力添加其他模态
            if 'vision' in capabilities:
                modalities.append('image_input')
            
            if 'image' in capabilities:
                modalities.append('image_output')
                
            if 'audio' in capabilities:
                modalities.extend(['audio_input', 'audio_output'])
            
            if 'tts' in capabilities:
                modalities.append('audio_output')
            
            # 去重
            modalities = list(set(modalities))
            
            # 更新记录
            cursor.execute(
                "UPDATE model_configs SET modalities = ? WHERE id = ?",
                (json.dumps(modalities), record_id)
            )
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"成功为 {len(records)} 个模型配置添加了modalities字段")
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == '__main__':
    print("开始数据库迁移：添加modalities字段到model_configs表")
    success = migrate_database()
    
    if success:
        print("迁移完成！")
        sys.exit(0)
    else:
        print("迁移失败！")
        sys.exit(1)
