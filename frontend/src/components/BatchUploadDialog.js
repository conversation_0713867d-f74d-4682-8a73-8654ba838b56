import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Upload,
  Button,
  Select,
  List,
  Progress,
  Space,
  Typography,
  Tag,
  message,
  Divider,
  Empty,
  Tooltip,
  Card,
  Row,
  Col,
  Alert
} from 'antd';
import {
  InboxOutlined,
  UploadOutlined,
  DeleteOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FileMarkdownOutlined,
  FileUnknownOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  FolderOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import knowledgeAPI from '../services/api/knowledge';
import {
  validateFileType,
  formatFileSize,
  generateUniqueId,
  getSupportedFileTypesDescription,
  getFileAcceptString,
  getFileIcon
} from '../utils/fileUtils';

const { Title, Text } = Typography;
const { Dragger } = Upload;
const { Option } = Select;

// 文件状态枚举
const FILE_STATUS = {
  PENDING: 'pending',
  UPLOADING: 'uploading',
  COMPLETED: 'completed',
  FAILED: 'failed'
};





const BatchUploadDialog = ({
  visible,
  onClose,
  knowledgeBases = [],
  onUploadComplete,
  defaultKnowledgeBaseId = null
}) => {
  const [selectedKnowledgeBaseId, setSelectedKnowledgeBaseId] = useState(defaultKnowledgeBaseId);
  const [uploadQueue, setUploadQueue] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStats, setUploadStats] = useState({
    total: 0,
    completed: 0,
    failed: 0,
    inProgress: 0
  });

  // 重置状态
  const resetState = useCallback(() => {
    setUploadQueue([]);
    setIsUploading(false);
    setUploadStats({
      total: 0,
      completed: 0,
      failed: 0,
      inProgress: 0
    });
  }, []);

  // 当对话框关闭时重置状态
  useEffect(() => {
    if (!visible) {
      resetState();
    }
  }, [visible, resetState]);

  // 当默认知识库ID改变时更新选中的知识库
  useEffect(() => {
    if (defaultKnowledgeBaseId) {
      setSelectedKnowledgeBaseId(defaultKnowledgeBaseId);
    }
  }, [defaultKnowledgeBaseId]);



  // 处理文件添加到队列
  const handleFilesAdd = (files) => {
    const validFiles = [];
    const invalidFiles = [];
    const duplicateFiles = [];

    files.forEach(file => {
      // 检查是否已经在队列中（基于文件名和大小）
      const isDuplicate = uploadQueue.some(item =>
        item.name === file.name && item.size === file.size
      );

      if (isDuplicate) {
        duplicateFiles.push(file.name);
        return;
      }

      if (validateFileType(file)) {
        const queueItem = {
          id: generateUniqueId(),
          file: file,
          name: file.name,
          size: file.size,
          type: file.name.split('.').pop()?.toLowerCase() || 'unknown',
          status: FILE_STATUS.PENDING,
          progress: 0,
          error: null
        };
        validFiles.push(queueItem);
      } else {
        invalidFiles.push(file.name);
      }
    });

    if (duplicateFiles.length > 0) {
      message.info(`以下文件已在队列中，已跳过: ${duplicateFiles.join(', ')}`);
    }

    if (invalidFiles.length > 0) {
      message.warning(`以下文件类型不支持: ${invalidFiles.join(', ')}`);
    }

    if (validFiles.length > 0) {
      setUploadQueue(prev => [...prev, ...validFiles]);
      setUploadStats(prev => ({
        ...prev,
        total: prev.total + validFiles.length
      }));
    }

    return false; // 阻止默认上传行为
  };

  // 从队列中移除文件
  const removeFromQueue = (id) => {
    setUploadQueue(prev => {
      const newQueue = prev.filter(item => item.id !== id);
      const removedItem = prev.find(item => item.id === id);

      if (removedItem && removedItem.status === FILE_STATUS.PENDING) {
        setUploadStats(prevStats => ({
          ...prevStats,
          total: prevStats.total - 1
        }));
      }

      return newQueue;
    });
  };

  // 重试上传
  const retryUpload = (id) => {
    setUploadQueue(prev => prev.map(item =>
      item.id === id
        ? { ...item, status: FILE_STATUS.PENDING, progress: 0, error: null }
        : item
    ));

    setUploadStats(prev => ({
      ...prev,
      failed: prev.failed - 1,
      total: prev.total // 保持总数不变
    }));
  };

  // 单个文件上传
  const uploadSingleFile = async (queueItem) => {
    if (!selectedKnowledgeBaseId) {
      throw new Error('请先选择知识库');
    }

    // 更新状态为上传中
    setUploadQueue(prev => prev.map(item =>
      item.id === queueItem.id
        ? { ...item, status: FILE_STATUS.UPLOADING, progress: 0 }
        : item
    ));

    setUploadStats(prev => ({
      ...prev,
      inProgress: prev.inProgress + 1
    }));

    try {
      const formData = new FormData();
      formData.append('file', queueItem.file);

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setUploadQueue(prev => prev.map(item =>
          item.id === queueItem.id && item.status === FILE_STATUS.UPLOADING
            ? { ...item, progress: Math.min(item.progress + 10, 90) }
            : item
        ));
      }, 200);

      const response = await knowledgeAPI.uploadFile(selectedKnowledgeBaseId, formData);

      clearInterval(progressInterval);

      if (response.success) {
        // 上传成功
        setUploadQueue(prev => prev.map(item =>
          item.id === queueItem.id
            ? { ...item, status: FILE_STATUS.COMPLETED, progress: 100 }
            : item
        ));

        setUploadStats(prev => ({
          ...prev,
          completed: prev.completed + 1,
          inProgress: prev.inProgress - 1
        }));

        return { success: true, file: queueItem.name };
      } else {
        throw new Error(response.message || '上传失败');
      }
    } catch (error) {
      // 上传失败
      setUploadQueue(prev => prev.map(item =>
        item.id === queueItem.id
          ? {
              ...item,
              status: FILE_STATUS.FAILED,
              progress: 0,
              error: error.message
            }
          : item
      ));

      setUploadStats(prev => ({
        ...prev,
        failed: prev.failed + 1,
        inProgress: prev.inProgress - 1
      }));

      return { success: false, file: queueItem.name, error: error.message };
    }
  };

  // 批量上传
  const handleBatchUpload = async () => {
    if (!selectedKnowledgeBaseId) {
      message.error('请先选择知识库');
      return;
    }

    const pendingFiles = uploadQueue.filter(item => item.status === FILE_STATUS.PENDING);

    if (pendingFiles.length === 0) {
      message.warning('没有待上传的文件');
      return;
    }

    setIsUploading(true);

    try {
      // 并发上传，限制并发数为3
      const concurrencyLimit = 3;
      const results = [];

      for (let i = 0; i < pendingFiles.length; i += concurrencyLimit) {
        const batch = pendingFiles.slice(i, i + concurrencyLimit);
        const batchPromises = batch.map(file => uploadSingleFile(file));
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      }

      // 通知父组件上传完成
      if (onUploadComplete) {
        onUploadComplete(results);
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      if (successCount > 0 && failCount === 0) {
        message.success(`成功上传 ${successCount} 个文件`);
      } else if (successCount > 0 && failCount > 0) {
        message.warning(`成功上传 ${successCount} 个文件，${failCount} 个文件上传失败`);
      } else {
        message.error('所有文件上传失败');
      }

    } catch (error) {
      console.error('批量上传失败:', error);
      message.error('批量上传失败');
    } finally {
      setIsUploading(false);
    }
  };

  // 清空队列
  const clearQueue = () => {
    if (isUploading) {
      message.warning('正在上传中，无法清空队列');
      return;
    }
    resetState();
  };

  // 拖拽上传配置
  const draggerProps = {
    name: 'files',
    multiple: true,
    directory: true, // 支持文件夹上传
    beforeUpload: (file, fileList) => {
      handleFilesAdd(fileList);
      return false; // 阻止默认上传
    },
    showUploadList: false,
    accept: getFileAcceptString(),
  };

  // 渲染队列项状态图标
  const renderStatusIcon = (status) => {
    switch (status) {
      case FILE_STATUS.PENDING:
        return <Tag color="default">待上传</Tag>;
      case FILE_STATUS.UPLOADING:
        return <Tag color="processing" icon={<LoadingOutlined />}>上传中</Tag>;
      case FILE_STATUS.COMPLETED:
        return <Tag color="success" icon={<CheckCircleOutlined />}>已完成</Tag>;
      case FILE_STATUS.FAILED:
        return <Tag color="error" icon={<ExclamationCircleOutlined />}>失败</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  // 渲染队列项
  const renderQueueItem = (item) => {
    return (
      <List.Item
        key={item.id}
        actions={[
          item.status === FILE_STATUS.FAILED && (
            <Tooltip title="重试上传">
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={() => retryUpload(item.id)}
                disabled={isUploading}
              />
            </Tooltip>
          ),
          item.status === FILE_STATUS.PENDING && (
            <Tooltip title="从队列中移除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => removeFromQueue(item.id)}
                disabled={isUploading}
              />
            </Tooltip>
          )
        ].filter(Boolean)}
      >
        <List.Item.Meta
          avatar={getFileIcon(item.name)}
          title={
            <Space>
              <Text>{item.name}</Text>
              {renderStatusIcon(item.status)}
            </Space>
          }
          description={
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text type="secondary">{formatFileSize(item.size)}</Text>
              {item.status === FILE_STATUS.UPLOADING && (
                <Progress
                  percent={item.progress}
                  size="small"
                  status="active"
                />
              )}
              {item.status === FILE_STATUS.FAILED && item.error && (
                <Text type="danger" style={{ fontSize: '12px' }}>
                  {item.error}
                </Text>
              )}
            </Space>
          }
        />
      </List.Item>
    );
  };

  return (
    <Modal
      title="批量上传文件"
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="clear" onClick={clearQueue} disabled={isUploading}>
          清空队列
        </Button>,
        <Button key="cancel" onClick={onClose} disabled={isUploading}>
          取消
        </Button>,
        <Button
          key="upload"
          type="primary"
          onClick={handleBatchUpload}
          loading={isUploading}
          disabled={uploadQueue.filter(item => item.status === FILE_STATUS.PENDING).length === 0}
        >
          开始上传
        </Button>
      ]}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 知识库选择 */}
        <div>
          <Title level={5}>选择目标知识库</Title>
          <Select
            style={{ width: '100%' }}
            placeholder="请选择知识库"
            value={selectedKnowledgeBaseId}
            onChange={setSelectedKnowledgeBaseId}
            disabled={isUploading}
          >
            {knowledgeBases.map(kb => (
              <Option key={kb.id} value={kb.id}>
                {kb.name}
              </Option>
            ))}
          </Select>
        </div>

        {/* 文件上传区域 */}
        <div>
          <Title level={5}>选择文件或文件夹</Title>
          <Dragger {...draggerProps} disabled={isUploading}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件/文件夹到此区域上传</p>
            <p className="ant-upload-hint">
              支持单个或批量上传。{getSupportedFileTypesDescription()}
            </p>
          </Dragger>
        </div>

        {/* 上传统计 */}
        {uploadStats.total > 0 && (
          <Card size="small">
            <Row gutter={16}>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                    {uploadStats.total}
                  </div>
                  <div style={{ color: '#8c8c8c' }}>总计</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {uploadStats.completed}
                  </div>
                  <div style={{ color: '#8c8c8c' }}>已完成</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                    {uploadStats.inProgress}
                  </div>
                  <div style={{ color: '#8c8c8c' }}>进行中</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>
                    {uploadStats.failed}
                  </div>
                  <div style={{ color: '#8c8c8c' }}>失败</div>
                </div>
              </Col>
            </Row>
          </Card>
        )}

        {/* 上传队列 - 只有当有文件时才显示 */}
        {uploadQueue.length > 0 && (
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
              <Title level={5} style={{ margin: 0 }}>上传队列</Title>
              <Text type="secondary">共 {uploadQueue.length} 个文件</Text>
            </div>

            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
              <List
                dataSource={uploadQueue}
                renderItem={renderQueueItem}
                size="small"
              />
            </div>
          </div>
        )}

        {/* 提示信息 */}
        {!selectedKnowledgeBaseId && (
          <Alert
            message="请先选择目标知识库"
            type="warning"
            showIcon
          />
        )}
      </Space>
    </Modal>
  );
};

export default BatchUploadDialog;