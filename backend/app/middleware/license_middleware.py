"""
许可证中间件

用于检查API请求的许可证状态，在许可证过期时拒绝非授权相关的API请求
"""
from functools import wraps
from flask import request, jsonify, current_app
from app.services.license_service import LicenseService

# 不需要许可证验证的路径列表
EXEMPT_PATHS = [
    # 许可证相关API
    '/api/license',
    '/api/license/expired',
    '/api/license/activate',
    '/api/license/activate-file',
    '/api/license/system-key',

    # 认证相关API
    '/api/auth/login',
    '/api/auth/logout',
    '/api/auth/validate',
    '/api/auth/user',

    # 系统健康检查API
    '/api/health',
]

def is_path_exempt(path):
    """检查路径是否免除许可证验证"""
    # 检查路径是否在豁免列表中
    for exempt_path in EXEMPT_PATHS:
        if path.startswith(exempt_path):
            return True
    return False

def license_required(f):
    """许可证验证装饰器，用于API路由"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查路径是否免除许可证验证
        if is_path_exempt(request.path):
            current_app.logger.debug(f"路径 {request.path} 免除许可证验证")
            return f(*args, **kwargs)

        # 创建许可证服务实例
        license_service = LicenseService()

        # 获取当前许可证
        license_data = license_service.get_current_license()

        # 如果没有有效许可证，返回错误
        if not license_data:
            current_app.logger.warning(f"访问 {request.path} 被拒绝：许可证无效或已过期")
            return jsonify({
                'status': 'error',
                'message': '许可证无效或已过期，请激活系统',
                'code': 'LICENSE_EXPIRED'
            }), 403

        # 许可证有效，继续处理请求
        return f(*args, **kwargs)

    return decorated_function

def register_license_middleware(app):
    """注册许可证中间件"""
    @app.before_request
    def check_license():
        # 只处理API请求
        if not request.path.startswith('/api/'):
            return None

        # 跳过OPTIONS预检请求，避免CORS问题
        if request.method == 'OPTIONS':
            return None

        # 检查路径是否免除许可证验证
        if is_path_exempt(request.path):
            return None

        # 创建许可证服务实例
        license_service = LicenseService()

        # 获取当前许可证
        license_data = license_service.get_current_license()

        # 如果没有有效许可证，返回错误
        if not license_data:
            app.logger.warning(f"访问 {request.path} 被拒绝：许可证无效或已过期")
            return jsonify({
                'status': 'error',
                'message': '许可证无效或已过期，请激活系统',
                'code': 'LICENSE_EXPIRED'
            }), 403

        # 许可证有效，继续处理请求
        return None
