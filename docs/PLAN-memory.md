# 记忆系统设计方案（已过时）

> **注意：此文档已过时**
> 原有的基于文件的"智能体记忆"系统已重新定位为"项目空间"系统。
> 真正的智能体记忆功能将基于向量数据库+RAG技术实现。
> 请参考最新的项目空间相关文档。

## 记忆系统核心概念

记忆系统包含四种类型：

1. **短期记忆（上下文）**：对话中的即时上下文，系统自动处理
2. **长期记忆（Agent独享）**：每个Agent的个人记忆和技能与经验记忆，Markdown格式
3. **任务空间记忆（Agent共享）**：行动空间内所有角色共享的记忆，包含用户偏好记忆
4. **专业知识记忆（知识库）**：未来实现的知识库工具调用

长期记忆可以转化为记忆模板，在创建行动空间选择角色时可以选择这些模板。

## 数据模型设计

### 记忆类型枚举
```
MemoryType {
  SHORT_TERM = "short_term"        // 短期记忆(上下文)
  LONG_TERM = "long_term"          // 长期记忆(agent独享)
  KNOWLEDGE_BASE = "knowledge_base" // 专业知识记忆
  SHARED_SPACE = "shared_space"     // 任务空间记忆(共享)
}
```

### 记忆数据结构
```
Memory {
  id: string                // 唯一标识
  content: string           // 记忆内容，Markdown格式
  type: MemoryType          // 记忆类型
  created_at: timestamp     // 创建时间
  updated_at: timestamp     // 更新时间
}

MemoryAssociation {
  memory_id: string         // 记忆ID
  entity_id: string         // 关联实体ID(Agent或ActionSpace)
  entity_type: string       // 关联类型(Agent/ActionSpace)
}

MemoryTemplate {
  id: string                // 唯一标识
  name: string              // 模板名称
  content: string           // 模板内容
  created_at: timestamp     // 创建时间
}
```

## 用户界面设计

### 1. 行动空间创建/编辑界面
- **共享记忆编辑区**：
  - 位置：行动空间创建/编辑表单中添加"共享记忆"部分
  - 功能：设置行动空间的共享记忆内容（Markdown编辑器）
  - 可从现有模板导入内容

- **角色选择区**：
  - 位置：行动空间创建/编辑表单中的角色选择部分
  - 功能：选择角色时可以同时选择记忆模板
  - 每个角色旁边添加记忆模板下拉选择框

### 2. Agent对话界面
- **记忆访问按钮**：
  - 位置：对话界面工具栏
  - 功能：
    - 查看/编辑当前Agent的长期记忆
    - 查看当前行动空间的共享记忆
  - 点击后弹出记忆编辑器面板

- **记忆编辑器面板**：
  - 标签页切换：个人记忆/共享记忆
  - Markdown编辑器，支持预览模式
  - 保存/取消按钮
  - 转为模板按钮（仅对个人记忆可用）

### 3. 记忆模板管理界面
- **位置**：设置页面下添加"记忆模板"选项
- **模板列表**：
  - 简洁的表格布局
  - 显示模板名称、创建时间、操作按钮
  - 支持搜索和排序

- **模板编辑器**：
  - 模板名称输入框
  - Markdown编辑器
  - 保存/取消按钮

- **从记忆创建模板**：
  - 在Agent记忆编辑器中添加"转为模板"按钮
  - 点击后弹出模板名称输入对话框
  - 确认后创建新模板

### 4. 分区记忆管理界面

#### 主界面结构
分区记忆管理界面采用标签页式设计，顶部为记忆类型切换标签，下方为当前选中类型的记忆内容区域。

#### 各分区内容设计

##### 短期记忆（上下文）分区
- 显示对话历史（只读）
- 包含时间戳和消息内容
- 提示用户短期记忆由系统自动管理，仅供查看

##### 长期记忆（Agent独享）分区
- 顶部操作按钮：新建记忆、转为模板
- 左侧记忆列表
- 右侧记忆编辑器，包含标题和内容（Markdown格式）
- 底部保存/取消按钮

##### 任务空间记忆（Agent共享）分区
- 顶部操作按钮：新建记忆、导入模板
- 左侧共享记忆列表
- 右侧记忆编辑器，与长期记忆编辑器类似
- 底部保存/取消按钮

##### 专业知识记忆（知识库）分区
- 顶部操作按钮：连接知识库、搜索知识库
- 知识库列表，显示名称、描述、状态和操作
- 提示用户专业知识记忆通过知识库工具实现

#### 交互设计
- **记忆类型切换**：点击顶部标签切换不同记忆分区
- **长期记忆操作**：查看、新建、编辑、删除记忆，转为模板
- **任务空间记忆操作**：与长期记忆类似，增加导入模板功能
- **专业知识记忆操作**：连接/断开知识库，搜索知识库内容

#### 响应式设计
- **大屏幕**：左右分栏显示记忆列表和编辑器
- **中等屏幕**：记忆列表和编辑器垂直排列
- **小屏幕**：采用抽屉式设计，点击记忆项目弹出编辑器

#### 视觉设计
- 不同类型的记忆使用不同的颜色和图标标识
- 短期记忆：蓝色，时钟图标
- 长期记忆：绿色，书本图标
- 任务空间记忆：紫色，共享图标
- 专业知识记忆：橙色，数据库图标

## 用户使用流程

### 1. 创建行动空间时
1. 填写行动空间基本信息
2. 编辑共享记忆内容
3. 选择角色
4. 为每个角色选择记忆模板（可选）
5. 保存行动空间

### 2. Agent对话中
1. Agent可以通过工具调用读取自己的长期记忆
2. Agent可以通过工具调用更新自己的长期记忆
3. Agent可以通过工具调用读取共享记忆
4. 短期记忆（上下文）由系统自动处理

### 3. 用户管理Agent记忆
1. 在对话界面点击记忆按钮
2. 查看/编辑Agent的长期记忆
3. 可以将优秀的长期记忆转化为模板

### 4. 管理记忆模板
1. 进入设置页面的记忆模板管理
2. 查看所有记忆模板
3. 创建/编辑/删除模板

## 前端组件结构

```
frontend/src/
  components/
    memory/
      MemoryEditor.tsx         // 记忆编辑器组件
      MemoryTemplateSelect.tsx // 模板选择组件

  pages/
    actionspace/
      components/
        ActionSpaceMemorySection.tsx  // 行动空间记忆编辑区
        RoleMemoryTemplateSelect.tsx  // 角色记忆模板选择

    actiontask/
      components/
        AgentMemoryPanel.tsx   // Agent记忆面板

    settings/
      MemoryTemplatesPage.tsx // 记忆模板管理页面
```

## 后端API设计

```
# 记忆API
GET    /api/memory                                  // 获取记忆，支持参数：id, owner_id, owner_type
POST   /api/memory                                  // 创建或更新记忆
DELETE /api/memory/{id}                             // 删除记忆

# 记忆模板API
GET    /api/memory-template                         // 获取记忆模板，支持参数：id
POST   /api/memory-template                         // 创建或更新记忆模板
DELETE /api/memory-template/{id}                    // 删除记忆模板

# 角色记忆模板关联API
POST   /api/action-space/{space_id}/role/{role_id}/memory-template/{template_id}  // 关联模板
```

## 实现步骤

### 第一步：基础记忆功能
1. 实现记忆的存储和检索
2. 实现Agent长期记忆和行动空间共享记忆的关联
3. 在前端对话界面添加记忆查看/编辑功能

### 第二步：记忆模板功能
1. 实现记忆模板的创建和管理
2. 实现从长期记忆创建模板的功能
3. 在行动空间角色选择时集成记忆模板选择