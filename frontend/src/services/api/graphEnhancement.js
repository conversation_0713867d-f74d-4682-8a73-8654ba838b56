import api from './axios';

const graphEnhancementAPI = {
  // 获取配置
  getConfig: async () => {
    const response = await api.get('/graph-enhancement/config');
    return response.data;
  },

  // 保存配置
  saveConfig: async (data) => {
    const response = await api.post('/graph-enhancement/config', data);
    return response.data;
  },

  // 获取状态
  getStatus: async () => {
    const response = await api.get('/graph-enhancement/status');
    return response.data;
  },

  // 测试连接
  testConnection: async (data) => {
    const response = await api.post('/graph-enhancement/test-connection', data);
    return response.data;
  },

  // 测试查询
  testQuery: async (data) => {
    const response = await api.post('/graph-enhancement/test-query', data);
    return response.data;
  },

  // 重建索引
  rebuildIndex: async () => {
    const response = await api.post('/graph-enhancement/rebuild-index');
    return response.data;
  },

  // 清空图谱数据
  clearGraph: async () => {
    const response = await api.post('/graph-enhancement/clear-graph');
    return response.data;
  },
};

export default graphEnhancementAPI;