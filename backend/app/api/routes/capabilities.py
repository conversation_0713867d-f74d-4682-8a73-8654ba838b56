from flask import Blueprint, request, jsonify, g
from app.models import Capability, RoleCapability, Role
from app.extensions import db
from sqlalchemy.exc import SQLAlchemyError
import logging
from flask import current_app
import json

# 创建蓝图
bp = Blueprint('capabilities', __name__)
capabilities_bp = bp  # 添加别名以便在routes/__init__.py中使用
logger = logging.getLogger(__name__)

@bp.route('/capabilities', methods=['GET'])
def get_capabilities():
    """获取所有能力列表"""
    try:
        capabilities = Capability.query.all()
        result = []
        for capability in capabilities:
            capability_data = {
                'id': capability.id,
                'name': capability.name,
                'description': capability.description,
                'type': capability.type,
                'provider': capability.provider,
                'parameters': capability.parameters,
                'response_format': capability.response_format,
                'examples': capability.examples,
                'settings': capability.settings,
                'security_level': capability.security_level,
                'default_enabled': capability.default_enabled,
                'tools': capability.tools,
                'icon': capability.icon,
                'created_at': capability.created_at.isoformat() if capability.created_at else None,
                'updated_at': capability.updated_at.isoformat() if capability.updated_at else None
            }
            result.append(capability_data)
        return jsonify({'status': 'success', 'data': result}), 200
    except Exception as e:
        logger.error(f"获取能力列表失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'获取能力列表失败: {str(e)}'}), 500

@bp.route('/capabilities/<int:capability_id>', methods=['GET'])
def get_capability(capability_id):
    """获取特定能力详情"""
    try:
        capability = Capability.query.get(capability_id)
        if not capability:
            return jsonify({'status': 'error', 'message': '能力不存在'}), 404
        
        # 获取使用该能力的角色列表
        role_capabilities = RoleCapability.query.filter_by(capability_id=capability_id).all()
        roles = []
        for rc in role_capabilities:
            role = Role.query.get(rc.role_id)
            if role:
                roles.append({
                    'id': role.id,
                    'name': role.name
                })
        
        capability_data = {
            'id': capability.id,
            'name': capability.name,
            'description': capability.description,
            'type': capability.type,
            'provider': capability.provider,
            'parameters': capability.parameters,
            'response_format': capability.response_format,
            'examples': capability.examples,
            'settings': capability.settings,
            'security_level': capability.security_level,
            'default_enabled': capability.default_enabled,
            'tools': capability.tools,
            'icon': capability.icon,
            'roles': roles,
            'created_at': capability.created_at.isoformat() if capability.created_at else None,
            'updated_at': capability.updated_at.isoformat() if capability.updated_at else None
        }
        return jsonify({'status': 'success', 'data': capability_data}), 200
    except Exception as e:
        logger.error(f"获取能力详情失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'获取能力详情失败: {str(e)}'}), 500

@bp.route('/capabilities', methods=['POST'])
def create_capability():
    """创建新能力"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        if not data.get('name'):
            return jsonify({'status': 'error', 'message': '缺少必填字段: name'}), 400
        
        # 检查名称是否重复
        existing = Capability.query.filter_by(name=data['name']).first()
        if existing:
            return jsonify({'status': 'error', 'message': '能力名称已存在'}), 400
        
        new_capability = Capability(
            name=data['name'],
            description=data.get('description', ''),
            type=data.get('type', ''),
            provider=data.get('provider', ''),
            parameters=data.get('parameters', {}),
            response_format=data.get('response_format', {}),
            examples=data.get('examples', []),
            settings=data.get('settings', {}),
            security_level=data.get('security_level', 1),
            default_enabled=data.get('default_enabled', False),
            icon=data.get('icon', '')
        )
        
        db.session.add(new_capability)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '能力创建成功',
            'data': {
                'id': new_capability.id,
                'name': new_capability.name
            }
        }), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建能力失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'创建能力失败: {str(e)}'}), 500

@bp.route('/capabilities/<int:capability_id>', methods=['PUT'])
def update_capability(capability_id):
    """更新能力信息"""
    try:
        capability = Capability.query.get(capability_id)
        if not capability:
            return jsonify({'status': 'error', 'message': '能力不存在'}), 404
        
        data = request.json
        
        # 检查名称是否重复（如果更改了名称）
        if 'name' in data and data['name'] != capability.name:
            existing = Capability.query.filter_by(name=data['name']).first()
            if existing:
                return jsonify({'status': 'error', 'message': '能力名称已存在'}), 400
            capability.name = data['name']
        
        # 更新其他字段
        if 'description' in data:
            capability.description = data['description']
        if 'type' in data:
            capability.type = data['type']
        if 'provider' in data:
            capability.provider = data['provider']
        if 'parameters' in data:
            capability.parameters = data['parameters']
        if 'response_format' in data:
            capability.response_format = data['response_format']
        if 'examples' in data:
            capability.examples = data['examples']
        if 'settings' in data:
            capability.settings = data['settings']
        if 'security_level' in data:
            capability.security_level = data['security_level']
        if 'default_enabled' in data:
            capability.default_enabled = data['default_enabled']
        if 'icon' in data:
            capability.icon = data['icon']
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '能力更新成功',
            'data': {
                'id': capability.id,
                'name': capability.name
            }
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新能力失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'更新能力失败: {str(e)}'}), 500

@bp.route('/capabilities/<int:capability_id>', methods=['DELETE'])
def delete_capability(capability_id):
    """删除能力"""
    try:
        capability = Capability.query.get(capability_id)
        if not capability:
            return jsonify({'status': 'error', 'message': '能力不存在'}), 404
        
        # 删除关联的角色能力关系
        RoleCapability.query.filter_by(capability_id=capability_id).delete()
        
        # 删除能力
        db.session.delete(capability)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '能力删除成功'
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除能力失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'删除能力失败: {str(e)}'}), 500

@bp.route('/roles/<int:role_id>/capabilities', methods=['GET'])
def get_role_capabilities(role_id):
    """获取角色的能力列表"""
    try:
        role = Role.query.get(role_id)
        if not role:
            return jsonify({'status': 'error', 'message': '角色不存在'}), 404
        
        role_capabilities = RoleCapability.query.filter_by(role_id=role_id).all()
        result = []
        
        for rc in role_capabilities:
            capability = Capability.query.get(rc.capability_id)
            if capability:
                capability_data = {
                    'id': capability.id,
                    'name': capability.name,
                    'description': capability.description,
                    'type': capability.type,
                    'provider': capability.provider
                }
                result.append(capability_data)
        
        return jsonify({'status': 'success', 'data': result}), 200
    except Exception as e:
        logger.error(f"获取角色能力列表失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'获取角色能力列表失败: {str(e)}'}), 500

@bp.route('/roles/<int:role_id>/capabilities/<int:capability_id>', methods=['POST'])
def add_capability_to_role(role_id, capability_id):
    """为角色添加能力"""
    try:
        role = Role.query.get(role_id)
        if not role:
            return jsonify({'status': 'error', 'message': '角色不存在'}), 404
        
        capability = Capability.query.get(capability_id)
        if not capability:
            return jsonify({'status': 'error', 'message': '能力不存在'}), 404
        
        # 检查是否已存在关联
        existing = RoleCapability.query.filter_by(role_id=role_id, capability_id=capability_id).first()
        if existing:
            return jsonify({'status': 'error', 'message': '角色已拥有该能力'}), 400
        
        # 创建关联
        role_capability = RoleCapability(role_id=role_id, capability_id=capability_id)
        db.session.add(role_capability)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '能力添加成功'
        }), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"为角色添加能力失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'为角色添加能力失败: {str(e)}'}), 500

@bp.route('/roles/<int:role_id>/capabilities/<int:capability_id>', methods=['DELETE'])
def remove_capability_from_role(role_id, capability_id):
    """从角色移除能力"""
    try:
        role_capability = RoleCapability.query.filter_by(role_id=role_id, capability_id=capability_id).first()
        if not role_capability:
            return jsonify({'status': 'error', 'message': '角色未拥有该能力'}), 404
        
        db.session.delete(role_capability)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '能力移除成功'
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"从角色移除能力失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'从角色移除能力失败: {str(e)}'}), 500

@bp.route('/capabilities/categories', methods=['GET'])
def get_categories():
    """获取能力分类列表"""
    try:
        # 从能力表中提取所有不同的类型作为分类
        capability_types = db.session.query(Capability.type).distinct().all()
        categories = [{"id": i+1, "name": cat[0]} for i, cat in enumerate(capability_types) if cat[0]]
        
        # 添加默认分类
        default_categories = [
            {"id": len(categories)+1, "name": "core"},
            {"id": len(categories)+2, "name": "advanced"},
            {"id": len(categories)+3, "name": "supervision"},
            {"id": len(categories)+4, "name": "execution"},
            {"id": len(categories)+5, "name": "specialized"}
        ]
        
        # 过滤已存在的分类，避免重复
        existing_types = [cat["name"] for cat in categories]
        for cat in default_categories:
            if cat["name"] not in existing_types:
                categories.append(cat)
                
        return jsonify(categories), 200
    except Exception as e:
        logger.error(f"获取能力分类列表失败: {str(e)}")
        return jsonify({"error": f"获取能力分类失败: {str(e)}"}), 500

@bp.route('/capabilities/categories', methods=['POST'])
def add_category():
    """添加新的能力分类"""
    try:
        data = request.json
        if not data or not data.get('name'):
            return jsonify({"error": "分类名称不能为空"}), 400
            
        category_name = data['name'].strip()
        
        # 这里我们不实际存储分类，因为能力的类型直接存储在能力记录中
        # 而是返回成功并通知前端已添加
        return jsonify({
            "id": 9999,  # 临时ID
            "name": category_name,
            "message": f"成功添加分类: {category_name}"
        }), 201
    except Exception as e:
        logger.error(f"添加能力分类失败: {str(e)}")
        return jsonify({"error": f"添加能力分类失败: {str(e)}"}), 500

@bp.route('/capabilities/tools', methods=['GET'])
def get_capability_tools():
    """获取所有能力的工具关联关系"""
    try:
        capabilities = Capability.query.all()
        capability_tools_map = {}
        
        for capability in capabilities:
            if capability.tools:
                # 如果tools字段已包含JSON数据，直接使用
                capability_tools_map[capability.name] = capability.tools
            else:
                # 如果tools字段为空，设置为空字典
                capability_tools_map[capability.name] = {}
        
        return jsonify(capability_tools_map), 200
    except Exception as e:
        logger.error(f"获取能力工具关联关系失败: {str(e)}")
        return jsonify({"error": f"获取能力工具关联关系失败: {str(e)}"}), 500

@bp.route('/capabilities/with_roles', methods=['GET'])
def get_capabilities_with_roles():
    """获取所有能力及其关联角色的信息"""
    try:
        capabilities = Capability.query.all()
        result = {}
        
        for capability in capabilities:
            # 获取使用该能力的角色列表
            role_capabilities = RoleCapability.query.filter_by(capability_id=capability.id).all()
            roles = []
            for rc in role_capabilities:
                role = Role.query.get(rc.role_id)
                if role:
                    roles.append({
                        'id': role.id,
                        'name': role.name
                    })
            
            # 将能力名称与角色列表建立映射关系
            result[capability.name] = roles
        
        return jsonify(result), 200
    except Exception as e:
        logger.error(f"获取能力-角色映射关系失败: {str(e)}")
        return jsonify({"error": f"获取能力-角色映射关系失败: {str(e)}"}), 500

@bp.route('/capabilities/<int:capability_id>/tools', methods=['PUT'])
def update_capability_tools(capability_id):
    """更新特定能力的工具关联关系"""
    try:
        capability = Capability.query.get(capability_id)
        if not capability:
            return jsonify({'status': 'error', 'message': '能力不存在'}), 404
        
        data = request.json
        if not data or 'tools' not in data:
            return jsonify({'status': 'error', 'message': '缺少工具关联数据'}), 400
        
        # 更新工具关联
        capability.tools = data['tools']
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '能力工具关联更新成功',
            'data': {
                'id': capability.id,
                'name': capability.name,
                'tools': capability.tools
            }
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新能力工具关联失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'更新能力工具关联失败: {str(e)}'}), 500 