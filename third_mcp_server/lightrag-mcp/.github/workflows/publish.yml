name: Publish to PyPI

on:
  push:
    tags:
      - "v*.*.*"
  workflow_dispatch:

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    environment: pypi
    permissions:
      contents: read
      id-token: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"

    - name: Install uv and build tools
      run: |
        pipx install uv
        uv venv --python 3.11
        uv pip install build

    - name: Build package
      run: |
        uv build

    - name: Publish to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        packages-dir: dist/
