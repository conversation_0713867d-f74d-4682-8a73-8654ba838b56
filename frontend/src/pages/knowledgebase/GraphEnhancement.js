import React, { useState, useEffect } from 'react';
import {
  Card,
  Switch,
  Radio,
  Form,
  Input,
  InputNumber,
  Button,
  Alert,
  Divider,
  Collapse,
  Row,
  Col,
  Spin,
  message,
  Modal,
  Typography,
  Tag,
  Space,
  Tooltip
} from 'antd';
import {
  SettingOutlined,
  <PERSON>boltOutlined,
  ShareAltOutlined,
  BarChartOutlined,
  SearchOutlined,
  ReloadOutlined,
  ClearOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import graphEnhancementAPI from '../../services/api/graphEnhancement';

const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;

const GraphEnhancement = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState(null);
  const [status, setStatus] = useState(null);
  const [testLoading, setTestLoading] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [queryModalVisible, setQueryModalVisible] = useState(false);
  const [localEnabled, setLocalEnabled] = useState(false);
  const [selectedFramework, setSelectedFramework] = useState('lightrag');

  // 加载配置
  useEffect(() => {
    loadConfig();
    loadStatus();
  }, []);

  const loadConfig = async () => {
    try {
      setLoading(true);
      const result = await graphEnhancementAPI.getConfig();

      if (result.success) {
        setConfig(result.data);
        setLocalEnabled(result.data.enabled || false);
        setSelectedFramework(result.data.framework || 'lightrag');
        form.setFieldsValue(result.data);
      } else {
        message.error(result.message || '加载配置失败');
      }
    } catch (error) {
      message.error('加载配置失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const loadStatus = async () => {
    try {
      const result = await graphEnhancementAPI.getStatus();

      if (result.success) {
        setStatus(result.data);
      }
    } catch (error) {
      console.error('加载状态失败:', error);
    }
  };

  const handleSaveConfig = async (values) => {
    try {
      setLoading(true);
      const result = await graphEnhancementAPI.saveConfig(values);

      if (result.success) {
        message.success('配置保存成功');
        setConfig({ ...config, ...values });
        loadStatus(); // 重新加载状态
      } else {
        message.error(result.message || '配置保存失败');
      }
    } catch (error) {
      message.error('配置保存失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      setTestLoading(true);
      const values = form.getFieldsValue();

      const result = await graphEnhancementAPI.testConnection({
        framework: values.framework,
        framework_config: values.framework_config || {}
      });

      if (result.success) {
        message.success('连接测试成功');
      } else {
        message.error(result.message || '连接测试失败');
      }
    } catch (error) {
      message.error('连接测试失败: ' + error.message);
    } finally {
      setTestLoading(false);
    }
  };

  const handleTestQuery = async (queryData) => {
    try {
      setTestLoading(true);
      const result = await graphEnhancementAPI.testQuery(queryData);

      if (result.success) {
        setTestResult(result.data);
        message.success('查询测试成功');
      } else {
        message.error(result.message || '查询测试失败');
        setTestResult(null);
      }
    } catch (error) {
      message.error('查询测试失败: ' + error.message);
      setTestResult(null);
    } finally {
      setTestLoading(false);
    }
  };

  const handleRebuildIndex = async () => {
    try {
      setLoading(true);
      const result = await graphEnhancementAPI.rebuildIndex();

      if (result.success) {
        message.success('索引重建成功');
        loadStatus();
      } else {
        message.error(result.message || '索引重建失败');
      }
    } catch (error) {
      message.error('索引重建失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClearData = async () => {
    Modal.confirm({
      title: '确认清空数据',
      content: '此操作将清空所有图谱数据，无法恢复。确定要继续吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          const result = await graphEnhancementAPI.clearGraph();

          if (result.success) {
            message.success('数据清空成功');
            loadStatus();
          } else {
            message.error(result.message || '数据清空失败');
          }
        } catch (error) {
          message.error('数据清空失败: ' + error.message);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  const renderFrameworkDescription = (framework) => {
    const descriptions = {
      lightrag: '简单快速，支持多种查询模式，适合快速部署',
      graphiti: '时序感知，企业级功能，适合复杂场景',
      graphrag: '微软方案，适合文档分析'
    };
    return descriptions[framework] || '';
  };

  const renderFrameworkConfig = (framework) => {
    switch (framework) {
      case 'lightrag':
        return (
          <Card
            title="LightRAG 配置"
            style={{ marginBottom: 24 }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name={['framework_config', 'embedding_model']} label="嵌入模型">
                  <Input placeholder="text-embedding-ada-002" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name={['framework_config', 'llm_model']} label="LLM模型">
                  <Input placeholder="gpt-4" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name={['framework_config', 'chunk_size']} label="文档块大小">
                  <InputNumber min={100} max={2000} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name={['framework_config', 'chunk_overlap']} label="文档块重叠">
                  <InputNumber min={0} max={500} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      case 'graphiti':
        return (
          <Card
            title="Graphiti 配置"
            style={{ marginBottom: 24 }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name={['framework_config', 'db_type']} label="图数据库类型">
                  <Radio.Group>
                    <Radio.Button value="neo4j">Neo4j</Radio.Button>
                    <Radio.Button value="memgraph">Memgraph</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name={['framework_config', 'connection_url']} label="连接地址">
                  <Input placeholder="bolt://localhost:7687" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name={['framework_config', 'username']} label="用户名">
                  <Input placeholder="neo4j" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name={['framework_config', 'password']} label="密码">
                  <Input.Password placeholder="请输入密码" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name={['framework_config', 'embedding_model']} label="嵌入模型">
                  <Input placeholder="text-embedding-ada-002" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name={['framework_config', 'llm_model']} label="LLM模型">
                  <Input placeholder="gpt-4" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      case 'graphrag':
        return (
          <Card
            title="GraphRAG 配置"
            style={{ marginBottom: 24 }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name={['framework_config', 'data_dir']} label="数据目录">
                  <Input placeholder="./graphrag_data" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name={['framework_config', 'storage_type']} label="存储类型">
                  <Radio.Group>
                    <Radio.Button value="file">文件</Radio.Button>
                    <Radio.Button value="azure_blob">Azure Blob</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name={['framework_config', 'embedding_model']} label="嵌入模型">
                  <Input placeholder="text-embedding-ada-002" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name={['framework_config', 'llm_model']} label="LLM模型">
                  <Input placeholder="gpt-4" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name={['framework_config', 'community_level']} label="社区层级">
                  <InputNumber min={0} max={5} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      default:
        return null;
    }
  };

  const renderStatusTag = (status) => {
    const statusConfig = {
      ready: { color: 'green', text: '就绪' },
      disabled: { color: 'default', text: '未启用' },
      not_initialized: { color: 'orange', text: '未初始化' },
      error: { color: 'red', text: '错误' },
      not_implemented: { color: 'blue', text: '未实现' }
    };

    const config = statusConfig[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  if (loading && !config) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="graph-enhancement-container">
      {/* 顶部开关区域 */}
      <Card
        title={
          <Space>
            <ShareAltOutlined />
            图谱增强
          </Space>
        }
        style={{ marginBottom: 24 }}
      >
        <Row align="middle" gutter={16}>
          <Col>
            <Switch
              checked={localEnabled}
              checkedChildren="启用"
              unCheckedChildren="禁用"
              onChange={(checked) => {
                setLocalEnabled(checked);
              }}
            />
          </Col>
          <Col flex={1}>
            <Text type="secondary">
              图谱增强可以通过知识图谱技术提升检索准确性和上下文理解能力
            </Text>
          </Col>
        </Row>
      </Card>

      {localEnabled && (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveConfig}
          initialValues={config}
        >
          {/* 框架选择区域 */}
          <Card
            title={
              <Space>
                <SettingOutlined />
                RAG框架选择
              </Space>
            }
            style={{ marginBottom: 24 }}
          >
            <Form.Item name="framework" label="选择框架">
              <Radio.Group onChange={(e) => setSelectedFramework(e.target.value)}>
                <Space direction="vertical">
                  <Radio value="lightrag">
                    <Space>
                      <ThunderboltOutlined />
                      <strong>LightRAG</strong>
                      <Text type="secondary">
                        {renderFrameworkDescription('lightrag')}
                      </Text>
                    </Space>
                  </Radio>
                  <Radio value="graphiti">
                    <Space>
                      <ShareAltOutlined />
                      <strong>Graphiti</strong>
                      <Text type="secondary">
                        {renderFrameworkDescription('graphiti')}
                      </Text>
                    </Space>
                  </Radio>
                  <Radio value="graphrag">
                    <Space>
                      <BarChartOutlined />
                      <strong>GraphRAG</strong>
                      <Text type="secondary">
                        {renderFrameworkDescription('graphrag')}
                      </Text>
                    </Space>
                  </Radio>
                </Space>
              </Radio.Group>
            </Form.Item>
          </Card>

          {/* 基础配置 */}
          <Card
            title="基础配置"
            style={{ marginBottom: 24 }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="配置名称"
                  rules={[{ required: true, message: '请输入配置名称' }]}
                >
                  <Input placeholder="请输入配置名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="description" label="描述信息">
                  <Input placeholder="请输入描述信息" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="working_dir" label="工作目录">
                  <Input placeholder="留空使用默认目录" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="default_query_mode" label="默认查询模式">
                  <Radio.Group>
                    <Radio.Button value="hybrid">Hybrid</Radio.Button>
                    <Radio.Button value="local">Local</Radio.Button>
                    <Radio.Button value="global">Global</Radio.Button>
                    <Radio.Button value="mix">Mix</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="top_k"
                  label={
                    <Space>
                      Top-K
                      <Tooltip title="检索的实体或关系数量">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                >
                  <InputNumber min={1} max={200} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="chunk_top_k"
                  label={
                    <Space>
                      Chunk Top-K
                      <Tooltip title="检索的文档块数量">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                >
                  <InputNumber min={1} max={50} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="llm_config" label="LLM配置">
                  <Radio.Group>
                    <Radio.Button value="inherit">继承系统</Radio.Button>
                    <Radio.Button value="custom">自定义</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 框架特定配置 */}
          {renderFrameworkConfig(selectedFramework)}

          {/* 高级配置 */}
          <Card style={{ marginBottom: 24 }}>
            <Collapse
              ghost
              items={[
                {
                  key: 'advanced',
                  label: '高级配置',
                  children: (
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="max_entity_tokens"
                          label="最大实体Token"
                        >
                          <InputNumber min={1000} max={50000} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="max_relation_tokens"
                          label="最大关系Token"
                        >
                          <InputNumber min={1000} max={50000} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                  )
                }
              ]}
            />
          </Card>

          {/* 操作按钮 */}
          <Card>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                保存配置
              </Button>
              <Button
                onClick={handleTestConnection}
                loading={testLoading}
              >
                测试连接
              </Button>
              <Button
                onClick={() => setQueryModalVisible(true)}
                icon={<SearchOutlined />}
              >
                测试查询
              </Button>
              <Button
                onClick={handleRebuildIndex}
                icon={<ReloadOutlined />}
                loading={loading}
              >
                重建索引
              </Button>
              <Button
                onClick={handleClearData}
                icon={<ClearOutlined />}
                danger
              >
                清空数据
              </Button>
            </Space>
          </Card>
        </Form>
      )}

      {/* 状态监控 */}
      {localEnabled && status && (
        <Card
          title="图谱状态"
          style={{ marginTop: 24 }}
          extra={
            <Button
              size="small"
              onClick={loadStatus}
              icon={<ReloadOutlined />}
            >
              刷新
            </Button>
          }
        >
          <Row gutter={16}>
            <Col span={6}>
              <div>
                <Text strong>连接状态:</Text>{' '}
                {renderStatusTag(status.status)}
              </div>
            </Col>
            <Col span={6}>
              <div>
                <Text strong>框架:</Text> {status.framework}
              </div>
            </Col>
            <Col span={6}>
              <div>
                <Text strong>实体数量:</Text> {status.statistics?.entity_count || 0}
              </div>
            </Col>
            <Col span={6}>
              <div>
                <Text strong>关系数量:</Text> {status.statistics?.relation_count || 0}
              </div>
            </Col>
          </Row>
        </Card>
      )}

      {/* 测试查询Modal */}
      <TestQueryModal
        visible={queryModalVisible}
        onCancel={() => setQueryModalVisible(false)}
        onQuery={handleTestQuery}
        loading={testLoading}
        result={testResult}
        config={config}
      />
    </div>
  );
};

// 测试查询Modal组件
const TestQueryModal = ({ visible, onCancel, onQuery, loading, result, config }) => {
  const [queryForm] = Form.useForm();

  const handleQuery = () => {
    queryForm.validateFields().then(values => {
      onQuery(values);
    });
  };

  return (
    <Modal
      title="图谱查询测试"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
        <Button
          key="query"
          type="primary"
          onClick={handleQuery}
          loading={loading}
        >
          执行查询
        </Button>
      ]}
    >
      <Form
        form={queryForm}
        layout="vertical"
        initialValues={{
          mode: config?.default_query_mode || 'hybrid',
          top_k: config?.top_k || 60,
          chunk_top_k: config?.chunk_top_k || 10,
          response_type: 'Multiple Paragraphs'
        }}
      >
        <Form.Item
          name="query"
          label="查询内容"
          rules={[{ required: true, message: '请输入查询内容' }]}
        >
          <TextArea
            rows={3}
            placeholder="请输入要查询的内容..."
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="mode" label="查询模式">
              <Radio.Group size="small">
                <Radio.Button value="hybrid">Hybrid</Radio.Button>
                <Radio.Button value="local">Local</Radio.Button>
                <Radio.Button value="global">Global</Radio.Button>
                <Radio.Button value="mix">Mix</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="top_k" label="Top-K">
              <InputNumber min={1} max={200} size="small" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="response_type" label="响应类型">
              <Radio.Group size="small">
                <Radio.Button value="Multiple Paragraphs">多段落</Radio.Button>
                <Radio.Button value="Single Paragraph">单段落</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      {result && (
        <div style={{ marginTop: 16 }}>
          <Divider>查询结果</Divider>
          <Alert
            message={
              <Space>
                <Text strong>响应时间:</Text> {result.response_time?.toFixed(2)}s
                <Text strong>查询模式:</Text> {result.query_params?.mode}
                <Text strong>框架:</Text> {result.framework}
              </Space>
            }
            type="info"
            style={{ marginBottom: 16 }}
          />
          <Card size="small">
            <Paragraph>
              {result.result}
            </Paragraph>
          </Card>
        </div>
      )}
    </Modal>
  );
};

export default GraphEnhancement;
