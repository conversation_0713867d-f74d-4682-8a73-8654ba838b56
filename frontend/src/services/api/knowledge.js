import api from './axios';

const knowledgeAPI = {
  // 获取所有知识库
  getAll: async () => {
    const response = await api.get('/knowledges');
    return response.data;
  },

  // 获取知识库详情
  getById: async (id) => {
    const response = await api.get(`/knowledges/${id}`);
    return response.data;
  },

  // 创建知识库
  create: async (data) => {
    const response = await api.post('/knowledges', data);
    return response.data;
  },

  // 更新知识库
  update: async (id, data) => {
    const response = await api.put(`/knowledges/${id}`, data);
    return response.data;
  },

  // 删除知识库
  delete: async (id) => {
    const response = await api.delete(`/knowledges/${id}`);
    return response.data;
  },

  // 获取角色挂载的知识库
  getRoleKnowledges: async (roleId) => {
    const response = await api.get(`/roles/${roleId}/knowledges`);
    return response.data;
  },

  // 为角色挂载知识库
  mountToRole: async (roleId, knowledgeId) => {
    const response = await api.post(`/roles/${roleId}/knowledges/${knowledgeId}`);
    return response.data;
  },

  // 为角色解除知识库
  unmountFromRole: async (roleId, knowledgeId) => {
    const response = await api.delete(`/roles/${roleId}/knowledges/${knowledgeId}`);
    return response.data;
  },

  // 文件管理
  getAllFiles: async () => {
    const response = await api.get('/knowledges/files');
    return response.data;
  },

  getFiles: async (knowledgeId) => {
    const response = await api.get(`/knowledges/${knowledgeId}/files`);
    return response.data;
  },

  uploadFile: async (knowledgeId, formData) => {
    const response = await api.post(`/knowledges/${knowledgeId}/files`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  deleteFile: async (knowledgeId, filename) => {
    const response = await api.delete(`/knowledges/${knowledgeId}/files/${filename}`);
    return response.data;
  },

  getFileContent: async (knowledgeId, filename) => {
    const response = await api.get(`/knowledges/${knowledgeId}/files/${filename}/content`);
    return response.data;
  },

  // 搜索
  search: async (knowledgeId, query) => {
    const response = await api.post(`/knowledges/${knowledgeId}/search`, { query });
    return response.data;
  },

  // 向量化处理
  vectorize: async (knowledgeId) => {
    const response = await api.post(`/knowledges/${knowledgeId}/vectorize`);
    return response.data;
  },
};

export default knowledgeAPI;