import React, { createContext, useState, useContext, useEffect } from 'react';
import { authAPI } from '../services/api/auth';

// 创建认证上下文
const AuthContext = createContext(null);

/**
 * 认证上下文提供者组件
 * 管理全局认证状态
 */
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // 初始化时检查登录状态
  useEffect(() => {
    // 添加标志，防止重复初始化
    let isMounted = true;
    console.log('AuthContext: 初始化认证状态');

    const initAuth = async () => {
      const token = localStorage.getItem('authToken');
      console.log('AuthContext: 检查token:', !!token);

      if (token) {
        try {
          // 先设置为已认证状态，避免闪烁，同时立即结束loading状态
          if (isMounted) {
            setIsAuthenticated(true);
            setLoading(false); // 立即结束loading，让用户看到界面
          }

          // 在后台静默验证token有效性
          console.log('AuthContext: 静默验证token有效性');
          const isValid = await authAPI.validateToken();
          console.log('AuthContext: token验证结果:', isValid);

          if (isValid) {
            // 获取用户信息
            console.log('AuthContext: 获取用户信息');
            const userResult = await authAPI.getCurrentUser();
            console.log('AuthContext: 用户信息结果:', userResult);

            // 只有当组件仍然挂载时才更新状态
            if (isMounted) {
              if (userResult.success) {
                setUser(userResult.user);
                console.log('AuthContext: 认证成功，用户:', userResult.user);
              } else {
                // 获取用户信息失败，清除token并重定向
                console.log('AuthContext: 获取用户信息失败');
                localStorage.removeItem('authToken');
                setIsAuthenticated(false);
                // 强制刷新页面到登录页
                window.location.href = '/login';
              }
            }
          } else {
            // token无效，清除token并重定向
            console.log('AuthContext: token无效');
            if (isMounted) {
              localStorage.removeItem('authToken');
              setIsAuthenticated(false);
              // 强制刷新页面到登录页
              window.location.href = '/login';
            }
          }
        } catch (error) {
          console.error('AuthContext: 初始化认证状态失败:', error);
          if (isMounted) {
            localStorage.removeItem('authToken');
            setIsAuthenticated(false);
            // 强制刷新页面到登录页
            window.location.href = '/login';
          }
        }
      } else {
        console.log('AuthContext: 无token，未认证');
        if (isMounted) {
          setIsAuthenticated(false);
          setLoading(false);
        }
      }
    };

    initAuth();

    // 清理函数，防止内存泄漏
    return () => {
      console.log('AuthContext: 清理');
      isMounted = false;
    };
  }, []);

  // 登录方法
  const login = async (username, password) => {
    console.log('AuthContext: 开始登录');
    setLoading(true);
    try {
      const result = await authAPI.login(username, password);
      console.log('AuthContext: 登录结果:', result);

      if (result.success) {
        // 使用Promise来确保状态更新后再返回结果
        return new Promise(resolve => {
          localStorage.setItem('authToken', result.token);
          setUser(result.user);
          setIsAuthenticated(true);
          console.log('AuthContext: 登录成功，设置认证状态为true');

          // 使用setTimeout确保状态已更新
          setTimeout(() => {
            console.log('AuthContext: 状态更新后的认证状态:', isAuthenticated);
            setLoading(false);
            resolve({ success: true });
          }, 50);
        });
      } else {
        console.log('AuthContext: 登录失败:', result.message);
        setLoading(false);
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('AuthContext: 登录请求失败:', error);
      setLoading(false);
      return {
        success: false,
        message: error.response?.data?.message || '登录失败，请稍后再试'
      };
    }
  };

  // 登出方法
  const logout = async () => {
    setLoading(true);
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('登出失败:', error);
    } finally {
      // 无论API是否成功，都清除本地状态
      localStorage.removeItem('authToken');
      setUser(null);
      setIsAuthenticated(false);
      setLoading(false);
    }
  };

  // 刷新用户信息
  const refreshUser = async () => {
    console.log('AuthContext: 刷新用户信息, 当前认证状态:', isAuthenticated);

    try {
      // 即使未认证也尝试获取用户信息，用于恢复认证状态
      const result = await authAPI.getCurrentUser();
      console.log('AuthContext: 刷新用户信息结果:', result);

      if (result.success) {
        // 使用Promise来确保状态更新后再返回结果
        return new Promise(resolve => {
          setUser(result.user);
          setIsAuthenticated(true);
          console.log('AuthContext: 用户信息刷新成功，设置认证状态为true');

          // 使用setTimeout确保状态已更新
          setTimeout(() => {
            console.log('AuthContext: 状态更新后的认证状态:', isAuthenticated);
            resolve(true);
          }, 50);
        });
      } else {
        // 如果获取失败且当前已认证，则设置为未认证
        if (isAuthenticated) {
          console.log('AuthContext: 用户信息刷新失败，设置认证状态为false');
          setIsAuthenticated(false);
          localStorage.removeItem('authToken');
        }
        return false;
      }
    } catch (error) {
      console.error('AuthContext: 刷新用户信息失败:', error);
      // 如果出错且当前已认证，则设置为未认证
      if (isAuthenticated) {
        console.log('AuthContext: 刷新用户信息出错，设置认证状态为false');
        setIsAuthenticated(false);
        localStorage.removeItem('authToken');
      }
      return false;
    }
  };

  // 提供的上下文值
  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    refreshUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// 自定义钩子，方便使用认证上下文
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth必须在AuthProvider内部使用');
  }
  return context;
};

export default AuthContext;
