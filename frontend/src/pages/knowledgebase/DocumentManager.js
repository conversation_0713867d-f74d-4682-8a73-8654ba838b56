import React, { useState, useEffect } from 'react';
import { Table, Button, Space, message, Typography, Tag, Tooltip, Select, Input, Progress, Modal } from 'antd';
import { UploadOutlined, DeleteOutlined, EyeOutlined, SearchOutlined, DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import knowledgeAPI from '../../services/api/knowledge';
import BatchUploadDialog from '../../components/BatchUploadDialog';
import { getFileIcon } from '../../utils/fileUtils';

const { Title, Text } = Typography;
const { Option } = Select;


const DocumentManager = () => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [fileTypeFilter, setFileTypeFilter] = useState('all');
  const [selectedKnowledgeId, setSelectedKnowledgeId] = useState(0);
  const [knowledges, setKnowledges] = useState([]);
  const [uploadDialogVisible, setUploadDialogVisible] = useState(false);

  // 获取知识库列表
  const fetchKnowledges = async () => {
    try {
      const response = await knowledgeAPI.getAll();
      if (response.success) {
        setKnowledges(response.data);
      } else {
        message.error(response.message || '获取知识库列表失败');
      }
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      message.error('获取知识库列表失败');
    }
  };

  // 初始化时获取知识库列表
  useEffect(() => {
    fetchKnowledges();
  }, []);

  // 当选择的知识库改变时，获取对应的文档列表
  useEffect(() => {
    fetchDocuments(selectedKnowledgeId);
  }, [selectedKnowledgeId]);

  const fetchDocuments = async (id) => {
    try {
      setLoading(true);
      let response;

      if (!id || id === 0) {
        // 获取所有知识库的文件
        response = await knowledgeAPI.getAllFiles();
      } else {
        // 获取指定知识库的文件
        response = await knowledgeAPI.getFiles(id);
      }

      if (response.success) {
        setDocuments(response.data);
      } else {
        message.error(response.message || '获取文件列表失败');
        setDocuments([]);
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      message.error('获取文件列表失败');
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理知识库选择变化
  const handleKnowledgeChange = (value) => {
    setSelectedKnowledgeId(value);
  };

  // 处理上传对话框打开
  const handleOpenUploadDialog = () => {
    setUploadDialogVisible(true);
  };

  // 处理上传对话框关闭
  const handleCloseUploadDialog = () => {
    setUploadDialogVisible(false);
  };

  // 处理上传完成
  const handleUploadComplete = (results) => {
    // 刷新文档列表
    fetchDocuments(selectedKnowledgeId);

    // 显示上传结果消息
    const successCount = results.filter(r => r.success).length;
    const failCount = results.filter(r => !r.success).length;

    if (successCount > 0) {
      message.success(`成功上传 ${successCount} 个文件`);
    }
    if (failCount > 0) {
      message.error(`${failCount} 个文件上传失败`);
    }

    // 自动关闭上传对话框
    setUploadDialogVisible(false);
  };



  // 处理删除文档
  const handleDelete = (record) => {
    let knowledgeId, filename;

    if (selectedKnowledgeId === 0) {
      // 显示所有知识库时，从record中获取知识库ID和文件名
      knowledgeId = record.knowledge_id;
      filename = record.name;
    } else {
      // 显示单个知识库时，使用选中的知识库ID
      knowledgeId = selectedKnowledgeId;
      filename = record.name || record; // 兼容旧的调用方式
    }

    if (!knowledgeId) {
      message.error('无法确定文件所属的知识库');
      return;
    }

    Modal.confirm({
      title: '确认删除文档',
      content: `确定要删除文档 "${filename}" 吗？删除后无法恢复。`,
      okText: '确定删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await knowledgeAPI.deleteFile(knowledgeId, filename);
          if (response.success) {
            message.success('文档删除成功');
            fetchDocuments(selectedKnowledgeId); // 重新获取文件列表
          } else {
            message.error(response.message || '删除文档失败');
          }
        } catch (error) {
          console.error('删除文档失败:', error);
          message.error('删除文档失败');
        }
      }
    });
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
  };

  // 处理文件类型筛选
  const handleFileTypeChange = (value) => {
    setFileTypeFilter(value);
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchDocuments(selectedKnowledgeId);
    fetchKnowledges(); // 同时刷新知识库列表
  };



  // 过滤文档
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = searchText ? doc.name.toLowerCase().includes(searchText.toLowerCase()) : true;
    const matchesType = fileTypeFilter !== 'all' ? doc.type === fileTypeFilter : true;
    return matchesSearch && matchesType;
  });

  // 动态生成列定义，当显示所有知识库时添加"所属知识库"列
  const getColumns = () => {
    const baseColumns = [
      {
        title: '文件名',
        dataIndex: 'name',
        key: 'name',
        render: (text, record) => (
          <Space>
            {getFileIcon(record.name)}
            <span>{text}</span>
          </Space>
        ),
      },
    ];

    // 如果显示所有知识库，添加"所属知识库"列
    if (selectedKnowledgeId === 0) {
      baseColumns.push({
        title: '所属知识库',
        dataIndex: 'knowledge_name',
        key: 'knowledge_name',
        width: 150,
        render: (text) => (
          <Tag color="blue">{text}</Tag>
        ),
      });
    }

    // 添加其他列
    baseColumns.push(
      {
        title: '大小',
        dataIndex: 'size',
        key: 'size',
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 120,
        render: (status, record) => {
          if (status === 'indexed') {
            return <Tag color="success">已索引</Tag>;
          } else if (status === 'processing') {
            return (
              <div>
                <Tag color="processing">处理中</Tag>
                <Progress percent={record.progress} size="small" style={{ width: 80 }} />
              </div>
            );
          } else if (status === 'error') {
            return (
              <Tooltip title={record.error}>
                <Tag color="error">错误</Tag>
              </Tooltip>
            );
          }
          return <Tag>未知</Tag>;
        },
      },
      {
        title: '分块数',
        dataIndex: 'chunks',
        key: 'chunks',
        width: 100,
      },
      {
        title: 'Token数',
        dataIndex: 'tokens',
        key: 'tokens',
        width: 100,
      },
      {
        title: '上传时间',
        dataIndex: 'upload_time',
        key: 'upload_time',
        render: (date) => new Date(date).toLocaleString(),
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        render: (_, record) => (
          <Space>
            <Tooltip title="预览文档">
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => message.info('预览功能开发中')}
              />
            </Tooltip>
            <Tooltip title="删除文档">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record)}
              />
            </Tooltip>
          </Space>
        ),
      }
    );

    return baseColumns;
  };



  return (
    <div>
      <div style={{ marginBottom: '16px' }}>
        <Title level={5}>文档管理</Title>
      </div>

      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Input
            placeholder="搜索文件名"
            allowClear
            onChange={(e) => handleSearch(e.target.value)}
            style={{ width: 200 }}
          />
          <Button icon={<SearchOutlined />} style={{ marginLeft: '8px' }}>搜索</Button>
          <Button icon={<ReloadOutlined />} onClick={handleRefresh} title="刷新">刷新</Button>
          <Select
            defaultValue="all"
            style={{ width: 120 }}
            onChange={handleFileTypeChange}
          >
            <Option value="all">所有类型</Option>
            <Option value="pdf">PDF</Option>
            <Option value="docx">Word</Option>
            <Option value="xlsx">Excel</Option>
            <Option value="md">Markdown</Option>
          </Select>
          <Select
            value={selectedKnowledgeId}
            onChange={handleKnowledgeChange}
            style={{ width: 180 }}
            placeholder="选择知识库"
          >
            <Option value={0}>所有知识库</Option>
            {knowledges.map((knowledge) => (
              <Option key={knowledge.id} value={knowledge.id}>{knowledge.name}</Option>
            ))}
          </Select>
        </div>

        <Space>
          <Button icon={<DownloadOutlined />}>批量下载</Button>
          <Button 
            type="primary" 
            icon={<UploadOutlined />}
            onClick={handleOpenUploadDialog}
          >
            上传文件
          </Button>
        </Space>
      </div>

      <Table
        columns={getColumns()}
        dataSource={filteredDocuments}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 个文档`,
        }}
      />

      {/* 批量上传对话框 */}
      <BatchUploadDialog
        visible={uploadDialogVisible}
        onClose={handleCloseUploadDialog}
        knowledgeBases={knowledges}
        onUploadComplete={handleUploadComplete}
        defaultKnowledgeBaseId={selectedKnowledgeId !== 0 ? selectedKnowledgeId : null}
      />
    </div>
  );
};

export default DocumentManager;
