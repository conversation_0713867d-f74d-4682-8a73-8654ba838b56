const { createProxyMiddleware } = require('http-proxy-middleware');
require('dotenv').config();

module.exports = function(app) {
  // 配置代理 - 使用环境变量获取API URL
  const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:8080/api';
  const targetUrl = apiUrl.replace('/api', ''); // 移除路径中的/api，因为在代理中会添加
  
  app.use(
    '/api',
    createProxyMiddleware({
      target: targetUrl,
      changeOrigin: true,
      pathRewrite: {
        '^/api': '/api' // 保持API路径不变
      },
      logLevel: 'debug' // 添加调试日志
    })
  );
  
  // 配置Webpack开发服务器
  app.use(function(req, res, next) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    next();
  });
}; 