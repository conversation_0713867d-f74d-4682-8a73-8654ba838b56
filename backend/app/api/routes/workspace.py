"""
项目空间API路由

处理与项目空间文件相关的所有API请求，包括项目空间管理功能
"""
import os
from flask import Blueprint, jsonify, request
from app.services.workspace_service import workspace_service
from app.models import ActionTask, ActionTaskAgent, Agent

# 创建Blueprint
workspace_bp = Blueprint('workspace_api', __name__)

@workspace_bp.route('/action-tasks/<int:task_id>/workspace-files', methods=['GET'])
@workspace_bp.route('/action-tasks/<int:task_id>/workspace-files/<path:sub_path>', methods=['GET'])
def get_workspace_files(task_id, sub_path=''):
    """获取行动任务的所有项目文件列表，支持子目录浏览"""
    try:
        # 首先验证任务是否存在
        task = ActionTask.query.get(task_id)
        if not task:
            return jsonify({'error': f'行动任务 {task_id} 不存在'}), 404

        # 纯粹的文件浏览器，不需要验证智能体权限

        # 获取项目空间目录路径
        task_dir = os.path.join(workspace_service.workspace_dir, f'ActionTask-{task_id}')

        # 如果有子路径，则进入子目录
        if sub_path:
            current_dir = os.path.join(task_dir, sub_path)
            current_path_prefix = f'ActionTask-{task_id}/{sub_path}'
        else:
            current_dir = task_dir
            current_path_prefix = f'ActionTask-{task_id}'

        # 检查目录是否存在
        if not os.path.exists(current_dir):
            return jsonify({'error': f'目录不存在: {current_path_prefix}'}), 404

        # 标准文件浏览器实现 - 只返回当前目录的直接子项
        items = []
        try:
            for item in os.listdir(current_dir):
                item_path = os.path.join(current_dir, item)

                # 跳过隐藏文件
                if item.startswith('.'):
                    continue

                if os.path.isfile(item_path):
                    # 文件
                    items.append({
                        'file_name': item,
                        'file_path': f'{current_path_prefix}/{item}',
                        'is_directory': False,
                        'size': os.path.getsize(item_path),
                        'modified_time': os.path.getmtime(item_path)
                    })
                elif os.path.isdir(item_path):
                    # 目录
                    items.append({
                        'file_name': item,
                        'file_path': f'{current_path_prefix}/{item}',
                        'is_directory': True,
                        'size': 0,
                        'modified_time': os.path.getmtime(item_path)
                    })
        except PermissionError:
            return jsonify({'error': f'没有权限访问目录: {current_path_prefix}'}), 403
        except Exception as e:
            return jsonify({'error': f'读取目录失败: {str(e)}'}), 500

        # 对items进行排序：目录在前，文件在后，同类型按名称排序
        items.sort(key=lambda x: (not x['is_directory'], x['file_name'].lower()))

        # 构建结果
        result = {
            'items': items,
            'current_path': sub_path
        }

        return jsonify(result)

    except Exception as e:
        return jsonify({'error': f'获取项目文件列表失败: {str(e)}'}), 500

@workspace_bp.route('/workspace-files/<path:file_path>', methods=['GET'])
def get_workspace_file_content(file_path):
    """获取项目文件内容"""
    try:
        # 构建完整的文件路径
        full_path = os.path.join(workspace_service.workspace_dir, file_path)

        # 检查文件是否存在
        if not os.path.exists(full_path):
            return jsonify({'error': f'项目文件 {file_path} 不存在'}), 404

        # 读取文件内容
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 构建结果
        result = {
            'file_path': file_path,
            'content': content
        }

        return jsonify(result)

    except Exception as e:
        return jsonify({'error': f'获取项目文件内容失败: {str(e)}'}), 500


# ==================== 项目空间管理API ====================
# 以下是专门为项目空间管理页面提供的优化API接口

@workspace_bp.route('/workspace-management/tasks-with-agents', methods=['GET'])
def get_tasks_with_agents_for_workspace():
    """
    获取所有行动任务及其智能体信息，专门用于项目空间管理页面
    优化版本，减少API请求次数
    """
    try:
        # 获取所有行动任务
        action_tasks = ActionTask.query.all()
        result = []

        for task in action_tasks:
            # 获取任务的智能体
            task_agents = ActionTaskAgent.query.filter_by(action_task_id=task.id).all()
            agents = []

            for ta in task_agents:
                agent = Agent.query.get(ta.agent_id)
                if agent:
                    agents.append({
                        'id': agent.id,
                        'name': agent.name,
                        'description': agent.description,
                        'avatar': agent.avatar,
                        'is_default': ta.is_default,
                        'is_observer': agent.is_observer,
                        'type': agent.type
                    })

            # 检查是否有项目文件
            has_shared_files = False
            agent_workspace_count = 0

            try:
                workspace_files = workspace_service.get_workspace_files_for_task(task.id)
                has_shared_files = len(workspace_files.get('shared_files', [])) > 0
                agent_workspace_count = len(workspace_files.get('agent_workspaces', []))
            except Exception as e:
                print(f"获取任务 {task.id} 项目文件失败: {e}")

            task_data = {
                'id': task.id,
                'title': task.title,
                'description': task.description,
                'status': task.status,
                'mode': task.mode,
                'created_at': task.created_at.isoformat() if task.created_at else None,
                'updated_at': task.updated_at.isoformat() if task.updated_at else None,
                'agents': agents,
                'agent_count': len(agents),
                'has_shared_workspace': has_shared_workspace,
                'agent_workspace_count': agent_workspace_count
            }

            result.append(task_data)

        return jsonify({
            'success': True,
            'tasks': result,
            'total_count': len(result)
        })

    except Exception as e:
        print(f"获取任务和智能体信息失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取任务和智能体信息失败: {str(e)}'
        }), 500

@workspace_bp.route('/workspace-management/task/<int:task_id>/workspaces', methods=['GET'])
def get_task_workspaces(task_id):
    """
    获取指定任务的所有项目空间信息
    包括共享工作区和所有智能体的工作区
    """
    try:
        # 验证任务是否存在
        task = ActionTask.query.get(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': '行动任务未找到'
            }), 404

        # 获取项目文件
        workspace_files = workspace_service.get_workspace_files_for_task(task_id)

        # 获取任务的智能体信息
        task_agents = ActionTaskAgent.query.filter_by(action_task_id=task_id).all()
        agents_info = {}

        for ta in task_agents:
            agent = Agent.query.get(ta.agent_id)
            if agent:
                agents_info[agent.id] = {
                    'id': agent.id,
                    'name': agent.name,
                    'description': agent.description,
                    'avatar': agent.avatar,
                    'is_default': ta.is_default,
                    'is_observer': agent.is_observer,
                    'type': agent.type
                }

        # 组织返回数据
        result = {
            'task_id': task_id,
            'task_title': task.title,
            'shared_files': workspace_files.get('shared_files', []),
            'agent_workspaces': workspace_files.get('agent_workspaces', []),
            'agents_info': agents_info
        }

        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        print(f"获取任务 {task_id} 项目空间信息失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取项目空间信息失败: {str(e)}'
        }), 500

@workspace_bp.route('/workspace-management/workspace-file/<path:file_path>', methods=['GET'])
def get_workspace_file_content_v2(file_path):
    """
    获取项目文件内容（项目空间管理版本）
    """
    try:
        content = workspace_service.get_workspace_file_content(file_path)
        return jsonify({
            'success': True,
            'content': content,
            'file_path': file_path
        })
    except Exception as e:
        print(f"获取项目文件内容失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取项目文件内容失败: {str(e)}'
        }), 500

@workspace_bp.route('/workspace-management/workspace-file/<path:file_path>', methods=['PUT'])
def update_workspace_file_content(file_path):
    """
    更新项目文件内容
    """
    try:
        data = request.get_json()
        content = data.get('content', '')

        # 更新文件内容
        workspace_service.update_workspace_file_content(file_path, content)

        return jsonify({
            'success': True,
            'message': '项目文件更新成功',
            'file_path': file_path
        })
    except Exception as e:
        print(f"更新项目文件内容失败: {e}")
        return jsonify({
            'success': False,
            'error': f'更新项目文件内容失败: {str(e)}'
        }), 500

@workspace_bp.route('/workspace-management/workspace-file', methods=['POST'])
def create_workspace_file():
    """
    创建新的项目文件
    """
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        agent_id = data.get('agent_id')  # 可选，如果是共享记忆则为None
        title = data.get('title', '未命名记忆')
        content = data.get('content', '')
        memory_type = data.get('type', 'agent')  # 'agent' 或 'shared'

        # 创建项目文件
        file_path = workspace_service.create_workspace_file(
            task_id=task_id,
            agent_id=agent_id,
            title=title,
            content=content,
            file_type=memory_type
        )

        return jsonify({
            'success': True,
            'message': '项目文件创建成功',
            'file_path': file_path
        })
    except Exception as e:
        print(f"创建项目文件失败: {e}")
        return jsonify({
            'success': False,
            'error': f'创建项目文件失败: {str(e)}'
        }), 500

@workspace_bp.route('/workspace-management/workspace-file/<path:file_path>', methods=['DELETE'])
def delete_workspace_file(file_path):
    """
    删除项目文件
    """
    try:
        workspace_service.delete_workspace_file(file_path)

        return jsonify({
            'success': True,
            'message': '记忆文件删除成功',
            'file_path': file_path
        })
    except Exception as e:
        print(f"删除记忆文件失败: {e}")
        return jsonify({
            'success': False,
            'error': f'删除项目文件失败: {str(e)}'
        }), 500

@workspace_bp.route('/workspace-management/workspace-template', methods=['POST'])
def create_workspace_template():
    """
    从项目文件创建模板
    """
    try:
        data = request.get_json()
        source_file_path = data.get('source_file_path')
        template_name = data.get('template_name')
        template_description = data.get('template_description', '')

        # 创建模板
        template_path = workspace_service.create_workspace_template(
            source_file_path=source_file_path,
            template_name=template_name,
            template_description=template_description
        )

        return jsonify({
            'success': True,
            'message': '工作空间模板创建成功',
            'template_path': template_path
        })
    except Exception as e:
        print(f"创建工作空间模板失败: {e}")
        return jsonify({
            'success': False,
            'error': f'创建工作空间模板失败: {str(e)}'
        }), 500

@workspace_bp.route('/workspace-management/workspace-templates', methods=['GET'])
def get_workspace_templates():
    """
    获取工作空间模板列表
    """
    try:
        templates = workspace_service.get_workspace_templates()
        return jsonify({
            'success': True,
            'templates': templates
        })
    except Exception as e:
        print(f"获取记忆模板列表失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取工作空间模板列表失败: {str(e)}'
        }), 500

@workspace_bp.route('/workspace-management/workspace-template/<int:template_id>', methods=['PUT'])
def update_workspace_template(template_id):
    """
    更新工作空间模板
    """
    try:
        data = request.get_json()
        template_name = data.get('name')
        template_content = data.get('content')
        template_description = data.get('description', '')
        template_category = data.get('category', 'agent')

        # 更新模板
        updated_template = workspace_service.update_workspace_template(
            template_id=template_id,
            template_name=template_name,
            template_content=template_content,
            template_description=template_description,
            template_category=template_category
        )

        return jsonify({
            'success': True,
            'message': '记忆模板更新成功',
            'template': updated_template
        })
    except Exception as e:
        print(f"更新工作空间模板失败: {e}")
        return jsonify({
            'success': False,
            'error': f'更新工作空间模板失败: {str(e)}'
        }), 500

@workspace_bp.route('/workspace-management/workspace-template/<int:template_id>', methods=['DELETE'])
def delete_workspace_template(template_id):
    """
    删除工作空间模板
    """
    try:
        workspace_service.delete_workspace_template(template_id)

        return jsonify({
            'success': True,
            'message': '工作空间模板删除成功'
        })
    except Exception as e:
        print(f"删除工作空间模板失败: {e}")
        return jsonify({
            'success': False,
            'error': f'删除工作空间模板失败: {str(e)}'
        }), 500

@workspace_bp.route('/workspace-management/workspace-template/new', methods=['POST'])
def create_new_workspace_template():
    """
    创建新的工作空间模板
    """
    try:
        data = request.get_json()
        template_name = data.get('name')
        template_content = data.get('content')
        template_description = data.get('description', '')
        template_category = data.get('category', 'agent')

        if not template_name:
            return jsonify({
                'success': False,
                'error': '模板名称不能为空'
            }), 400

        if not template_content:
            return jsonify({
                'success': False,
                'error': '模板内容不能为空'
            }), 400

        # 创建新模板
        template = workspace_service.create_new_workspace_template(
            template_name=template_name,
            template_content=template_content,
            template_description=template_description,
            template_category=template_category
        )

        return jsonify({
            'success': True,
            'message': '工作空间模板创建成功',
            'template': template
        })
    except Exception as e:
        print(f"创建新工作空间模板失败: {e}")
        return jsonify({
            'success': False,
            'error': f'创建新工作空间模板失败: {str(e)}'
        }), 500
