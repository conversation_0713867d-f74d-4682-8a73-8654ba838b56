# Design Document

## Overview

The Knowledge Upload Dialog is a comprehensive modal component that enhances the file upload experience in the knowledge base management system. It provides a centralized interface for users to select target knowledge bases, upload files and folders through drag-and-drop or file selection, monitor upload progress, and manage upload queues with batch processing capabilities.

The design follows the existing Ant Design patterns used throughout the application and integrates seamlessly with the current DocumentManager component and knowledge API services.

## Architecture

### Component Hierarchy

```
DocumentManager
├── UploadDialog (Modal)
│   ├── KnowledgeBaseSelector
│   ├── FileUploadArea (Dragger)
│   ├── UploadQueue
│   │   ├── UploadQueueItem[]
│   │   │   ├── FileInfo
│   │   │   ├── ProgressBar
│   │   │   ├── StatusIndicator
│   │   │   └── ActionButtons
│   │   └── QueueSummary
│   ├── UploadControls
│   └── UploadSummary
```

### State Management

The component will use React hooks for state management:

- `uploadQueue`: Array of files with metadata and status
- `selectedKnowledgeBase`: Currently selected knowledge base
- `uploadProgress`: Progress tracking for individual files
- `isUploading`: Global upload state
- `uploadStats`: Summary statistics (total, completed, failed)

## Components and Interfaces

### UploadDialog Component

**Props:**
- `visible: boolean` - Controls modal visibility
- `onClose: () => void` - Callback when dialog closes
- `knowledgeBases: KnowledgeBase[]` - Available knowledge bases
- `onUploadComplete: (results: UploadResult[]) => void` - Callback when uploads complete

**State:**
```typescript
interface UploadDialogState {
  selectedKnowledgeBaseId: number | null;
  uploadQueue: UploadQueueItem[];
  isUploading: boolean;
  uploadStats: UploadStats;
}

interface UploadQueueItem {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  status: 'pending' | 'uploading' | 'completed' | 'failed';
  progress: number;
  error?: string;
}

interface UploadStats {
  total: number;
  completed: number;
  failed: number;
  inProgress: number;
}
```

### KnowledgeBaseSelector Component

A dropdown selector that displays available knowledge bases with search functionality.

**Features:**
- Dropdown with knowledge base list
- Search/filter capability
- Visual indicators for knowledge base status
- Empty state handling when no knowledge bases exist

### FileUploadArea Component

A drag-and-drop area built with Ant Design's Dragger component.

**Features:**
- Drag-and-drop file/folder support
- Click to browse file selection
- Visual feedback during drag operations
- File type validation and filtering
- Recursive folder processing

**Implementation:**
```javascript
const uploadProps = {
  name: 'files',
  multiple: true,
  directory: true, // Enable folder upload
  beforeUpload: validateFile,
  customRequest: handleFileUpload,
  showUploadList: false,
  accept: '.txt,.pdf,.doc,.docx,.md,.json,.csv,.xlsx',
};
```

### UploadQueue Component

Displays the list of files queued for upload with individual progress tracking.

**Features:**
- File list with metadata (name, size, type)
- Individual progress bars
- Status indicators (pending, uploading, completed, failed)
- Remove file functionality
- Retry failed uploads
- File type icons

### UploadQueueItem Component

Individual file item in the upload queue.

**Props:**
- `item: UploadQueueItem` - File item data
- `onRemove: (id: string) => void` - Remove file callback
- `onRetry: (id: string) => void` - Retry upload callback

### UploadControls Component

Control buttons for managing the upload process.

**Features:**
- Start/pause upload button
- Clear queue button
- Upload settings (concurrent uploads limit)
- Cancel all uploads button

## Data Models

### File Processing Pipeline

1. **File Selection/Drop**
   - Validate file types against supported formats
   - Check file size limits
   - Generate unique IDs for tracking
   - Add to upload queue

2. **Folder Processing**
   - Recursively traverse folder structure
   - Filter supported file types
   - Flatten file list
   - Maintain relative path information

3. **Upload Processing**
   - Process files in batches (configurable concurrency)
   - Track individual file progress
   - Handle upload failures with retry logic
   - Update queue status in real-time

### API Integration

The component will integrate with the existing `knowledgeAPI` service:

```javascript
// Enhanced upload function with progress tracking
const uploadFileWithProgress = async (knowledgeId, file, onProgress) => {
  const formData = new FormData();
  formData.append('file', file);
  
  return await knowledgeAPI.uploadFile(knowledgeId, formData, {
    onUploadProgress: (progressEvent) => {
      const progress = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress(progress);
    }
  });
};
```

## Error Handling

### File Validation Errors
- Unsupported file types: Show warning with supported formats
- File size exceeded: Display size limit information
- Duplicate files: Offer skip/replace options
- Empty folders: Show informational message

### Upload Errors
- Network failures: Automatic retry with exponential backoff
- Server errors: Display error message with retry option
- Authentication errors: Redirect to login
- Knowledge base access errors: Show permission message

### Error Recovery
- Individual file failures don't stop batch processing
- Failed uploads can be retried individually
- Upload queue persists during temporary network issues
- Graceful degradation when knowledge base is unavailable

## Testing Strategy

### Unit Tests
- File validation logic
- Upload queue management
- Progress calculation
- Error handling scenarios

### Integration Tests
- API integration with knowledge service
- File upload flow end-to-end
- Drag-and-drop functionality
- Modal interaction patterns

### User Acceptance Tests
- Upload dialog workflow
- Batch upload scenarios
- Error recovery flows
- Knowledge base selection

## Performance Considerations

### File Processing
- Lazy loading for large file lists
- Chunked file processing for large files
- Memory management for file previews
- Efficient progress tracking

### Upload Optimization
- Configurable concurrent upload limits (default: 3)
- Request queuing to prevent server overload
- Progress debouncing to reduce re-renders
- Cleanup of completed uploads from memory

### UI Responsiveness
- Virtual scrolling for large upload queues
- Debounced search in knowledge base selector
- Optimistic UI updates for better perceived performance
- Loading states for all async operations

## Security Considerations

### File Validation
- Client-side file type validation
- File size limits enforcement
- Malicious file detection patterns
- Content-type verification

### Upload Security
- CSRF protection for upload requests
- File sanitization on server side
- Virus scanning integration points
- Access control validation

## Accessibility

### Keyboard Navigation
- Full keyboard support for all interactive elements
- Tab order optimization
- Escape key to close dialog
- Enter key for primary actions

### Screen Reader Support
- ARIA labels for all components
- Progress announcements
- Error message accessibility
- File status descriptions

### Visual Accessibility
- High contrast mode support
- Focus indicators
- Color-blind friendly status indicators
- Scalable text and icons