import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, Form, Input, message, Tag, Typography, Tooltip, Card, List, App } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, DatabaseOutlined, CloudDownloadOutlined, UploadOutlined, SearchOutlined } from '@ant-design/icons';
import knowledgeAPI from '../../services/api/knowledge';

const { TextArea } = Input;



const KnowledgeList = () => {
  const { modal } = App.useApp();
  const [knowledges, setKnowledges] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingId, setEditingId] = useState(null);
  const [currentKnowledge, setCurrentKnowledge] = useState(null);
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // 获取知识库列表
  const fetchKnowledges = async () => {
    try {
      setLoading(true);
      const response = await knowledgeAPI.getAll();

      if (response && response.success) {
        setKnowledges(response.data);
      } else {
        message.error(response?.message || '获取知识库列表失败');
      }
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      message.error('获取知识库列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchKnowledges();
  }, []);

  // 处理创建/编辑知识库
  const handleSubmit = async (values) => {
    try {
      if (editingId) {
        // 更新知识库
        const response = await knowledgeAPI.update(editingId, values);
        if (response.success) {
          message.success('知识库更新成功');
          fetchKnowledges(); // 重新获取列表
        } else {
          message.error(response.message || '更新知识库失败');
        }
      } else {
        // 创建知识库
        const response = await knowledgeAPI.create(values);
        if (response.success) {
          message.success('知识库创建成功');
          fetchKnowledges(); // 重新获取列表
        } else {
          message.error(response.message || '创建知识库失败');
        }
      }
      setModalVisible(false);
      form.resetFields();
      setEditingId(null);
    } catch (error) {
      console.error('操作知识库失败:', error);
      message.error(editingId ? '更新知识库失败' : '创建知识库失败');
    }
  };



  // 处理删除知识库
  const handleDelete = (id) => {
    modal.confirm({
      title: '确认删除',
      content: '确定要删除这个知识库吗？删除后无法恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await knowledgeAPI.delete(id);
          if (response.success) {
            message.success('知识库删除成功');
            fetchKnowledges(); // 重新获取列表
          } else {
            message.error(response.message || '删除知识库失败');
          }
        } catch (error) {
          console.error('删除知识库失败:', error);
          message.error('删除知识库失败');
        }
      }
    });
  };

  // 打开创建模态框
  const showCreateModal = () => {
    setEditingId(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑模态框
  const showEditModal = (record) => {
    setEditingId(record.id);
    form.setFieldsValue(record);
    setModalVisible(true);
  };



  // 搜索知识库
  const handleSearch = async (knowledgeId, query) => {
    if (!query.trim()) {
      message.error('请输入搜索内容');
      return;
    }

    try {
      setSearchLoading(true);
      const response = await knowledgeAPI.search(knowledgeId, query);
      if (response.success) {
        setSearchResults(response.data.results);
        setSearchQuery(query);
      } else {
        message.error(response.message || '搜索失败');
      }
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索失败');
    } finally {
      setSearchLoading(false);
    }
  };

  // 显示搜索模态框
  const showSearchModal = (record) => {
    setCurrentKnowledge(record);
    setSearchResults([]);
    setSearchQuery('');
    setSearchModalVisible(true);
  };

  // 处理查看知识库详情
  const handleViewDetails = (record) => {
    message.info(`查看知识库详情: ${record.name}`);
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <DatabaseOutlined style={{ color: '#1677ff' }} />
          <button
            onClick={() => handleViewDetails(record)}
            style={{
              background: 'none',
              border: 'none',
              padding: 0,
              color: '#1677ff',
              textDecoration: 'underline',
              cursor: 'pointer',
              font: 'inherit'
            }}
          >
            {text}
          </button>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },

    {
      title: '文档数',
      dataIndex: 'document_count',
      key: 'document_count',
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Tooltip title="搜索知识库">
            <Button
              type="text"
              icon={<SearchOutlined />}
              onClick={() => showSearchModal(record)}
            />
          </Tooltip>
          <Tooltip title="编辑知识库">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Tooltip title="删除知识库">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '16px' }}>
        <Space>
          <Button
            icon={<CloudDownloadOutlined />}
            onClick={() => message.info('导入第三方功能开发中')}
          >
            导入第三方
          </Button>
          <Button
            icon={<UploadOutlined />}
            onClick={() => message.info('上传文件功能开发中')}
          >
            上传文件
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showCreateModal}
          >
            新建知识库
          </Button>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={knowledges}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`,
        }}
      />

      <Modal
        title={editingId ? '编辑知识库' : '新建知识库'}
        open={modalVisible}
        onOk={form.submit}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入知识库名称' }]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入知识库描述' }]}
          >
            <TextArea rows={4} placeholder="请输入知识库描述" />
          </Form.Item>

        </Form>
      </Modal>



      {/* 搜索模态框 */}
      <Modal
        title={`搜索知识库: ${currentKnowledge?.name || ''}`}
        open={searchModalVisible}
        onCancel={() => setSearchModalVisible(false)}
        footer={null}
        width={800}
      >
        <Space.Compact style={{ width: '100%', marginBottom: 16 }}>
          <Input
            placeholder="输入搜索内容..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onPressEnter={() => currentKnowledge && handleSearch(currentKnowledge.id, searchQuery)}
          />
          <Button
            type="primary"
            icon={<SearchOutlined />}
            loading={searchLoading}
            onClick={() => currentKnowledge && handleSearch(currentKnowledge.id, searchQuery)}
          >
            搜索
          </Button>
        </Space.Compact>

        {searchResults.length > 0 ? (
          <List
            dataSource={searchResults}
            renderItem={(item) => (
              <List.Item>
                <Card size="small" style={{ width: '100%' }}>
                  <div style={{ marginBottom: 8 }}>
                    <Tag color="blue">相似度: {(item.score * 100).toFixed(1)}%</Tag>
                    <Tag color="green">来源: {item.source}</Tag>
                  </div>
                  <Typography.Paragraph
                    ellipsis={{ rows: 3, expandable: true }}
                    style={{ marginBottom: 0 }}
                  >
                    {item.content}
                  </Typography.Paragraph>
                </Card>
              </List.Item>
            )}
          />
        ) : searchQuery && !searchLoading ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Typography.Text type="secondary">未找到相关内容</Typography.Text>
          </div>
        ) : null}
      </Modal>
    </div>
  );
};

export default KnowledgeList;
