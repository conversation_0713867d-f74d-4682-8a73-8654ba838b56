import React, { useState, useEffect } from 'react';
import { Card, Empty, Typography, Spin, message, Button, Space } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { MarkdownRenderer } from './ConversationExtraction';
import { workspaceAPI } from '../../../services/api/workspace';

const { Text } = Typography;

const ActionTaskConclusion = ({ task }) => {
  // 结论数据状态
  const [conclusionData, setConclusionData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 获取任务结论数据
  const fetchConclusionData = async (showMessage = false) => {
    if (!task?.id) return;

    setLoading(true);
    setError(null);

    try {
      // 首先获取项目文件列表，检查项目总结文件是否存在
      const workspaceFiles = await workspaceAPI.getWorkspaceFiles(task.id);

      if (workspaceFiles.project_summary && workspaceFiles.project_summary.exists) {
        // 获取项目总结文件内容
        const conclusionContent = await workspaceAPI.getWorkspaceFileContent(workspaceFiles.project_summary.file_path);
        setConclusionData(conclusionContent);
        if (showMessage) {
          message.success('任务结论已刷新');
        }
      } else {
        setConclusionData(null);
        if (showMessage) {
          message.info('暂无任务结论数据');
        }
      }
    } catch (err) {
      console.error('获取任务结论数据失败:', err);
      setError('获取任务结论数据失败');
      message.error('获取任务结论数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 手动刷新任务结论
  const handleRefresh = () => {
    fetchConclusionData(true);
  };

  // 初始化时获取结论数据
  useEffect(() => {
    console.log('ActionTaskConclusion组件接收到任务数据:', task);
    fetchConclusionData();
  }, [task?.id]); // 只在任务ID变化时重新获取，而不是整个task对象变化时

  // 渲染卡片标题栏的额外操作
  const renderCardExtra = () => (
    <Button
      type="text"
      icon={<ReloadOutlined />}
      onClick={handleRefresh}
      loading={loading}
      size="small"
    />
  );

  // 渲染卡片内容
  const renderCardContent = () => {
    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 8 }}>
            <Text type="secondary">正在加载任务结论...</Text>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <Empty
          description={error}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    if (!conclusionData) {
      return (
        <Empty
          description="暂无任务结论数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <div className="markdown-content">
        <MarkdownRenderer content={conclusionData.content} />
      </div>
    );
  };

  return (
    <Card
      title="任务结论"
      extra={renderCardExtra()}
      style={{ marginBottom: 16 }}
    >
      {renderCardContent()}
    </Card>
  );
};

export default ActionTaskConclusion;