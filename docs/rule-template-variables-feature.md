# 规则模板变量功能实现

## 功能概述

本功能为规则系统添加了模板变量支持，允许在规则中使用 `{{变量名}}` 格式引用内部和外部环境变量，并在规则测试时进行变量替换。

## 实现的功能

### 1. 前端功能

#### 1.1 模板工具函数增强
- **文件**: `frontend/src/utils/templateUtils.js`
- **新增函数**:
  - `formatEnvironmentVariables()`: 格式化环境变量为模板变量对象
  - `getTemplateVariableInfo()`: 获取模板中使用的变量及其描述信息

#### 1.2 规则编辑界面增强
- **文件**: `frontend/src/pages/actionspace/ActionRules.js`
- **新增功能**:
  - 自动获取内部和外部环境变量
  - 实时分析规则内容中使用的变量
  - 显示检测到的模板变量信息
  - 提供可点击的环境变量列表，方便插入到规则中
  - 支持自然语言规则和逻辑规则的变量引用

#### 1.3 规则测试功能增强
- **API调用增强**: 测试规则时自动传递环境变量用于模板替换
- **解释器支持**: 确保逻辑规则测试时正确传递解释器信息

### 2. 后端功能

#### 2.1 API接口增强
- **文件**: `app/api/routes/action_spaces.py`
- **新增接口**: `/environment-variables/internal` - 获取所有内部环境变量

#### 2.2 规则测试API增强
- **文件**: `app/api/routes/rules.py`
- **功能增强**:
  - 支持接收 `variables` 参数用于模板变量替换
  - 在规则测试前进行变量替换
  - 正确处理解释器信息

#### 2.3 前端API服务增强
- **文件**: `frontend/src/services/api/actionspace.js`
- **功能增强**:
  - `getAllEnvironmentVariables()`: 获取所有环境变量（内部+外部）
  - `testRules()`: 支持传递变量参数

## 使用方法

### 1. 在规则中使用变量

#### 自然语言规则示例
```
如果用户的年龄大于{{min_age}}岁且小于{{max_age}}岁，则允许访问
```

#### 逻辑规则示例 (JavaScript)
```javascript
return context.age > {{min_age}} && context.age < {{max_age}};
```

#### 逻辑规则示例 (Python)
```python
return context['age'] > {{min_age}} and context['age'] < {{max_age}}
```

### 2. 环境变量配置

#### 内部环境变量
- 在行动空间的环境变量页面配置
- 设置变量名、标签、默认值和描述

#### 外部环境变量
- 在外部环境变量页面配置
- 通过API自动同步获取实时数据

### 3. 规则测试

1. 在规则编辑界面点击"规则测试"展开测试区域
2. 系统自动检测规则中使用的变量
3. 使用当前环境变量的值进行模板替换
4. 执行规则测试并显示结果

### 4. 环境变量分组显示

#### 4.1 按行动空间分组
- 内部环境变量按照所属的行动空间进行分组显示
- 每个行动空间显示为一个独立的分组，带有文件夹图标 📁
- 未分类的变量归入"未分类"组

#### 4.2 分组界面设计
```
📁 行动空间A (内部变量)
    [变量1] [变量2] [变量3]

📁 行动空间B (内部变量)
    [变量4] [变量5]

🌐 外部环境变量
    [外部变量1] [外部变量2]
```

#### 4.3 变量标签样式
- 内部变量：蓝色标签，显示行动空间信息
- 外部变量：绿色标签，统一分组显示
- 字体大小：11px，紧凑显示
- 左侧缩进：12px，层次清晰

## 技术实现细节

### 1. 变量替换逻辑
- 使用正则表达式 `/\{\{\s*(\w+)\s*\}\}/g` 匹配变量
- 支持变量名前后的空格
- 在规则测试时进行实时替换

### 2. 变量检测和验证
- 实时分析规则内容中的变量引用
- 显示变量来源（内部/外部/未知）
- 提供变量详细信息的工具提示

### 3. 用户界面设计
- 使用不同颜色的标签区分变量类型：
  - 蓝色：内部环境变量
  - 绿色：外部环境变量
  - 红色：未定义的变量
- 提供点击插入功能，方便用户添加变量
- 按行动空间分组显示内部环境变量，提高组织性和可读性
- 使用图标和缩进来区分不同的变量分组

## 文件修改清单

### 前端文件
1. `frontend/src/utils/templateUtils.js` - 模板工具函数增强
2. `frontend/src/pages/actionspace/ActionRules.js` - 规则编辑界面增强
3. `frontend/src/services/api/actionspace.js` - API服务增强

### 后端文件
1. `app/api/routes/action_spaces.py` - 内部环境变量API
2. `app/api/routes/rules.py` - 规则测试API增强

## 测试建议

1. **变量检测测试**:
   - 在规则中输入包含 `{{变量名}}` 的内容
   - 验证系统是否正确检测并显示变量信息

2. **变量插入测试**:
   - 点击环境变量标签
   - 验证变量是否正确插入到规则内容中

3. **规则测试功能**:
   - 创建包含变量的规则
   - 执行规则测试
   - 验证变量是否被正确替换

4. **解释器切换测试**:
   - 创建逻辑规则
   - 切换解释器（JavaScript/Python）
   - 验证测试时使用正确的解释器

5. **环境变量分组测试**:
   - 验证内部环境变量按行动空间正确分组
   - 检查分组标题和图标显示
   - 验证变量标签的缩进和样式

6. **解释器优先级测试**:
   - 在规则编辑界面切换解释器
   - 执行规则测试
   - 验证使用的是界面当前选择的解释器，而不是数据库保存的解释器

## 重要修复

### 规则测试优先级问题修复
**问题**: 规则测试时优先使用数据库中保存的规则设置和内容，而不是用户在界面上当前编辑的内容。

**修复**: 修改后端规则测试逻辑，确保：
- 规则内容优先使用请求数据中的内容（界面当前编辑的内容）
- 解释器设置优先使用请求数据中的设置（界面当前选择的解释器）
- 这样用户可以在不保存规则的情况下测试当前编辑的内容

**影响文件**: `app/api/routes/rules.py` - 规则测试API

## 注意事项

1. 变量名只支持字母、数字和下划线
2. 变量替换在规则测试时进行，不会修改原始规则内容
3. 未定义的变量会显示为红色标签，提醒用户注意
4. 环境变量数据在规则编辑弹窗打开时自动加载
