# ABM-LLM-V2 Docker 部署指南

本目录包含了ABM-LLM-V2项目的Docker化部署配置，支持开发和生产环境的快速启动。

## 文件说明

- `docker-compose.yml` - Docker Compose配置文件
- `Dockerfile` - 统一的Docker镜像文件（包含前后端）
- `supervisord.conf` - 进程管理配置文件
- `build-image.sh` - 镜像构建脚本
- `start.sh` - 便捷启动脚本
- `test.sh` - Docker配置测试脚本
- `.dockerignore` - Docker构建忽略文件
- `.env.example` - 环境变量配置模板（可选）

## 快速开始

### 1. 准备工作

确保已安装Docker和Docker Compose：

```bash
# 检查Docker版本
docker --version
docker-compose --version
```

### 2. 构建镜像

首次使用需要先构建Docker镜像：

```bash
# 进入docker目录
cd docker

# 给脚本添加执行权限
chmod +x build-image.sh start.sh test.sh

# 构建镜像
./build-image.sh

# 可选：构建时指定标签
./build-image.sh -t v1.0.0

# 可选：构建并测试
./build-image.sh --test
```

### 3. 测试配置

在启动服务前，建议先测试Docker配置：

```bash
# 运行配置测试
./test.sh

# 可选：运行包含构建测试的完整测试
./test.sh --build
```

### 4. 启动服务

使用便捷脚本启动：

```bash
# 启动ABM-LLM-V2服务
./start.sh start

# 查看服务状态
./start.sh status

# 查看日志
./start.sh logs

# 停止服务
./start.sh stop
```

### 5. 访问应用

启动成功后，可以通过以下地址访问：

- **前端应用**：http://localhost:3000
- **后端API**：http://localhost:8080

## 详细使用说明

### 脚本选项

#### 构建脚本选项

```bash
./build-image.sh [选项]

选项:
  -n, --name NAME       镜像名称 (默认: abm-llm-v2)
  -t, --tag TAG         镜像标签 (默认: latest)
  -r, --registry REG    镜像仓库地址
  --no-cache            不使用构建缓存
  --push                构建完成后推送到仓库
  --clean               构建前清理旧镜像
  --test                构建后测试镜像
  -h, --help            显示帮助信息
```

#### 启动脚本选项

```bash
./start.sh [选项]

选项:
  start     启动ABM-LLM-V2服务
  stop      停止所有服务
  restart   重启服务
  logs      查看服务日志
  status    查看服务状态
  clean     清理所有容器和镜像
  help      显示帮助信息
```

### 手动使用Docker命令

如果不使用脚本，也可以直接使用Docker命令：

```bash
# 构建镜像
docker build -t abm-llm-v2 -f docker/Dockerfile .

# 运行容器
docker run -d --name abm-app \
  -p 8080:8080 -p 3000:3000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/config.conf:/app/config.conf \
  abm-llm-v2

# 或使用docker-compose
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 环境配置

#### 开发环境特点
- 前端支持热重载
- 源代码挂载到容器内
- 适合开发调试

#### 生产环境特点
- 前端代码经过构建优化
- 使用nginx提供静态文件服务
- 适合生产部署

### 数据持久化

以下目录会被挂载到宿主机，确保数据持久化：
- `../data` - 应用数据目录
- `../logs` - 日志目录
- `../config.conf` - 配置文件
- `../mcp_config.json` - MCP配置文件

### 健康检查

所有服务都配置了健康检查：
- 后端：检查 `/api/health` 端点
- 前端：检查服务可用性
- 自动重启不健康的容器

### 网络配置

所有服务运行在 `abm-network` 网络中，服务间可以通过服务名相互访问。

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :3000
   lsof -i :8080
   lsof -i :8000
   ```

2. **权限问题**
   ```bash
   # 确保启动脚本有执行权限
   chmod +x start.sh
   ```

3. **构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -a
   
   # 重新构建
   docker-compose build --no-cache
   ```

4. **查看详细日志**
   ```bash
   # 查看特定服务日志
   docker-compose logs backend
   docker-compose logs frontend-dev
   docker-compose logs frontend-prod
   ```

### 调试模式

如果需要进入容器调试：

```bash
# 进入后端容器
docker-compose exec backend bash

# 进入前端容器
docker-compose exec frontend-dev sh
```

## 自定义配置

### 修改端口

编辑 `docker-compose.yml` 文件中的端口映射：

```yaml
ports:
  - "自定义端口:容器端口"
```

### 修改环境变量

在 `docker-compose.yml` 中的 `environment` 部分添加或修改环境变量。

### 添加新服务

可以在 `docker-compose.yml` 中添加新的服务，如数据库、缓存等。

## 生产部署建议

1. **使用外部数据库**：生产环境建议使用外部数据库服务
2. **配置SSL**：使用nginx配置HTTPS
3. **监控和日志**：配置日志收集和监控系统
4. **备份策略**：定期备份数据目录
5. **资源限制**：为容器设置适当的资源限制

## 更新和维护

### 更新应用

```bash
# 停止服务
./start.sh stop

# 拉取最新代码
git pull

# 重新构建并启动
./start.sh prod
```

### 清理旧版本

```bash
# 清理未使用的镜像和容器
./start.sh clean
```
