import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, message, Space, Tag } from 'antd';
import { DeleteOutlined, EyeOutlined } from '@ant-design/icons';

const History = () => {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  const fetchConversations = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/conversations');
      if (!response.ok) throw new Error('获取历史记录失败');
      const data = await response.json();
      setConversations(data.conversations);
    } catch (error) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConversations();
  }, []);

  const handleView = (record) => {
    setSelectedConversation(record);
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      const response = await fetch(`/api/conversations/${id}`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('删除对话失败');
      message.success('删除成功');
      fetchConversations();
    } catch (error) {
      message.error(error.message);
    }
  };

  const columns = [
    {
      title: '对话ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '模式',
      dataIndex: 'mode',
      key: 'mode',
      render: (mode) => (
        <Tag color={mode === 'sequential' ? 'blue' : 'green'}>
          {mode === 'sequential' ? '顺序对话' : '面板对话'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: '消息数量',
      dataIndex: 'message_count',
      key: 'message_count',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card title="历史记录">
        <Table
          columns={columns}
          dataSource={conversations}
          rowKey="id"
          loading={loading}
        />
      </Card>

      <Modal
        title="对话详情"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={800}
        footer={null}
      >
        {selectedConversation && (
          <div>
            <h3>对话信息</h3>
            <p>模式：{selectedConversation.mode === 'sequential' ? '顺序对话' : '面板对话'}</p>
            <p>创建时间：{new Date(selectedConversation.created_at).toLocaleString()}</p>
            <p>消息数量：{selectedConversation.message_count}</p>
            
            <h3 style={{ marginTop: 20 }}>对话内容</h3>
            <div style={{ maxHeight: 400, overflow: 'auto' }}>
              {selectedConversation.messages.map((msg, index) => (
                <div key={index} style={{ marginBottom: 16 }}>
                  <strong>{msg.speaker}:</strong>
                  <p style={{ margin: '8px 0 0 20px' }}>{msg.message}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default History; 