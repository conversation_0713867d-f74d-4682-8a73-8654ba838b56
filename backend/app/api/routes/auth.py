"""
认证API路由

处理用户登录、登出和认证相关的API请求
"""
import jwt
import datetime
from flask import Blueprint, request, jsonify, current_app
from app.models import User, db
from werkzeug.security import check_password_hash

# 创建Blueprint
auth_bp = Blueprint('auth_api', __name__)

@auth_bp.route('/auth/login', methods=['POST'])
def login():
    """用户登录API"""
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({
            'status': 'error',
            'message': '无效的请求数据'
        }), 400

    username = data.get('username')
    password = data.get('password')

    # 验证必要字段
    if not username or not password:
        return jsonify({
            'status': 'error',
            'message': '用户名和密码不能为空'
        }), 400

    # 查询用户
    user = User.query.filter_by(username=username).first()

    # 验证用户和密码
    if not user or not user.check_password(password):
        return jsonify({
            'status': 'error',
            'message': '用户名或密码错误'
        }), 401

    # 检查用户状态
    if not user.is_active:
        return jsonify({
            'status': 'error',
            'message': '用户账号已被禁用'
        }), 403

    # 生成JWT令牌
    token_expiry = datetime.datetime.utcnow() + datetime.timedelta(days=1)
    token_payload = {
        'user_id': user.id,
        'username': user.username,
        'exp': token_expiry
    }
    token = jwt.encode(
        token_payload,
        current_app.config['SECRET_KEY'],
        algorithm='HS256'
    )

    # 返回成功响应
    return jsonify({
        'status': 'success',
        'message': '登录成功',
        'token': token,
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email
        }
    })

@auth_bp.route('/auth/logout', methods=['POST'])
def logout():
    """用户登出API"""
    # 由于使用JWT，服务器端不需要做任何操作
    # 客户端只需要删除本地存储的令牌
    return jsonify({
        'status': 'success',
        'message': '登出成功'
    })

@auth_bp.route('/auth/validate', methods=['GET'])
def validate_token():
    """验证令牌有效性"""
    # 从请求头获取令牌
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({
            'status': 'error',
            'message': '未提供认证令牌',
            'valid': False
        }), 401

    token = auth_header.split(' ')[1]

    try:
        # 验证令牌
        payload = jwt.decode(
            token,
            current_app.config['SECRET_KEY'],
            algorithms=['HS256']
        )
        
        # 检查用户是否存在
        user_id = payload.get('user_id')
        user = User.query.get(user_id)
        
        if not user or not user.is_active:
            return jsonify({
                'status': 'error',
                'message': '用户不存在或已被禁用',
                'valid': False
            }), 401
            
        return jsonify({
            'status': 'success',
            'message': '令牌有效',
            'valid': True
        })
    except jwt.ExpiredSignatureError:
        return jsonify({
            'status': 'error',
            'message': '令牌已过期',
            'valid': False
        }), 401
    except jwt.InvalidTokenError:
        return jsonify({
            'status': 'error',
            'message': '无效的令牌',
            'valid': False
        }), 401

@auth_bp.route('/auth/user', methods=['GET'])
def get_current_user():
    """获取当前登录用户信息"""
    # 从请求头获取令牌
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({
            'status': 'error',
            'message': '未提供认证令牌'
        }), 401

    token = auth_header.split(' ')[1]

    try:
        # 验证令牌
        payload = jwt.decode(
            token,
            current_app.config['SECRET_KEY'],
            algorithms=['HS256']
        )

        # 获取用户信息
        user_id = payload.get('user_id')
        user = User.query.get(user_id)

        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404

        if not user.is_active:
            return jsonify({
                'status': 'error',
                'message': '用户账号已被禁用'
            }), 403

        return jsonify({
            'status': 'success',
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email
            }
        })
    except jwt.ExpiredSignatureError:
        return jsonify({
            'status': 'error',
            'message': '令牌已过期'
        }), 401
    except jwt.InvalidTokenError:
        return jsonify({
            'status': 'error',
            'message': '无效的令牌'
        }), 401

@auth_bp.route('/auth/change-password', methods=['POST'])
def change_password():
    """修改用户密码"""
    # 从请求头获取令牌
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({
            'status': 'error',
            'message': '未提供认证令牌'
        }), 401

    token = auth_header.split(' ')[1]

    try:
        # 验证令牌
        payload = jwt.decode(
            token,
            current_app.config['SECRET_KEY'],
            algorithms=['HS256']
        )

        # 获取用户信息
        user_id = payload.get('user_id')
        user = User.query.get(user_id)

        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404

        if not user.is_active:
            return jsonify({
                'status': 'error',
                'message': '用户账号已被禁用'
            }), 403

        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({
                'status': 'error',
                'message': '无效的请求数据'
            }), 400

        new_password = data.get('new_password')

        # 验证新密码
        if not new_password:
            return jsonify({
                'status': 'error',
                'message': '新密码不能为空'
            }), 400

        if len(new_password) < 6:
            return jsonify({
                'status': 'error',
                'message': '密码长度不能少于6位'
            }), 400

        # 更新密码
        user.set_password(new_password)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '密码修改成功'
        })

    except jwt.ExpiredSignatureError:
        return jsonify({
            'status': 'error',
            'message': '令牌已过期'
        }), 401
    except jwt.InvalidTokenError:
        return jsonify({
            'status': 'error',
            'message': '无效的令牌'
        }), 401
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修改密码失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '修改密码失败，请稍后再试'
        }), 500
