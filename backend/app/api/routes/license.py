"""
许可证API路由

处理与系统许可证相关的所有API请求
"""
import json
from flask import Blueprint, request, jsonify, current_app
from app.services.license_service import LicenseService

# 创建Blueprint
license_bp = Blueprint('license_api', __name__)

@license_bp.route('/license', methods=['GET'])
def get_license():
    """获取当前许可证信息"""
    # 创建服务实例
    license_service = LicenseService()
    license_info = license_service.get_current_license()
    if not license_info:
        return jsonify({
            'status': 'error',
            'message': '未找到有效的许可证',
            'code': 'LICENSE_EXPIRED'
        }),400

    return jsonify({
        'status': 'success',
        'data': license_info
    })

@license_bp.route('/license/expired', methods=['GET'])
def get_expired_license():
    """获取过期的许可证信息（即使已过期也返回）"""
    # 创建服务实例
    license_service = LicenseService()
    license_info = license_service.get_license_data(include_expired=True)

    if not license_info:
        return jsonify({
            'status': 'error',
            'message': '未找到任何许可证信息'
        }),404

    return jsonify({
        'status': 'success',
        'data': license_info
    })

@license_bp.route('/license/activate', methods=['POST'])
def activate_license():
    """通过密钥激活许可证"""
    data = request.get_json()
    license_key = data.get('license_key')

    if not license_key:
        return jsonify({
            'status': 'error',
            'message': '缺少许可证密钥'
        }), 400

    # 创建服务实例
    license_service = LicenseService()
    result = license_service.activate_license(license_key)

    if not result['success']:
        return jsonify({
            'status': 'error',
            'message': result['message']
        }), 400

    return jsonify({
        'status': 'success',
        'data': result['license']
    })

@license_bp.route('/license/activate-file', methods=['POST'])
def activate_license_file():
    """通过文件激活许可证"""
    # 检查是否有文件上传
    if 'license_file' not in request.files:
        return jsonify({
            'status': 'error',
            'message': '未找到许可证文件'
        }), 400

    license_file = request.files['license_file']

    # 检查文件名
    if license_file.filename == '':
        return jsonify({
            'status': 'error',
            'message': '未选择文件'
        }), 400

    # 检查文件类型
    if not license_file.filename.endswith('.json'):
        return jsonify({
            'status': 'error',
            'message': '许可证文件必须是JSON格式'
        }), 400

    try:
        # 读取文件内容
        license_data = json.loads(license_file.read().decode('utf-8'))

        # 检查文件格式
        if 'license_key' not in license_data:
            return jsonify({
                'status': 'error',
                'message': '无效的许可证文件格式'
            }), 400

        # 创建服务实例并激活许可证
        license_service = LicenseService()
        result = license_service.activate_license(license_data['license_key'])

        if not result['success']:
            return jsonify({
                'status': 'error',
                'message': result['message']
            }), 400

        return jsonify({
            'status': 'success',
            'data': result['license']
        })
    except json.JSONDecodeError:
        return jsonify({
            'status': 'error',
            'message': '无效的JSON文件'
        }), 400
    except Exception as e:
        current_app.logger.error(f"通过文件激活许可证失败: {e}")
        return jsonify({
            'status': 'error',
            'message': f'激活许可证失败: {str(e)}'
        }), 500

@license_bp.route('/license/check-feature', methods=['GET'])
def check_feature():
    """检查功能是否可用"""
    feature_name = request.args.get('feature')

    if not feature_name:
        return jsonify({
            'status': 'error',
            'message': '缺少功能名称'
        }), 400

    # 创建服务实例
    license_service = LicenseService()
    available = license_service.check_feature_availability(feature_name)

    return jsonify({
        'status': 'success',
        'data': {
            'feature': feature_name,
            'available': available
        }
    })

@license_bp.route('/license/check-limit', methods=['GET'])
def check_limit():
    """检查资源限制"""
    resource_type = request.args.get('resource')
    current_count = request.args.get('count', type=int, default=0)

    if not resource_type:
        return jsonify({
            'status': 'error',
            'message': '缺少资源类型'
        }), 400

    if resource_type not in ['agents', 'action_spaces', 'roles']:
        return jsonify({
            'status': 'error',
            'message': f'不支持的资源类型: {resource_type}'
        }), 400

    # 创建服务实例
    license_service = LicenseService()
    allowed = license_service.check_resource_limit(resource_type, current_count)

    return jsonify({
        'status': 'success',
        'data': {
            'resource': resource_type,
            'current_count': current_count,
            'allowed': allowed
        }
    })

@license_bp.route('/license/system-key', methods=['GET'])
def get_system_key():
    """获取系统许可证密钥"""
    # 从系统设置中获取密钥
    from app.models import SystemSetting
    secret_key = SystemSetting.get('license_secret_key')

    if not secret_key:
        # 如果没有找到密钥，返回错误
        return jsonify({
            'status': 'error',
            'message': '系统未配置许可证密钥'
        }), 400

    # 返回密钥
    return jsonify({
        'status': 'success',
        'data': {
            'key': secret_key
        }
    })
