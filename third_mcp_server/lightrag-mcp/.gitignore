# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.mypy_cache
.ruff_cache

# Environments
.env
.venv
env/
venv/
ENV/
.env.local
.env.dev
.env.prod
# Разрешаем .env.example
!.env.example

# IDE
.idea/
.vscode/
*.swp
*.swo

# Project specific
rag_storage/
input/
inputs/
*.log
*.log.*
LightRAG/
.windsurfrules

# OS specific
.DS_Store
Thumbs.db
