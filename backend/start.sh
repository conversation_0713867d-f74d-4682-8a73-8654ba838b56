#!/bin/bash

# 设置UTF-8环境变量
export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
export PYTHONIOENCODING=utf-8

echo "============================================="
echo "启动多智能体专家系统"
echo "============================================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 找不到Python3。请安装Python3并确保它在PATH中。"
    exit 1
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p data/conversations data/agents data/worlds

# 安装必要的依赖
#echo "正在安装必要的依赖..."
#pip install flask flask-socketio python-dotenv pyyaml requests openai eventlet python-engineio gunicorn

# 设置权限
chmod +x run_app.py

# 激活conda环境
echo "激活conda环境: abm"
eval "$(conda shell.bash hook)"
conda activate abm

# 启动后端服务
echo "启动后端服务..."
cd "$(dirname "$0")"
python run_app.py &
BACKEND_PID=$!

echo "等待后端服务启动..."
sleep 5

# 读取配置文件中的主机和端口
if [ -f "config.conf" ]; then
    # 读取后端配置
    BACKEND_HOST=$(grep -A 10 "BACKEND_CONFIG" config.conf | grep "HOST" | head -1 | cut -d '=' -f 2 | tr -d ' ')
    BACKEND_PORT=$(grep -A 10 "BACKEND_CONFIG" config.conf | grep "PORT" | head -1 | cut -d '=' -f 2 | tr -d ' ')
    
    # 读取前端配置
    FRONTEND_HOST=$(grep -A 10 "FRONTEND_CONFIG" config.conf | grep "HOST" | head -1 | cut -d '=' -f 2 | tr -d ' ')
    FRONTEND_PORT=$(grep -A 10 "FRONTEND_CONFIG" config.conf | grep "PORT" | head -1 | cut -d '=' -f 2 | tr -d ' ')
    
    # 设置默认值（如果配置文件中没有）
    [ -z "$BACKEND_HOST" ] && BACKEND_HOST="localhost"
    [ -z "$BACKEND_PORT" ] && BACKEND_PORT="8080"
    [ -z "$FRONTEND_HOST" ] && FRONTEND_HOST="localhost"
    [ -z "$FRONTEND_PORT" ] && FRONTEND_PORT="3000"
else
    # 配置文件不存在，使用默认值
    BACKEND_HOST="localhost"
    BACKEND_PORT="8080"
    FRONTEND_HOST="localhost"
    FRONTEND_PORT="3000"
fi

# 启动前端服务
echo "启动前端服务..."
cd frontend
pnpm dev &
FRONTEND_PID=$!

echo "============================================="
echo "服务已启动:"
echo "后端: http://${BACKEND_HOST}:${BACKEND_PORT}"
echo "前端: http://${FRONTEND_HOST}:${FRONTEND_PORT}"
echo "============================================="
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait 
