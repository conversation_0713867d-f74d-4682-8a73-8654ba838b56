// 首页组件 - 展示系统概览信息的主页仪表盘
import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Typography, Space, Button, Spin, message
} from 'antd';
import {
  TeamOutlined, MessageOutlined, UserOutlined, DatabaseOutlined, RiseOutlined, SettingOutlined
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import ReactECharts from 'echarts-for-react';
import statisticsAPI from '../services/api/statistics';

const { Paragraph } = Typography;

const Home = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    overview: {
      total_tasks: 0,
      active_tasks: 0,
      total_roles: 0,
      total_action_spaces: 0
    },
    activity_trends: {
      today_new_tasks: 0,
      week_completed_tasks: 0,
      avg_task_duration_hours: 0,
      daily_trends: []
    },
    interactions: {
      total_messages: 0,
      today_messages: 0,
      avg_messages_per_task: 0,
      tool_calls_count: 0,
      active_conversations: 0,
      agent_messages: 0,
      human_messages: 0
    },
    ecosystem: {
      agent_status_distribution: {},
      top_roles: [],
      role_usage_rate: 0,
      avg_agents_per_task: 0
    },
    resources: {
      total_knowledge: 0,
      total_rule_sets: 0,
      total_capabilities: 0,
      spaces_with_roles: 0,
      spaces_with_rules: 0
    },
    users: {
      total_users: 0,
      active_users: 0,
      today_active_users: 0,
      avg_tasks_per_user: 0,
      top_users: []
    },
    autonomous_tasks: {
      total_autonomous_tasks: 0,
      active_autonomous_tasks: 0,
      today_autonomous_tasks: 0,
      week_autonomous_tasks: 0,
      autonomous_status_distribution: {},
      autonomous_type_distribution: {},
      total_executions: 0,
      today_executions: 0,
      execution_status_distribution: {},
      success_rate: 0,
      avg_execution_duration: 0
    }
  });

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      setLoading(true);
      const response = await statisticsAPI.getDashboardData();

      if (response.success) {
        setDashboardData(response.data);
      } else {
        message.error('获取系统统计数据失败');
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取系统统计数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchStatistics();
  }, []);

  // 核心指标柱状图配置
  const getCoreMetricsChartOption = () => {
    return {
      title: {
        text: '核心指标概览',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['行动任务', '角色数量', '行动空间', '进行中任务'],
        axisLabel: {
          interval: 0,
          rotate: 0
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '数量',
          type: 'bar',
          data: [
            {
              value: dashboardData.overview.total_tasks,
              itemStyle: { color: '#1677ff' }
            },
            {
              value: dashboardData.overview.total_roles,
              itemStyle: { color: '#52c41a' }
            },
            {
              value: dashboardData.overview.total_action_spaces,
              itemStyle: { color: '#fa8c16' }
            },
            {
              value: dashboardData.overview.active_tasks,
              itemStyle: { color: '#eb2f96' }
            }
          ],
          label: {
            show: true,
            position: 'top'
          }
        }
      ]
    };
  };

  // 智能体状态分布饼状图配置
  const getAgentStatusPieOption = () => {
    const statusData = Object.entries(dashboardData.ecosystem.agent_status_distribution || {}).map(([status, count]) => ({
      name: status === 'active' ? '活跃' : status === 'inactive' ? '非活跃' : status === 'running' ? '运行中' : status,
      value: count
    }));

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'center'
      },
      series: [
        {
          name: '智能体状态',
          type: 'pie',
          radius: '50%',
          center: ['65%', '50%'],
          data: statusData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  };

  // 活动趋势线图配置
  const getActivityTrendOption = () => {
    const trendData = dashboardData.activity_trends.daily_trends || [];
    const dates = trendData.map(item => item.date || '');
    const taskCounts = trendData.map(item => item.task_count || 0);
    const messageCounts = trendData.map(item => item.message_count || 0);

    return {
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          let result = params[0].name + '<br/>';
          params.forEach(param => {
            result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
          });
          return result;
        }
      },
      legend: {
        data: ['任务数量', '消息数量'],
        top: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates,
        axisLabel: {
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        minInterval: 1
      },
      series: [
        {
          name: '任务数量',
          type: 'line',
          data: taskCounts,
          smooth: true,
          lineStyle: {
            color: '#1677ff',
            width: 2
          },
          areaStyle: {
            color: 'rgba(22, 119, 255, 0.1)'
          },
          symbol: 'circle',
          symbolSize: 6
        },
        {
          name: '消息数量',
          type: 'line',
          data: messageCounts,
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 2
          },
          areaStyle: {
            color: 'rgba(82, 196, 26, 0.1)'
          },
          symbol: 'circle',
          symbolSize: 6
        }
      ]
    };
  };

  // 系统资源环形图配置
  const getResourcesDonutOption = () => {
    const resourceData = [
      { name: '知识库', value: dashboardData.resources.total_knowledge },
      { name: '规则集', value: dashboardData.resources.total_rule_sets },
      { name: '能力', value: dashboardData.resources.total_capabilities }
    ];

    return {
      title: {
        text: '系统资源分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '系统资源',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '30',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: resourceData
        }
      ]
    };
  };

  // 自主行动任务状态分布饼状图配置
  const getAutonomousTaskStatusPieOption = () => {
    const statusData = Object.entries(dashboardData.autonomous_tasks.autonomous_status_distribution || {}).map(([status, count]) => ({
      name: status === 'active' ? '活跃' : status === 'completed' ? '已完成' : status === 'stopped' ? '已停止' : status,
      value: count
    }));

    return {
      title: {
        text: '自主任务状态分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '自主任务状态',
          type: 'pie',
          radius: '50%',
          data: statusData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  };

  // 自主行动任务类型分布柱状图配置
  const getAutonomousTaskTypeBarOption = () => {
    const typeData = Object.entries(dashboardData.autonomous_tasks.autonomous_type_distribution || {}).map(([type, count]) => ({
      name: type === 'discussion' ? '讨论模式' : type === 'conditional_stop' ? '条件停止' : type === 'variable_trigger' ? '变量触发' : type === 'time_trigger' ? '时间触发' : type,
      value: count
    }));

    return {
      title: {
        text: '自主任务类型分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: typeData.map(item => item.name),
        axisLabel: {
          interval: 0,
          rotate: 45
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '任务数量',
          type: 'bar',
          data: typeData.map(item => ({
            value: item.value,
            itemStyle: {
              color: function(params) {
                const colors = ['#1677ff', '#52c41a', '#fa8c16', '#eb2f96'];
                return colors[params.dataIndex % colors.length];
              }
            }
          })),
          label: {
            show: true,
            position: 'top'
          }
        }
      ]
    };
  };

  // 自主行动执行统计仪表盘配置
  const getAutonomousExecutionGaugeOption = () => {
    return {
      title: {
        text: '自主任务执行成功率',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        formatter: '{a} <br/>{b} : {c}%'
      },
      series: [
        {
          name: '成功率',
          type: 'gauge',
          progress: {
            show: true
          },
          detail: {
            valueAnimation: true,
            formatter: '{value}%'
          },
          data: [
            {
              value: dashboardData.autonomous_tasks.success_rate,
              name: '执行成功率'
            }
          ]
        }
      ]
    };
  };

  return (
    <div className="home-dashboard">
      <div style={{ marginBottom: '24px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 20
        }}>
          <div>
            <Typography.Title level={4} style={{ margin: 0, marginBottom: '8px' }}>系统概览</Typography.Title>
            <Paragraph type="secondary">欢迎使用多智能体专家决策与执行系统，以下是系统关键指标</Paragraph>
          </div>
        </div>
      </div>

      <Spin spinning={loading}>
        {/* 快速操作 */}
        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title={<><SettingOutlined /> 快速操作</>}>
              <Space size="large" wrap>
                <Button type="primary" size="large">
                  <Link to="/action-tasks/overview">创建行动任务</Link>
                </Button>
                <Button size="large">
                  <Link to="/roles/management">管理角色</Link>
                </Button>
                <Button size="large">
                  <Link to="/action-spaces/overview">配置行动空间</Link>
                </Button>
                <Button size="large">
                  <Link to="/settings?tab=general">系统设置</Link>
                </Button>
              </Space>
            </Card>
          </Col>
        </Row>

        {/* 第一层：系统核心指标概览 */}
        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="📊 系统核心指标">
              <ReactECharts
                option={getCoreMetricsChartOption()}
                style={{ height: '300px' }}
                opts={{ renderer: 'canvas' }}
              />
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <Space>
                  <Button type="link">
                    <Link to="/action-tasks/overview">查看任务</Link>
                  </Button>
                  <Button type="link">
                    <Link to="/roles/management">管理角色</Link>
                  </Button>
                  <Button type="link">
                    <Link to="/action-spaces/overview">查看空间</Link>
                  </Button>
                  <Button type="link">
                    <Link to="/action-tasks/monitoring">监控任务</Link>
                  </Button>
                </Space>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 第二层：活动趋势分析 */}
        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="📈 活动趋势分析">
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Card size="small" title="最近7天活动趋势">
                    {dashboardData.activity_trends.daily_trends && dashboardData.activity_trends.daily_trends.length > 0 ? (
                      <ReactECharts
                        option={getActivityTrendOption()}
                        style={{ height: '250px' }}
                        opts={{ renderer: 'canvas' }}
                      />
                    ) : (
                      <div style={{
                        height: '250px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#999',
                        fontSize: '12px'
                      }}>
                        暂无活动趋势数据
                      </div>
                    )}
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small" title="智能体状态">
                    {Object.keys(dashboardData.ecosystem.agent_status_distribution || {}).length > 0 ? (
                      <ReactECharts
                        option={getAgentStatusPieOption()}
                        style={{ height: '250px' }}
                        opts={{ renderer: 'canvas' }}
                      />
                    ) : (
                      <div style={{
                        height: '250px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#999',
                        fontSize: '12px'
                      }}>
                        暂无智能体状态数据
                      </div>
                    )}
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small" title="活动指标">
                    <ReactECharts
                      option={{
                        tooltip: {
                          trigger: 'axis',
                          axisPointer: {
                            type: 'shadow'
                          }
                        },
                        grid: {
                          left: '5%',
                          right: '5%',
                          bottom: '5%',
                          top: '5%',
                          containLabel: true
                        },
                        xAxis: {
                          type: 'category',
                          data: ['今日任务', '本周完成', '活跃会话'],
                          axisLabel: {
                            fontSize: 10,
                            interval: 0
                          }
                        },
                        yAxis: {
                          type: 'value',
                          axisLabel: {
                            fontSize: 10
                          }
                        },
                        series: [
                          {
                            type: 'bar',
                            data: [
                              {
                                value: dashboardData.activity_trends.today_new_tasks,
                                itemStyle: { color: '#ff4d4f' }
                              },
                              {
                                value: dashboardData.activity_trends.week_completed_tasks,
                                itemStyle: { color: '#52c41a' }
                              },
                              {
                                value: dashboardData.interactions.active_conversations,
                                itemStyle: { color: '#eb2f96' }
                              }
                            ],
                            label: {
                              show: true,
                              position: 'top',
                              fontSize: 10
                            }
                          }
                        ]
                      }}
                      style={{ height: '250px' }}
                      opts={{ renderer: 'canvas' }}
                    />
                  </Card>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 第三层：交互与消息统计 */}
        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={12}>
            <Card title="💬 消息交互分析">
              <ReactECharts
                option={{
                  title: {
                    text: '消息类型分布',
                    left: 'center',
                    textStyle: {
                      fontSize: 14,
                      fontWeight: 'bold'
                    }
                  },
                  tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                  },
                  legend: {
                    orient: 'vertical',
                    left: 'left',
                    top: 'middle'
                  },
                  series: [
                    {
                      name: '消息类型',
                      type: 'pie',
                      radius: '60%',
                      center: ['60%', '50%'],
                      data: [
                        { name: '智能体消息', value: dashboardData.interactions.agent_messages },
                        { name: '用户消息', value: dashboardData.interactions.human_messages },
                        { name: '工具调用', value: dashboardData.interactions.tool_calls_count }
                      ],
                      emphasis: {
                        itemStyle: {
                          shadowBlur: 10,
                          shadowOffsetX: 0,
                          shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                      }
                    }
                  ]
                }}
                style={{ height: '300px' }}
                opts={{ renderer: 'canvas' }}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card title="👥 用户活动雷达">
              <ReactECharts
                option={{
                  tooltip: {
                    trigger: 'item'
                  },
                  radar: {
                    indicator: [
                      { name: '总用户数', max: Math.max(dashboardData.users.total_users * 1.2, 100) },
                      { name: '活跃用户数', max: Math.max(dashboardData.users.active_users * 1.2, 50) },
                      { name: '今日活跃用户', max: Math.max(dashboardData.users.today_active_users * 1.2, 20) },
                      { name: '平均任务数', max: Math.max(dashboardData.users.avg_tasks_per_user * 1.2, 10) },
                      { name: '用户活跃度', max: 100 }
                    ]
                  },
                  series: [
                    {
                      name: '用户活动指标',
                      type: 'radar',
                      data: [
                        {
                          value: [
                            dashboardData.users.total_users,
                            dashboardData.users.active_users,
                            dashboardData.users.today_active_users,
                            dashboardData.users.avg_tasks_per_user,
                            dashboardData.users.total_users > 0 ?
                              Math.round((dashboardData.users.active_users / dashboardData.users.total_users) * 100) : 0
                          ],
                          name: '当前指标',
                          itemStyle: {
                            color: '#1677ff'
                          },
                          areaStyle: {
                            color: 'rgba(22, 119, 255, 0.1)'
                          }
                        }
                      ]
                    }
                  ]
                }}
                style={{ height: '300px' }}
                opts={{ renderer: 'canvas' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 第四层：资源与生态统计 */}
        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={12}>
            <Card title="🎭 角色生态分析">
              {dashboardData.ecosystem.top_roles && dashboardData.ecosystem.top_roles.length > 0 ? (
                <ReactECharts
                  option={{
                    title: {
                      text: '最常用角色 TOP5',
                      left: 'center',
                      textStyle: {
                        fontSize: 14,
                        fontWeight: 'bold'
                      }
                    },
                    tooltip: {
                      trigger: 'axis',
                      axisPointer: {
                        type: 'shadow'
                      }
                    },
                    grid: {
                      left: '3%',
                      right: '4%',
                      bottom: '3%',
                      top: '15%',
                      containLabel: true
                    },
                    xAxis: {
                      type: 'value'
                    },
                    yAxis: {
                      type: 'category',
                      data: (dashboardData.ecosystem.top_roles || []).map(item => item.name).reverse()
                    },
                    series: [
                      {
                        name: '使用次数',
                        type: 'bar',
                        data: (dashboardData.ecosystem.top_roles || []).map(item => item.usage_count).reverse(),
                        itemStyle: {
                          color: function(params) {
                            const colors = ['#ff4d4f', '#fa8c16', '#fadb14', '#52c41a', '#1677ff'];
                            return colors[params.dataIndex % colors.length];
                          }
                        },
                        label: {
                          show: true,
                          position: 'right'
                        }
                      }
                    ]
                  }}
                  style={{ height: '300px' }}
                  opts={{ renderer: 'canvas' }}
                />
              ) : (
                <div style={{
                  height: '300px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#999',
                  fontSize: '14px'
                }}>
                  暂无角色使用数据
                </div>
              )}
            </Card>
          </Col>
          <Col span={12}>
            <Card title="🗄️ 系统资源分布">
              {(dashboardData.resources.total_knowledge + dashboardData.resources.total_rule_sets + dashboardData.resources.total_capabilities) > 0 ? (
                <ReactECharts
                  option={getResourcesDonutOption()}
                  style={{ height: '300px' }}
                  opts={{ renderer: 'canvas' }}
                />
              ) : (
                <div style={{
                  height: '300px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#999',
                  fontSize: '14px'
                }}>
                  暂无系统资源数据
                </div>
              )}
            </Card>
          </Col>
        </Row>

        {/* 第五层：自主行动统计 */}
        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="🤖 自主行动统计">
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Card size="small" title="任务状态分布">
                    {Object.keys(dashboardData.autonomous_tasks.autonomous_status_distribution || {}).length > 0 ? (
                      <ReactECharts
                        option={getAutonomousTaskStatusPieOption()}
                        style={{ height: '250px' }}
                        opts={{ renderer: 'canvas' }}
                      />
                    ) : (
                      <div style={{
                        height: '250px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#999',
                        fontSize: '12px'
                      }}>
                        暂无自主任务状态数据
                      </div>
                    )}
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small" title="任务类型分布">
                    {Object.keys(dashboardData.autonomous_tasks.autonomous_type_distribution || {}).length > 0 ? (
                      <ReactECharts
                        option={getAutonomousTaskTypeBarOption()}
                        style={{ height: '250px' }}
                        opts={{ renderer: 'canvas' }}
                      />
                    ) : (
                      <div style={{
                        height: '250px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#999',
                        fontSize: '12px'
                      }}>
                        暂无自主任务类型数据
                      </div>
                    )}
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small" title="执行成功率">
                    {dashboardData.autonomous_tasks.total_executions > 0 ? (
                      <ReactECharts
                        option={getAutonomousExecutionGaugeOption()}
                        style={{ height: '250px' }}
                        opts={{ renderer: 'canvas' }}
                      />
                    ) : (
                      <div style={{
                        height: '250px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#999',
                        fontSize: '12px'
                      }}>
                        暂无执行数据
                      </div>
                    )}
                  </Card>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 自主行动详细统计 */}
        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="自主行动详细统计">
              <Row gutter={24}>
                <Col span={4}>
                  <div style={{ textAlign: 'center', padding: '16px' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1677ff' }}>
                      {dashboardData.autonomous_tasks.total_autonomous_tasks}
                    </div>
                    <div style={{ color: '#666', marginTop: '8px' }}>总自主任务</div>
                  </div>
                </Col>
                <Col span={4}>
                  <div style={{ textAlign: 'center', padding: '16px' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                      {dashboardData.autonomous_tasks.active_autonomous_tasks}
                    </div>
                    <div style={{ color: '#666', marginTop: '8px' }}>活跃任务</div>
                  </div>
                </Col>
                <Col span={4}>
                  <div style={{ textAlign: 'center', padding: '16px' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                      {dashboardData.autonomous_tasks.today_autonomous_tasks}
                    </div>
                    <div style={{ color: '#666', marginTop: '8px' }}>今日新增</div>
                  </div>
                </Col>
                <Col span={4}>
                  <div style={{ textAlign: 'center', padding: '16px' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#eb2f96' }}>
                      {dashboardData.autonomous_tasks.total_executions}
                    </div>
                    <div style={{ color: '#666', marginTop: '8px' }}>总执行次数</div>
                  </div>
                </Col>
                <Col span={4}>
                  <div style={{ textAlign: 'center', padding: '16px' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                      {dashboardData.autonomous_tasks.today_executions}
                    </div>
                    <div style={{ color: '#666', marginTop: '8px' }}>今日执行</div>
                  </div>
                </Col>
                <Col span={4}>
                  <div style={{ textAlign: 'center', padding: '16px' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#13c2c2' }}>
                      {dashboardData.autonomous_tasks.avg_execution_duration}分钟
                    </div>
                    <div style={{ color: '#666', marginTop: '8px' }}>平均执行时长</div>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default Home;