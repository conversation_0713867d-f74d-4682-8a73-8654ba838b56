"""
时间触发会话模块

提供基于时间间隔触发的自主行动功能，智能体会按照预设的时间间隔自动执行单轮行动，直到达到停止条件为止

函数与关键变量说明:
---------------------------------------

* start_time_trigger_conversation - 启动时间触发会话
  - task_id: 行动任务ID
  - conversation_id: 会话ID
  - config: 配置参数，包含时间间隔、执行次数限制等
  - streaming: 是否使用流式输出
  - app_context: 应用上下文，用于流式处理
  - result_queue: 结果队列，用于流式处理

* _start_time_trigger_impl - 时间触发会话实现方法
  - task_id: 行动任务ID
  - conversation_id: 会话ID
  - config: 配置参数
  - streaming: 是否使用流式输出
  - result_queue: 结果队列，用于流式处理

* stop_time_trigger_conversation - 停止时间触发会话
  - task_id: 行动任务ID
  - conversation_id: 会话ID

* _schedule_next_execution - 调度下次执行
  - task_key: 任务键
  - interval_minutes: 时间间隔（分钟）

* _execute_single_round - 执行单轮行动
  - task_key: 任务键

* _check_execution_limits - 检查执行限制
  - task_key: 任务键
  - config: 配置参数

* _interrupt_running_agents - 中断正在执行的智能体
  - task_key: 任务键

* _track_agent_execution - 跟踪智能体执行状态
  - task_key: 任务键
  - agent_id: 智能体ID
  - thread: 执行线程
  - interrupt_flag: 中断标志
"""
import json
import logging
import queue
import traceback
import time
import threading
import jwt
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from flask import request, current_app, has_request_context

from app.models import db, Conversation, ConversationAgent, Message, Agent, Role, AutonomousTask, AutonomousTaskExecution, User
from app.utils.datetime_utils import get_current_time_with_timezone
from app.services.conversation.message_formater import (
    format_system_message, format_agent_info, format_connection_status,
    format_all_agents_done, format_agent_error_done, serialize_message
)

logger = logging.getLogger(__name__)

# 全局变量，用于跟踪活动的时间触发任务
_active_time_trigger_tasks = {}

def get_current_user():
    """获取当前用户信息 - 时间触发模式中固化为system"""
    try:
        # 检查是否在请求上下文中
        if has_request_context():
            # 尝试从JWT token获取用户信息
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                payload = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
                user = User.query.get(payload['user_id'])
                if user:
                    return {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email
                    }
    except Exception as e:
        logger.debug(f"获取当前用户失败: {str(e)}")

    # 时间触发模式后台任务中固化为system用户
    return {
        'id': None,
        'username': 'system',
        'email': None
    }


def start_time_trigger_conversation(task_id: int, conversation_id: int, config: Dict[str, Any],
                                  streaming: bool = False, app_context = None,
                                  result_queue: queue.Queue = None) -> Dict:
    """
    启动时间触发会话

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID
        config: 配置参数，包含时间间隔、执行次数限制等
        streaming: 是否使用流式输出，默认为False
        app_context: 应用上下文，用于流式处理
        result_queue: 结果队列，用于流式处理

    Returns:
        Dict: 会话结果信息
    """
    # 如果是流式处理模式，需要应用上下文
    if streaming and app_context:
        with app_context:
            return _start_time_trigger_impl(
                task_id, conversation_id, config, streaming, result_queue
            )
    else:
        # 非流式处理不需要应用上下文
        return _start_time_trigger_impl(
            task_id, conversation_id, config, streaming, result_queue
        )


def stop_time_trigger_conversation(task_id: int, conversation_id: int) -> bool:
    """
    停止时间触发会话

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID

    Returns:
        bool: 是否成功停止
    """
    task_key = f"{task_id}:{conversation_id}"
    
    if task_key in _active_time_trigger_tasks:
        try:
            task_info = _active_time_trigger_tasks[task_key]
            
            # 取消定时器
            timer = task_info.get('timer')
            if timer:
                timer.cancel()
            
            # 中断正在执行的智能体
            _interrupt_running_agents(task_key)
            
            # 从活动任务中移除
            del _active_time_trigger_tasks[task_key]
            
            # 创建系统消息，记录任务被停止
            try:
                # 使用新的数据库会话来避免事务冲突
                from sqlalchemy.orm import sessionmaker
                from app import db as app_db

                # 创建新的会话
                Session = sessionmaker(bind=app_db.engine)
                new_session = Session()

                try:
                    end_msg = Message(
                        conversation_id=conversation_id,
                        action_task_id=task_id,
                        content="提示：时间触发任务被用户手动停止。",
                        role="system",
                        created_at=get_current_time_with_timezone()
                    )
                    new_session.add(end_msg)
                    new_session.commit()

                    logger.info(f"已添加任务停止系统消息: {end_msg.id}")
                finally:
                    new_session.close()

            except Exception as e:
                logger.error(f"添加任务停止系统消息失败: {str(e)}")

            logger.info(f"成功停止时间触发任务: {task_key}")
            return True
        except Exception as e:
            logger.error(f"停止时间触发任务出错: {str(e)}")
            return False
    else:
        logger.info(f"未找到活动的时间触发任务: {task_key}")
        return False


def _start_time_trigger_impl(task_id: int, conversation_id: int, config: Dict[str, Any],
                           streaming: bool = False, result_queue: queue.Queue = None) -> Dict:
    """时间触发会话实现方法"""
    # 构建任务键
    task_key = f"{task_id}:{conversation_id}"

    # 不再保存应用上下文，而是保存应用实例用于后续创建上下文
    from flask import current_app
    flask_app = None
    try:
        # 尝试获取当前应用实例
        flask_app = current_app._get_current_object()
    except RuntimeError:
        # 如果没有应用上下文，记录警告
        logger.warning(f"无法获取应用实例，时间触发任务可能无法正常工作: {task_key}")

    # 注册时间触发任务
    _active_time_trigger_tasks[task_key] = {
        'task_id': task_id,
        'conversation_id': conversation_id,
        'config': config,
        'streaming': streaming,
        'result_queue': result_queue,
        'start_time': get_current_time_with_timezone(),
        'execution_count': 0,
        'timer': None,
        'agent_threads': {},  # 跟踪智能体执行状态
        'flask_app': flask_app  # 保存应用实例而不是上下文
    }

    try:
        # 验证配置参数
        time_interval = config.get('timeInterval', 30)  # 默认30分钟
        max_executions = config.get('maxExecutions', 0)  # 0表示无限制

        if time_interval < 1 or time_interval > 1440:
            raise ValueError(f"时间间隔必须在1-1440分钟之间，当前值: {time_interval}")

        if max_executions < 0:
            raise ValueError(f"最大执行次数不能为负数，当前值: {max_executions}")

        logger.info(f"开始启动时间触发任务: task_id={task_id}, conversation_id={conversation_id}")
        logger.info(f"配置参数: 时间间隔={time_interval}分钟, 最大执行次数={max_executions}")

        # 获取会话中的智能体
        conversation = Conversation.query.get(conversation_id)
        if not conversation:
            error_msg = f"会话不存在: {conversation_id}"
            logger.error(error_msg)
            
            if streaming and result_queue:
                result_queue.put(json.dumps({
                    'connectionStatus': 'error',
                    'error': error_msg
                }))
                result_queue.put(None)
                return {'status': 'error', 'message': error_msg}
            else:
                raise ValueError(error_msg)

        # 获取会话中的非监督者智能体
        task_agents = ConversationAgent.query.join(Agent).filter(
            ConversationAgent.conversation_id == conversation_id,
            Agent.is_observer == False  # 过滤掉监督者智能体
        ).all()

        if not task_agents:
            error_msg = f"会话中没有可用的任务智能体（监督者智能体不参与时间触发任务）: {conversation_id}"
            logger.error(error_msg)

            if streaming and result_queue:
                result_queue.put(json.dumps({
                    'connectionStatus': 'error',
                    'error': error_msg
                }))
                result_queue.put(None)
                return {'status': 'error', 'message': error_msg}
            else:
                raise ValueError(error_msg)

        # 创建自主任务记录
        autonomous_task = AutonomousTask(
            conversation_id=conversation_id,
            type='time_trigger',
            status='active',
            config=config
        )
        db.session.add(autonomous_task)
        db.session.flush()  # 获取ID但不提交事务

        # 获取当前登录用户信息（后台任务中固化为system用户）
        current_user = get_current_user()
        trigger_source = current_user['username']  # 现在总是返回有效值

        # 创建自主任务执行记录
        autonomous_execution = AutonomousTaskExecution(
            autonomous_task_id=autonomous_task.id,
            execution_type='manual',  # 手动触发
            trigger_source=trigger_source,
            trigger_data={
                'task_id': task_id,
                'conversation_id': conversation_id,
                'user_action': 'start_time_trigger_conversation',
                'user_id': current_user['id'],
                'username': current_user['username']
            },
            status='running'
        )
        db.session.add(autonomous_execution)
        db.session.commit()

        logger.info(f"已创建时间触发任务记录: autonomous_task_id={autonomous_task.id}, execution_id={autonomous_execution.id}")

        # 创建开始任务的系统消息
        topic = config.get('topic', '请基于各自角色和知识，持续进行行动')

        system_msg = Message(
            conversation_id=conversation_id,
            action_task_id=task_id,
            content=f"提示：现在开始时间触发模式的自主行动，智能体将按照 {time_interval} 分钟的间隔自动执行行动。\n任务主题：{topic}",
            role="system",
            created_at=get_current_time_with_timezone()
        )
        db.session.add(system_msg)
        db.session.commit()

        # 流式模式发送系统消息
        if streaming and result_queue:
            formatted_msg = format_system_message(
                message_id=str(system_msg.id),
                content=system_msg.content,
                created_at=system_msg.created_at.isoformat()
            )
            result_queue.put(serialize_message(formatted_msg))

        # 立即执行第一次触发
        _execute_trigger_action(task_key)

        # 调度下次执行
        _schedule_next_execution(task_key, time_interval)

        logger.info(f"时间触发任务启动成功: {task_key}")
        return {'status': 'success', 'message': '时间触发任务已启动'}

    except Exception as e:
        # 清理任务注册
        if task_key in _active_time_trigger_tasks:
            del _active_time_trigger_tasks[task_key]

        error_msg = f"启动时间触发任务失败: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        if streaming and result_queue:
            result_queue.put(json.dumps({
                'connectionStatus': 'error',
                'error': error_msg
            }))
            result_queue.put(None)
            return {'status': 'error', 'message': error_msg}
        else:
            raise


def _schedule_next_execution(task_key: str, interval_minutes: int):
    """调度下次执行"""
    if task_key not in _active_time_trigger_tasks:
        logger.warning(f"任务已不存在，取消调度: {task_key}")
        return

    task_info = _active_time_trigger_tasks[task_key]

    # 检查执行限制
    can_continue, reason = _check_execution_limits(task_key, task_info['config'])
    if not can_continue:
        logger.info(f"达到执行限制，停止调度: {task_key}, 原因: {reason}")
        _finalize_time_trigger_task(task_key, reason)
        return

    # 创建定时器，使用新的定时器回调函数
    interval_seconds = interval_minutes * 60
    timer = threading.Timer(interval_seconds, _timer_callback, args=[task_key, interval_minutes])
    timer.daemon = True
    timer.start()

    # 更新任务信息中的定时器
    task_info['timer'] = timer

    logger.info(f"已调度下次执行: {task_key}, {interval_minutes}分钟后执行")


def _timer_callback(task_key: str, interval_minutes: int):
    """定时器回调函数：执行触发行动并调度下次执行"""
    try:
        # 获取任务信息和应用上下文
        if task_key not in _active_time_trigger_tasks:
            logger.warning(f"定时器回调时任务已不存在: {task_key}")
            return

        task_info = _active_time_trigger_tasks[task_key]
        flask_app = task_info.get('flask_app')

        if flask_app:
            # 在新的应用上下文中执行
            with flask_app.app_context():
                # 执行触发行动（调度下次执行现在在执行完成后进行）
                _execute_trigger_action(task_key)
        else:
            logger.error(f"定时器回调时缺少Flask应用实例: {task_key}")

    except Exception as e:
        logger.error(f"定时器回调执行出错: {task_key}, {str(e)}")
        logger.error(traceback.format_exc())


def _execute_trigger_action(task_key: str):
    """执行触发行动"""
    if task_key not in _active_time_trigger_tasks:
        logger.warning(f"任务已不存在，跳过执行: {task_key}")
        return

    task_info = _active_time_trigger_tasks[task_key]

    try:
        # 在执行前检查时间限制（不包括执行次数，因为还没增加）
        config = task_info['config']

        # 检查总时长限制
        if config.get('enableTimeLimit', False):
            total_limit_minutes = config.get('totalTimeLimit', 1440)  # 默认1440分钟（24小时）
            elapsed_minutes = (get_current_time_with_timezone() - task_info['start_time']).total_seconds() / 60
            if elapsed_minutes >= total_limit_minutes:
                logger.info(f"执行前检查发现达到时间限制，停止任务: {task_key}, 已运行 {elapsed_minutes:.1f} 分钟")
                _finalize_time_trigger_task(task_key, 'reached_time_limit')
                return

        # 中断正在执行的智能体
        _interrupt_running_agents(task_key)

        # 增加执行计数
        task_info['execution_count'] += 1
        execution_count = task_info['execution_count']

        # 在执行后检查执行次数限制
        max_executions = config.get('maxExecutions', 0)
        if max_executions > 0 and execution_count >= max_executions:
            logger.info(f"执行后检查发现达到执行次数限制，停止任务: {task_key}, 执行次数: {execution_count}")
            _finalize_time_trigger_task(task_key, 'reached_max_executions')
            return

        logger.info(f"开始执行第 {execution_count} 次触发: {task_key}")

        # 发送触发通知
        _send_trigger_notification(task_key, execution_count)

        # 发送轮次信息，告知前端这是新的一轮执行
        result_queue = task_info.get('result_queue')
        if result_queue:
            # 使用标准格式发送轮次信息
            round_info = {
                "current": execution_count,
                "total": 999  # 时间触发模式使用999表示无限轮次
            }
            result_queue.put(json.dumps({
                "content": None,
                "meta": {
                    "roundInfo": round_info
                }
            }))
            logger.info(f"已发送第 {execution_count} 轮次信息: {task_key}")

        # 时间触发模式只支持单轮行动
        _execute_single_round(task_key)

        logger.info(f"第 {execution_count} 次触发执行完成: {task_key}")

        # 在执行完成后调度下次执行（从最后一个智能体发言完成时开始计时）
        time_interval = config.get('timeInterval', 30)
        _schedule_next_execution(task_key, time_interval)

    except Exception as e:
        logger.error(f"执行触发行动时出错: {task_key}, {str(e)}")
        logger.error(traceback.format_exc())


def _check_execution_limits(task_key: str, config: Dict[str, Any]) -> tuple:
    """
    检查执行限制

    Returns:
        tuple: (can_continue: bool, reason: str)
    """
    if task_key not in _active_time_trigger_tasks:
        return False, 'task_not_found'

    task_info = _active_time_trigger_tasks[task_key]

    # 检查最大执行次数
    max_executions = config.get('maxExecutions', 0)
    if max_executions > 0 and task_info['execution_count'] >= max_executions:
        return False, 'reached_max_executions'

    # 检查总时长限制
    if config.get('enableTimeLimit', False):
        total_limit_minutes = config.get('totalTimeLimit', 1440)  # 默认1440分钟（24小时）
        elapsed_minutes = (get_current_time_with_timezone() - task_info['start_time']).total_seconds() / 60
        if elapsed_minutes >= total_limit_minutes:
            return False, 'reached_time_limit'

    return True, None


def _interrupt_running_agents(task_key: str):
    """中断正在执行的智能体"""
    if task_key in _active_time_trigger_tasks:
        task_info = _active_time_trigger_tasks[task_key]
        agent_threads = task_info.get('agent_threads', {})

        for agent_key, thread_info in agent_threads.items():
            if thread_info['status'] == 'running':
                # 设置中断标志
                thread_info['interrupt_flag'].set()
                # 等待线程结束（最多等待5秒）
                if thread_info['thread'].is_alive():
                    thread_info['thread'].join(timeout=5)
                thread_info['status'] = 'interrupted'

        # 清空当前执行状态
        task_info['agent_threads'] = {}


def _track_agent_execution(task_key: str, agent_id: int, thread: threading.Thread, interrupt_flag: threading.Event):
    """跟踪智能体执行状态"""
    if task_key in _active_time_trigger_tasks:
        task_info = _active_time_trigger_tasks[task_key]
        agent_key = f"{task_key}:{agent_id}"

        task_info['agent_threads'][agent_key] = {
            'thread': thread,
            'start_time': get_current_time_with_timezone(),
            'status': 'running',
            'interrupt_flag': interrupt_flag
        }


def _send_trigger_notification(task_key: str, execution_count: int):
    """发送触发通知 - 使用标准消息格式"""
    if task_key in _active_time_trigger_tasks:
        task_info = _active_time_trigger_tasks[task_key]
        result_queue = task_info.get('result_queue')

        if result_queue:
            try:
                # 使用新的数据库会话来避免事务冲突
                from sqlalchemy.orm import sessionmaker
                from app import db as app_db

                # 创建新的会话
                Session = sessionmaker(bind=app_db.engine)
                new_session = Session()

                try:
                    # 创建系统消息对象
                    system_msg = Message(
                        conversation_id=task_info['conversation_id'],
                        action_task_id=task_info['task_id'],
                        content=f"提示：时间触发任务第 {execution_count} 次执行开始",
                        role="system",
                        created_at=get_current_time_with_timezone()
                    )
                    new_session.add(system_msg)
                    new_session.commit()

                    # 使用标准格式化函数发送消息
                    formatted_msg = format_system_message(
                        message_id=str(system_msg.id),
                        content=system_msg.content,
                        created_at=system_msg.created_at.isoformat()
                    )
                    result_queue.put(serialize_message(formatted_msg))

                finally:
                    new_session.close()

            except Exception as e:
                logger.error(f"发送触发通知时出错: {str(e)}")
                # 即使发送通知失败，也不影响主流程


def _send_agent_info(result_queue: queue.Queue, agent_info: Dict, round_info: Dict):
    """发送智能体信息 - 使用标准格式"""
    if result_queue:
        formatted_msg = format_agent_info(
            turn_prompt=f"轮到智能体 {agent_info['name']} 发言",
            agent_id=str(agent_info['id']),
            agent_name=agent_info['name'],
            round_num=round_info.get('current', 1),
            total_rounds=999,  # 时间触发模式使用999表示无限轮次
            response_order=round_info.get('order', 1),
            total_agents=round_info.get('total_agents', 1)
        )
        result_queue.put(serialize_message(formatted_msg))


def _send_completion_message(result_queue: queue.Queue, message: str, message_ids: List[int]):
    """发送完成消息 - 使用标准格式"""
    if result_queue:
        formatted_msg = format_all_agents_done(
            message=message,
            message_ids=message_ids
        )
        result_queue.put(serialize_message(formatted_msg))


def _finalize_time_trigger_task(task_key: str, reason: str):
    """完成时间触发任务"""
    if task_key not in _active_time_trigger_tasks:
        return

    task_info = _active_time_trigger_tasks[task_key]

    try:
        # 中断正在执行的智能体
        _interrupt_running_agents(task_key)

        # 创建结束消息
        reason_messages = {
            'reached_max_executions': f"提示：达到最大执行次数限制，时间触发任务结束。共执行了 {task_info['execution_count']} 次。",
            'reached_time_limit': f"提示：达到总时长限制，时间触发任务结束。共执行了 {task_info['execution_count']} 次。",
            'manual_stop': "提示：时间触发任务被用户手动停止。"
        }

        end_content = reason_messages.get(reason, f"提示：时间触发任务结束。原因：{reason}")

        # 使用新的数据库会话来避免事务冲突
        try:
            from sqlalchemy.orm import sessionmaker
            from app import db as app_db

            # 创建新的会话
            Session = sessionmaker(bind=app_db.engine)
            new_session = Session()

            try:
                end_msg = Message(
                    conversation_id=task_info['conversation_id'],
                    action_task_id=task_info['task_id'],
                    content=end_content,
                    role="system",
                    created_at=get_current_time_with_timezone()
                )
                new_session.add(end_msg)
                new_session.commit()

                # 获取消息ID用于后续处理
                end_msg_id = end_msg.id
                end_msg_created_at = end_msg.created_at

            finally:
                new_session.close()

        except Exception as e:
            logger.error(f"创建结束消息时出错: {str(e)}")
            # 创建一个临时消息对象用于流式输出
            end_msg_id = 'temp'
            end_msg_created_at = get_current_time_with_timezone()

        # 流式模式发送结束消息
        result_queue = task_info.get('result_queue')
        if result_queue:
            formatted_msg = format_system_message(
                message_id=str(end_msg_id),
                content=end_content,
                created_at=end_msg_created_at.isoformat()
            )
            result_queue.put(serialize_message(formatted_msg))

            # 发送完成事件
            _send_completion_message(
                result_queue,
                f'时间触发任务已完成，共执行了 {task_info["execution_count"]} 次',
                [end_msg_id] if end_msg_id != 'temp' else []
            )

            # 结束流
            result_queue.put(None)

        # 取消定时器
        timer = task_info.get('timer')
        if timer:
            timer.cancel()

        # 从活动任务中移除
        del _active_time_trigger_tasks[task_key]

        logger.info(f"时间触发任务已完成: {task_key}, 原因: {reason}")

    except Exception as e:
        logger.error(f"完成时间触发任务时出错: {task_key}, {str(e)}")
        logger.error(traceback.format_exc())


def _execute_single_round(task_key: str):
    """执行单轮行动"""
    if task_key not in _active_time_trigger_tasks:
        logger.warning(f"任务已不存在，跳过单轮行动: {task_key}")
        return

    task_info = _active_time_trigger_tasks[task_key]
    conversation_id = task_info['conversation_id']
    config = task_info['config']
    result_queue = task_info.get('result_queue')

    try:
        # 获取会话中的非监督者智能体
        task_agents = ConversationAgent.query.join(Agent).filter(
            ConversationAgent.conversation_id == conversation_id,
            Agent.is_observer == False
        ).all()

        if not task_agents:
            logger.warning(f"会话中没有可用的任务智能体: {conversation_id}")
            return

        # 按ID排序智能体，确保顺序一致
        task_agents.sort(key=lambda ca: ca.id)

        # 获取智能体信息映射
        agent_map = {}
        for conv_agent in task_agents:
            agent = Agent.query.get(conv_agent.agent_id)
            if agent:
                role = Role.query.get(agent.role_id) if agent.role_id else None
                agent_map[conv_agent.agent_id] = {
                    'id': agent.id,
                    'name': agent.name,
                    'role_name': role.name if role else None
                }

        topic = config.get('topic', '请基于各自角色和知识，持续进行行动')

        # 每个智能体轮流发言一次
        for i, conv_agent in enumerate(task_agents):
            # 检查任务是否仍然活跃
            if task_key not in _active_time_trigger_tasks:
                logger.info(f"任务已被停止，退出单轮行动: {task_key}")
                return

            agent_id = conv_agent.agent_id
            agent_info = agent_map.get(agent_id, {'id': agent_id, 'name': f'智能体{agent_id}'})

            logger.info(f"开始执行智能体响应: {task_key}, agent_id={agent_id}, agent_name={agent_info.get('name')}")

            # 发送智能体信息
            if result_queue:
                _send_agent_info(result_queue, agent_info, {
                    'current': task_info['execution_count'],  # 使用当前执行计数作为轮次
                    'order': i + 1,
                    'total_agents': len(task_agents)
                })

            # 构建发言提示
            prompt = f"<div style='color: #A0A0A0;'>@{agent_info.get('name')} 请你基于之前的信息，继续执行你的任务。这是时间触发任务的第 {task_info['execution_count']} 次执行，你是第 {i+1} 个行动者。有任何进展请更新共享记忆。\n任务主题：{topic}</div>\n"

            # 执行智能体响应
            _execute_agent_response(task_key, agent_id, prompt)

            logger.info(f"智能体响应执行完成: {task_key}, agent_id={agent_id}")

        # 轮次完成后触发监督者检查
        try:
            from app.services.supervisor_event_manager import supervisor_event_manager
            supervisor_event_manager.on_round_completed(
                conversation_id=task_info['conversation_id'],
                round_number=task_info['execution_count']
            )
        except Exception as e:
            logger.error(f"触发轮次完成监督者检查时出错: {str(e)}")
            # 不影响主流程，继续执行

        logger.info(f"单轮行动执行完成: {task_key}")

    except Exception as e:
        logger.error(f"执行单轮行动时出错: {task_key}, {str(e)}")
        logger.error(traceback.format_exc())





def _execute_agent_response(task_key: str, agent_id: int, prompt: str):
    """执行智能体响应"""
    if task_key not in _active_time_trigger_tasks:
        return

    task_info = _active_time_trigger_tasks[task_key]

    try:
        from app.services.conversation_service import ConversationService

        # 获取流式输出回调
        result_queue = task_info.get('result_queue')
        sse_callback = None

        if result_queue:
            from app.services.conversation.stream_handler import wrap_stream_callback
            sse_callback = wrap_stream_callback(result_queue)

        # 直接调用智能体响应处理
        response_completed, error_msg = ConversationService._process_single_agent_response(
            task_id=task_info['task_id'],
            conversation_id=task_info['conversation_id'],
            human_message=None,  # 虚拟消息
            agent_id=agent_id,
            content=prompt,
            sse_callback=sse_callback,
            result_queue=None  # 不在这里结束流
        )

        if response_completed:
            logger.info(f"智能体 {agent_id} 响应完成: {task_key}")
        else:
            logger.warning(f"智能体 {agent_id} 响应失败: {error_msg}, {task_key}")
            # 即使响应失败，也不中断整个流程，继续执行其他智能体

    except Exception as e:
        logger.error(f"执行智能体响应时出错: agent_id={agent_id}, {str(e)}")
        logger.error(traceback.format_exc())
        # 即使出现异常，也不中断整个流程，继续执行其他智能体
