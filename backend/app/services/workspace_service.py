"""
项目空间服务模块

负责处理智能体项目空间的创建、读取和更新
"""
import os
from datetime import datetime
from typing import Dict, List

class WorkspaceService:
    """项目空间服务类，处理智能体项目文件的创建和管理"""

    def __init__(self):
        """初始化项目空间服务"""
        # 使用backend目录下的agent-workspace
        # 当前文件: backend/app/services/workspace_service.py
        # backend目录: ../../ (相对于当前文件)
        current_dir = os.path.dirname(os.path.abspath(__file__))  # backend/app/services/
        app_dir = os.path.dirname(current_dir)  # backend/app/
        backend_dir = os.path.dirname(app_dir)  # backend/
        self.workspace_dir = os.path.join(backend_dir, 'agent-workspace')


        # 确保项目空间目录存在
        os.makedirs(self.workspace_dir, exist_ok=True)

    def initialize_workspace_for_action_task(self, task_id: int, agent_ids: List[int], task_title: str = None, agent_info: List[Dict] = None) -> bool:
        """
        为新创建的行动任务初始化项目空间文件结构

        Args:
            task_id: 行动任务ID
            agent_ids: 参与该任务的智能体ID列表
            task_title: 行动任务标题（可选）
            agent_info: 智能体信息列表，每个元素是包含id、name、role_name等字段的字典（可选）

        Returns:
            bool: 初始化是否成功
        """
        try:
            # 创建行动任务目录（项目共享工作目录）
            task_dir = os.path.join(self.workspace_dir, f'ActionTask-{task_id}')

            # 如果目录已存在，先删除它
            if os.path.exists(task_dir):
                import shutil
                shutil.rmtree(task_dir)
                print(f"已删除现有的行动任务 {task_id} 项目共享工作目录")

            # 创建新的目录
            os.makedirs(task_dir, exist_ok=True)

            # 获取任务标题，如果没有提供则使用默认值
            task_title_display = task_title if task_title else f"任务 {task_id}"

            # 创建项目索引文件
            index_path = os.path.join(task_dir, 'ProjectIndex.md')
            self._create_workspace_index_file(index_path, task_id, agent_ids, task_title_display, agent_info)

            # 创建项目总结文件
            summary_path = os.path.join(task_dir, 'ProjectSummary.md')
            self._create_project_summary_file(summary_path, task_id, task_title_display, agent_info)

            # 为每个智能体创建工作目录和个人工作空间说明文件
            for i, agent_id in enumerate(agent_ids):
                # 获取智能体信息
                agent_name = None
                agent_role = None
                if agent_info and i < len(agent_info):
                    agent_name = agent_info[i].get('name')
                    agent_role = agent_info[i].get('role_name')

                # 如果没有提供智能体信息，使用默认值
                agent_display = f"{agent_name}[{agent_role}][ID: {agent_id}]" if agent_name and agent_role else f"智能体 {agent_id}"

                # 创建智能体工作目录
                agent_dir = os.path.join(task_dir, f'Agent-{agent_id}')
                os.makedirs(agent_dir, exist_ok=True)

                # 创建智能体个人工作空间说明文件
                agent_workspace_path = os.path.join(agent_dir, 'AgentWorkspace.md')
                self._create_agent_workspace_file(agent_workspace_path, task_id, agent_id, task_title_display, agent_display)

            print(f"已成功为行动任务 {task_id} 初始化项目空间文件结构")
            return True

        except Exception as e:
            print(f"为行动任务 {task_id} 初始化项目空间文件结构时出错: {str(e)}")
            return False



    def _create_workspace_index_file(self, file_path: str, task_id: int, agent_ids: List[int], task_title: str, agent_info: List[Dict] = None) -> None:
        """创建项目索引文件"""
        if not os.path.exists(file_path):
            content = f"""# {task_title} 项目索引

## 工作目录结构说明
- 项目共享工作目录: `ActionTask-{task_id}/`
- 项目总结文件: `ActionTask-{task_id}/ProjectSummary.md`

## 智能体工作目录
"""
            # 添加每个智能体的工作目录位置
            for i, agent_id in enumerate(agent_ids):
                # 获取智能体信息
                agent_name = None
                agent_role = None
                if agent_info and i < len(agent_info):
                    agent_name = agent_info[i].get('name')
                    agent_role = agent_info[i].get('role_name')

                # 如果没有提供智能体信息，使用默认值
                agent_display = f"{agent_name}[{agent_role}]" if agent_name and agent_role else f"智能体 {agent_id}"
                content += f"- {agent_display}[ID: {agent_id}] 工作目录: `ActionTask-{task_id}/Agent-{agent_id}/`\n"
                content += f"  - 个人工作空间说明: `ActionTask-{task_id}/Agent-{agent_id}/AgentWorkspace.md`\n"



            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

    def _create_project_summary_file(self, file_path: str, task_id: int, task_title: str, agent_info: List[Dict] = None) -> None:
        """创建项目总结文件"""
        if not os.path.exists(file_path):
            # 构建参与智能体信息
            agents_section = ""
            if agent_info and len(agent_info) > 0:
                for i, agent in enumerate(agent_info):
                    agent_name = agent.get('name', f'智能体{i+1}')
                    agent_role = agent.get('role_name', '未知角色')
                    agent_id = agent.get('id', f'{i+1}')
                    agents_section += f"- {agent_name}[{agent_role}][ID: {agent_id}]\n"
            else:
                agents_section = "- 暂无智能体信息\n"

            content = f"""# {task_title} 项目总结

行动任务"{task_title}"[ID: {task_id}]的项目总结记录。
创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
任务状态: 进行中

### 参与智能体
{agents_section}

## 项目记录
智能体可在此记录项目目标、执行过程、结果总结和后续行动。

"""
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

    def _create_agent_workspace_file(self, file_path: str, task_id: int, agent_id: int, task_title: str, agent_display: str) -> None:
        """创建智能体个人工作空间说明文件"""
        if not os.path.exists(file_path):
            content = f"""# {agent_display} 个人工作空间说明

## 工作目录说明
这是{agent_display}在行动任务"{task_title}"[ID: {task_id}]中的个人工作目录。

## 目录结构
- 工作目录: `ActionTask-{task_id}/Agent-{agent_id}/`
- 个人工作空间说明: `ActionTask-{task_id}/Agent-{agent_id}/AgentWorkspace.md`（本文件）
- 项目共享工作目录: `ActionTask-{task_id}/`

## 使用说明
- 智能体可以在个人工作目录 `Agent-{agent_id}/` 下创建任何文件和文件夹
- 项目共享工作目录 `ActionTask-{task_id}/` 可供所有智能体共享使用
- 本文件用于记录个人工作空间的使用说明和工作记录

## 重要信息
- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 工作记录与经验

"""
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)



    def get_workspace_files_for_task(self, task_id: int) -> Dict[str, List[Dict]]:
        """
        获取指定任务的所有项目文件信息，包括所有子目录中的文件

        Args:
            task_id: 行动任务ID

        Returns:
            Dict: 包含项目共享文件和智能体工作文件的字典
        """
        try:
            task_dir = os.path.join(self.workspace_dir, f'ActionTask-{task_id}')

            if not os.path.exists(task_dir):
                return {'shared_files': [], 'agent_workspaces': []}

            shared_files = []
            agent_workspaces = []

            # 递归扫描任务目录下的所有文件
            def scan_directory(directory, relative_base=''):
                """递归扫描目录获取所有项目文件"""
                try:
                    for item in os.listdir(directory):
                        item_path = os.path.join(directory, item)
                        relative_path = os.path.join(relative_base, item) if relative_base else item

                        if os.path.isfile(item_path) and item.endswith('.md'):
                            # 获取文件信息
                            stat = os.stat(item_path)
                            file_info = {
                                'title': self._extract_title_from_file(item_path) or item.replace('.md', ''),
                                'file_path': f'ActionTask-{task_id}/{relative_path}',
                                'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                                'updated_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                                'size': stat.st_size
                            }

                            # 根据文件位置和名称判断类型
                            if relative_base == '':
                                # 根目录下的文件视为项目共享文件
                                file_info['type'] = 'shared'
                                shared_files.append(file_info)
                            elif relative_base.startswith('Agent-'):
                                # Agent目录下的文件视为智能体工作文件
                                agent_id_str = relative_base.replace('Agent-', '')
                                try:
                                    agent_id = int(agent_id_str)
                                    file_info['type'] = 'agent'
                                    file_info['agent_id'] = agent_id
                                    file_info['agent_name'] = f'智能体 {agent_id}'
                                    agent_workspaces.append(file_info)
                                except ValueError:
                                    # 如果agent_id不是数字，仍然作为智能体工作文件处理
                                    file_info['type'] = 'agent'
                                    file_info['agent_name'] = agent_id_str
                                    agent_workspaces.append(file_info)
                            else:
                                # 其他子目录下的文件也视为项目共享文件
                                file_info['type'] = 'shared'
                                file_info['subfolder'] = relative_base
                                shared_files.append(file_info)

                        elif os.path.isdir(item_path):
                            # 递归扫描子目录
                            scan_directory(item_path, relative_path)

                except Exception as e:
                    print(f"扫描目录 {directory} 时出错: {str(e)}")

            # 开始扫描任务目录
            scan_directory(task_dir)

            return {
                'shared_files': shared_files,
                'agent_workspaces': agent_workspaces
            }

        except Exception as e:
            print(f"获取任务 {task_id} 项目文件失败: {str(e)}")
            return {'shared_files': [], 'agent_workspaces': []}

    def _extract_title_from_file(self, file_path: str) -> str:
        """
        从文件中提取标题（第一行的# 标题）

        Args:
            file_path: 文件路径

        Returns:
            str: 提取的标题，如果没有则返回None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                if first_line.startswith('# '):
                    return first_line[2:].strip()
        except Exception:
            pass
        return None

    def get_workspace_file_content(self, file_path: str) -> str:
        """
        获取项目文件内容

        Args:
            file_path: 相对于workspace_dir的文件路径

        Returns:
            str: 文件内容
        """
        try:
            full_path = os.path.join(self.workspace_dir, file_path)

            if not os.path.exists(full_path):
                raise FileNotFoundError(f"项目文件不存在: {file_path}")

            with open(full_path, 'r', encoding='utf-8') as f:
                return f.read()

        except Exception as e:
            print(f"读取项目文件 {file_path} 失败: {str(e)}")
            raise

    def update_workspace_file_content(self, file_path: str, content: str) -> bool:
        """
        更新项目文件内容

        Args:
            file_path: 相对于workspace_dir的文件路径
            content: 新的文件内容

        Returns:
            bool: 更新是否成功
        """
        try:
            full_path = os.path.join(self.workspace_dir, file_path)

            # 确保目录存在
            os.makedirs(os.path.dirname(full_path), exist_ok=True)

            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"已更新项目文件: {file_path}")
            return True

        except Exception as e:
            print(f"更新项目文件 {file_path} 失败: {str(e)}")
            raise

    def create_workspace_file(self, task_id: int, agent_id: int = None, title: str = '未命名文件', content: str = '', file_type: str = 'agent') -> str:
        """
        创建新的项目文件

        Args:
            task_id: 行动任务ID
            agent_id: 智能体ID（如果是共享文件则为None）
            title: 文件标题
            content: 文件内容
            file_type: 文件类型（'agent' 或 'shared'）

        Returns:
            str: 创建的文件路径
        """
        try:
            task_dir = os.path.join(self.workspace_dir, f'ActionTask-{task_id}')
            os.makedirs(task_dir, exist_ok=True)

            if file_type == 'shared':
                # 创建共享项目文件
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f'SharedWorkspace_{timestamp}.md'
                file_path = os.path.join(task_dir, filename)
                relative_path = f'ActionTask-{task_id}/{filename}'
            else:
                # 创建智能体项目文件
                if not agent_id:
                    raise ValueError("创建智能体项目文件时必须提供agent_id")

                agent_dir = os.path.join(task_dir, f'Agent-{agent_id}')
                os.makedirs(agent_dir, exist_ok=True)

                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f'Workspace_{timestamp}.md'
                file_path = os.path.join(agent_dir, filename)
                relative_path = f'ActionTask-{task_id}/Agent-{agent_id}/{filename}'

            # 写入文件内容
            file_content = f"""# {title}

创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{content}
"""

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(file_content)

            print(f"已创建记忆文件: {relative_path}")
            return relative_path

        except Exception as e:
            print(f"创建项目文件失败: {str(e)}")
            raise

    def delete_workspace_file(self, file_path: str) -> bool:
        """
        删除项目文件

        Args:
            file_path: 相对于workspace_dir的文件路径

        Returns:
            bool: 删除是否成功
        """
        try:
            full_path = os.path.join(self.workspace_dir, file_path)

            if not os.path.exists(full_path):
                print(f"项目文件不存在: {file_path}")
                return True  # 文件不存在视为删除成功

            os.remove(full_path)
            print(f"已删除项目文件: {file_path}")
            return True

        except Exception as e:
            print(f"删除项目文件 {file_path} 失败: {str(e)}")
            raise

    def create_workspace_template(self, source_file_path: str, template_name: str, template_description: str = '') -> dict:
        """
        从项目文件创建模板

        Args:
            source_file_path: 源项目文件路径
            template_name: 模板名称
            template_description: 模板描述

        Returns:
            dict: 创建的模板信息
        """
        try:
            from app.models import WorkspaceTemplate
            from app.extensions import db

            # 读取源文件内容
            source_content = self.get_workspace_file_content(source_file_path)

            # 根据源文件路径推断分类
            template_category = 'agent'  # 默认分类
            if 'ProjectSummary' in source_file_path or 'ProjectIndex' in source_file_path:
                template_category = 'shared'
            elif 'Skills' in source_file_path or 'Experience' in source_file_path:
                template_category = 'skills'

            # 创建数据库记录
            template = WorkspaceTemplate(
                name=template_name,
                description=template_description,
                category=template_category,
                content=source_content,
                source_file_path=source_file_path,
                is_active=True
            )

            db.session.add(template)
            db.session.commit()

            print(f"已创建工作空间模板: {template.id} - {template.name}")

            return {
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'category': template.category,
                'content': template.content,
                'source_file_path': template.source_file_path,
                'created_at': template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else ''
            }

        except Exception as e:
            print(f"创建工作空间模板失败: {str(e)}")
            raise

    def create_new_workspace_template(self, template_name: str, template_content: str,
                                 template_description: str = '', template_category: str = 'agent') -> dict:
        """
        创建新的工作空间模板

        Args:
            template_name: 模板名称
            template_content: 模板内容
            template_description: 模板描述
            template_category: 模板分类

        Returns:
            dict: 创建的模板信息
        """
        try:
            from app.models import WorkspaceTemplate
            from app.extensions import db

            # 创建数据库记录
            template = WorkspaceTemplate(
                name=template_name,
                description=template_description,
                category=template_category,
                content=template_content,
                is_active=True
            )

            db.session.add(template)
            db.session.commit()

            print(f"已创建新工作空间模板: {template.id} - {template.name}")

            return {
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'category': template.category,
                'content': template.content,
                'created_at': template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else ''
            }

        except Exception as e:
            print(f"创建新工作空间模板失败: {str(e)}")
            raise

    def delete_workspace_for_action_task(self, task_id: int) -> bool:
        """
        删除行动任务的所有项目文件

        Args:
            task_id: 行动任务ID

        Returns:
            bool: 删除是否成功
        """
        try:
            # 获取行动任务目录路径
            task_dir = os.path.join(self.memory_dir, f'ActionTask-{task_id}')

            # 检查目录是否存在
            if not os.path.exists(task_dir):
                print(f"行动任务 {task_id} 的项目空间目录不存在")
                return True  # 目录不存在视为删除成功

            # 递归删除目录及其内容
            import shutil
            shutil.rmtree(task_dir)

            print(f"已成功删除行动任务 {task_id} 的项目文件")
            return True

        except Exception as e:
            print(f"删除行动任务 {task_id} 的项目文件时出错: {str(e)}")
            return False

    def get_workspace_templates(self) -> list:
        """
        获取工作空间模板列表

        Returns:
            list: 模板列表
        """
        try:
            from app.models import WorkspaceTemplate
            from app.extensions import db

            templates = WorkspaceTemplate.query.filter_by(is_active=True).all()

            template_list = []
            for template in templates:
                template_info = {
                    'id': template.id,
                    'name': template.name,
                    'description': template.description or '',
                    'category': template.category,
                    'content': template.content,
                    'source_file_path': template.source_file_path,
                    'created_at': template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else '',
                    'updated_at': template.updated_at.strftime('%Y-%m-%d %H:%M:%S') if template.updated_at else ''
                }
                template_list.append(template_info)

            return template_list

        except Exception as e:
            print(f"获取工作空间模板列表失败: {str(e)}")
            return []



    def update_workspace_template(self, template_id: int, template_name: str,
                             template_content: str, template_description: str = '',
                             template_category: str = 'agent') -> dict:
        """
        更新工作空间模板

        Args:
            template_id: 模板ID
            template_name: 模板名称
            template_content: 模板内容
            template_description: 模板描述
            template_category: 模板分类

        Returns:
            dict: 更新后的模板信息
        """
        try:
            from app.models import WorkspaceTemplate
            from app.extensions import db

            template = WorkspaceTemplate.query.get(template_id)
            if not template:
                raise ValueError(f"模板不存在: {template_id}")

            # 更新模板信息
            template.name = template_name
            template.content = template_content
            template.description = template_description
            template.category = template_category

            db.session.commit()

            # 返回更新后的模板信息
            return {
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'category': template.category,
                'content': template.content,
                'updated_at': template.updated_at.strftime('%Y-%m-%d %H:%M:%S') if template.updated_at else ''
            }

        except Exception as e:
            print(f"更新工作空间模板失败: {str(e)}")
            raise

    def delete_workspace_template(self, template_id: int) -> bool:
        """
        删除工作空间模板

        Args:
            template_id: 模板ID

        Returns:
            bool: 删除是否成功
        """
        try:
            from app.models import WorkspaceTemplate
            from app.extensions import db

            template = WorkspaceTemplate.query.get(template_id)
            if not template:
                print(f"模板不存在: {template_id}")
                return True  # 模板不存在视为删除成功

            # 软删除：设置为不活跃
            template.is_active = False
            db.session.commit()

            print(f"已成功删除工作空间模板: {template_id}")
            return True

        except Exception as e:
            print(f"删除工作空间模板失败: {str(e)}")
            return False

# 创建全局实例
workspace_service = WorkspaceService()
