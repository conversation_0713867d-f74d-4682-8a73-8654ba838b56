"""
模型客户端模块

处理模型API调用，发送流式模型请求，处理模型响应
统一ModelClient和ModelService功能，支持测试和生产两种场景
"""
import json
import logging
import traceback
import requests
import threading
import queue
from typing import Dict, List, Any, Callable, Optional, Tuple

from config import DEBUG_LLM_RESPONSE
from app.services.conversation.event_types import *
from app.services.conversation.observer import event_manager
# 导入连接管理器
from app.services.conversation.connection_manager import connection_manager

logger = logging.getLogger(__name__)

# 平台标准参数到供应商参数的映射
PROVIDER_PARAMETER_MAPPING = {
    'openai': {
        'temperature': 'temperature',
        'max_tokens': 'max_tokens',
        'top_p': 'top_p',
        'frequency_penalty': 'frequency_penalty',
        'presence_penalty': 'presence_penalty',
        'stop_sequences': 'stop'
    },
    'anthropic': {
        'temperature': 'temperature',
        'max_tokens': 'max_tokens',
        'top_p': 'top_p',
        'stop_sequences': 'stop_sequences',
        # frequency_penalty 和 presence_penalty 不支持，会被过滤掉
    },
    'google': {
        'temperature': 'temperature',
        'max_tokens': 'max_tokens',
        'top_p': 'top_p',
        # frequency_penalty, presence_penalty, stop_sequences 不支持
    },
    'ollama': {
        'temperature': 'temperature',
        'max_tokens': 'max_tokens',
        'top_p': 'top_p',
        'frequency_penalty': 'frequency_penalty',
        'presence_penalty': 'presence_penalty',
        'stop_sequences': 'stop'
    },
    'gpustack': {
        'temperature': 'temperature',
        'max_tokens': 'max_tokens',
        'top_p': 'top_p',
        'frequency_penalty': 'frequency_penalty',
        'presence_penalty': 'presence_penalty',
        'stop_sequences': 'stop'
    }
}

# 全局变量，用于跟踪活动的请求
_active_requests = {}
_active_requests_lock = threading.Lock()

# 注册和取消请求的函数
def register_request(task_id: int, conversation_id: int, agent_id: str, response: requests.Response) -> str:
    """
    注册请求，用于后续取消

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID
        agent_id: 智能体ID
        response: 请求响应对象

    Returns:
        str: 请求ID
    """
    # 生成请求ID
    if agent_id:
        request_id = f"{task_id}:{conversation_id}:{agent_id}"
    else:
        request_id = f"{task_id}:{conversation_id}"

    # 注册请求
    with _active_requests_lock:
        _active_requests[request_id] = response

    logger.info(f"已注册请求: {request_id}")
    return request_id

def cancel_request(task_id: int, conversation_id: int, agent_id: str = None) -> bool:
    """
    取消请求 - 使用连接管理器直接断开连接

    Args:
        task_id: 行动任务ID
        conversation_id: 会话ID
        agent_id: 智能体ID，如果提供则只取消该智能体的请求

    Returns:
        bool: 是否成功取消请求
    """
    # 生成请求ID
    if agent_id:
        request_id = f"{task_id}:{conversation_id}:{agent_id}"
    else:
        request_id = f"{task_id}:{conversation_id}"

    # 记录取消请求的开始
    logger.info(f"[连接取消] 开始取消请求: {request_id}")

    # 首先尝试使用连接管理器强制关闭连接
    success = connection_manager.force_close_connection(request_id)

    if success:
        logger.info(f"[连接取消] 连接管理器成功关闭连接: {request_id}")
    else:
        logger.warning(f"[连接取消] 连接管理器未找到连接: {request_id}")

    # 作为备用，也清理旧的请求跟踪机制
    with _active_requests_lock:
        # 查找匹配的请求
        matching_keys = [k for k in _active_requests.keys() if k.startswith(request_id)]

        if not matching_keys and request_id in _active_requests:
            matching_keys = [request_id]

        if matching_keys:
            for matching_key in matching_keys:
                # 从活动请求中移除
                if matching_key in _active_requests:
                    del _active_requests[matching_key]
                    logger.info(f"[连接取消] 已从旧请求跟踪中移除: {matching_key}")
            success = True
        else:
            logger.info(f"[连接取消] 旧请求跟踪中未找到请求: {request_id}")

    # 总是返回成功，避免前端卡住
    return True

class ModelClient:
    """统一模型客户端 - 支持测试和生产两种场景"""

    def __init__(self):
        """初始化模型客户端"""
        logger.debug("初始化统一模型客户端")

        # 供应商适配器映射
        self.provider_adapters = {
            'openai': self._handle_openai_request,
            'anthropic': self._handle_anthropic_request,
            'google': self._handle_google_request,
            'ollama': self._handle_ollama_request,
            'gpustack': self._handle_gpustack_request,
        }

    def send_request(self, api_url: str, api_key: str, messages: List[Dict[str, str]],
                    model: str, is_stream: bool = False, callback: Optional[Callable] = None,
                    agent_info: Optional[Dict[str, Any]] = None, **kwargs) -> str:
        """
        发送模型请求

        Args:
            api_url: API URL
            api_key: API密钥
            messages: 消息列表
            model: 模型名称
            is_stream: 是否使用流式响应
            callback: 回调函数，如果提供则使用流式响应
            agent_info: 智能体信息(可选)，包含角色和工具信息
            **kwargs: 其他参数

        Returns:
            str: 模型响应内容
        """
        try:
            # 检测提供商类型
            detected_provider = self._detect_provider(api_url, kwargs.get('config'), kwargs.get('provider'))

            # 根据提供商规范化 API URL 和设置请求头
            if detected_provider == 'anthropic':
                # Anthropic API 使用 /v1/messages 端点
                if not api_url.endswith('/'):
                    api_url = api_url.rstrip('/')
                if not api_url.endswith('/v1/messages'):
                    if '/v1/messages' in api_url:
                        # URL已包含完整路径
                        pass
                    elif '/v1' in api_url:
                        api_url = f"{api_url}/messages"
                    else:
                        api_url = f"{api_url}/v1/messages"

                headers = {
                    "Content-Type": "application/json",
                    "x-api-key": api_key,
                    "anthropic-version": "2023-06-01"
                }

                # Anthropic API 格式：system 作为顶级参数
                system_message = None
                user_messages = []

                for msg in messages:
                    if msg.get('role') == 'system':
                        system_message = msg.get('content', '')
                    else:
                        user_messages.append(msg)

                payload = {
                    "model": model,
                    "messages": user_messages,
                    "stream": is_stream
                }

                if system_message:
                    payload["system"] = system_message
            else:
                # OpenAI 兼容格式（默认）
                if not api_url.endswith('/'):
                    api_url = api_url.rstrip('/')
                if not api_url.endswith('/chat/completions'):
                    api_url = f"{api_url}/chat/completions"

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}"
                }

                # 构建请求payload
                payload = {
                    "model": model,
                    "messages": messages,
                    "stream": is_stream
                }

            # 添加其他参数（根据提供商过滤）
            excluded_keys = ['agent_info', 'task_id', 'conversation_id', 'config', 'provider']

            for key, value in kwargs.items():
                if value is not None and key not in excluded_keys:
                    if key == 'max_tokens' and value == 0:
                        continue

                    # 根据提供商过滤参数
                    if detected_provider == 'anthropic':
                        # Anthropic 支持的参数
                        if key in ['temperature', 'max_tokens', 'top_p', 'top_k', 'stop_sequences']:
                            payload[key] = value
                    else:
                        # OpenAI 兼容参数
                        payload[key] = value

            # 添加工具定义(如果有) - 目前只对OpenAI兼容格式支持
            if detected_provider != 'anthropic' and agent_info and 'tools' in agent_info and agent_info['tools']:
                payload['tools'] = agent_info['tools']
                if 'tool_choice' not in payload:
                    payload['tool_choice'] = "auto"

            # 根据调试开关输出详细日志
            if DEBUG_LLM_RESPONSE:
                # 恢复详细的日志输出
                logger.debug("\n" + "="*80)
                logger.debug(f"[API请求] 发送到LLM的{'流式' if is_stream else ''}原始数据:")
                logger.debug("-"*40)

                # 打印智能体和角色信息
                if agent_info:
                    logger.debug(f"智能体信息: ID={agent_info.get('id')}, 名称={agent_info.get('name')}")
                    logger.debug(f"角色信息: ID={agent_info.get('role_id')}, 名称={agent_info.get('role_name')}")
                    if 'capabilities' in agent_info and agent_info['capabilities']:
                        logger.debug(f"角色能力: {', '.join(agent_info['capabilities'])}")
                    if 'tool_names' in agent_info and agent_info['tool_names']:
                        logger.debug(f"角色工具: {', '.join(agent_info['tool_names'])}")

                logger.debug(f"模型信息: {model}, 温度={kwargs.get('temperature', 0.7)}")
                logger.debug("-"*40)

                # 打印消息内容（仅在非流式模式下）
                if not is_stream:
                    logger.debug("消息内容:")
                    for i, msg in enumerate(messages):
                        logger.debug(f"[{i+1}] {msg.get('role')}: {msg.get('content')[:100]}..." if len(msg.get('content', '')) > 100 else f"[{i+1}] {msg.get('role')}: {msg.get('content')}")

            logger.debug("*"*80 + "\n")
            logger.debug("完整请求体:")
            logger.debug(json.dumps(payload, ensure_ascii=False, indent=2))
            logger.debug("*"*80 + "\n")

            # 使用已检测的提供商信息进行日志记录
            provider_name = detected_provider if detected_provider else 'unknown'
            logger.info(f"[ModelClient] {'流式' if is_stream else ''}请求: 提供商={provider_name}, URL={api_url}, 模型={model}")

            # 发送请求前发出事件通知
            event_data = {
                'api_url': api_url,
                'model': model,
                'is_stream': is_stream,
                'agent_info': agent_info
            }

            if is_stream:
                event_manager.emit(STREAM_STARTED, event_data)

            # 发送请求
            if DEBUG_LLM_RESPONSE:
                logger.debug(f"[API请求] 正在发送HTTP请求到: {api_url}")

            # 创建会话对象，以便能够取消请求
            session = requests.Session()

            # 如果是流式响应，使用连接管理器注册连接
            request_id = None
            if is_stream:
                task_id = kwargs.get('task_id')
                conversation_id = kwargs.get('conversation_id')
                agent_id = agent_info.get('id') if agent_info else None

                # 确保智能体ID是字符串类型
                if agent_id is not None:
                    agent_id = str(agent_id)

                # 如果提供了任务ID和会话ID，注册连接
                if task_id and conversation_id:
                    # 生成请求ID
                    if agent_id:
                        request_id = f"{task_id}:{conversation_id}:{agent_id}"
                    else:
                        request_id = f"{task_id}:{conversation_id}"

                    # 使用连接管理器注册连接
                    connection_manager.register_connection(request_id, session)
                    logger.info(f"[连接管理] 已注册连接: {request_id}")

                    # 同时保留旧的请求跟踪机制作为备用
                    with _active_requests_lock:
                        _active_requests[request_id] = None

            # 发送请求 - 设置合理的超时时间
            # 读取超时：用于流式响应的读取超时
            # 连接超时：用于建立连接的超时
            timeout = (30, 300)  # (连接超时30秒, 读取超时300秒)

            response = session.post(
                api_url,
                headers=headers,
                json=payload,
                stream=is_stream,
                timeout=timeout
            )

            # 检查响应状态
            status_code = response.status_code
            if DEBUG_LLM_RESPONSE:
                logger.debug(f"[API请求] 收到HTTP响应状态码: {status_code}")
                if status_code != 200:
                    logger.error(f"[API请求] 错误: 服务器返回非200状态码: {status_code}")
                    logger.error(f"[API请求] 响应内容: {response.text}")

            # 如果状态码不是200，获取详细错误信息
            if status_code != 200:
                error_detail = ""
                try:
                    error_json = response.json()
                    if 'error' in error_json and 'message' in error_json['error']:
                        error_detail = error_json['error']['message']
                    else:
                        error_detail = response.text
                except:
                    error_detail = response.text

                # 抛出带有详细错误信息的异常
                raise requests.exceptions.HTTPError(
                    f"[API请求] 错误: 服务器返回非200状态码: {status_code}\n[API请求] 响应内容: {error_detail}",
                    response=response
                )

            # 如果状态码是200，继续处理
            response.raise_for_status()

            # 如果是流式响应，更新连接管理器中的响应对象
            if is_stream and request_id:
                # 更新连接管理器中的响应对象
                connection_manager.update_connection(request_id, response=response)
                logger.info(f"[连接管理] 已更新响应对象: {request_id}")

                # 同时更新旧的请求跟踪机制
                task_id = kwargs.get('task_id')
                conversation_id = kwargs.get('conversation_id')
                agent_id = agent_info.get('id') if agent_info else None
                if agent_id is not None:
                    agent_id = str(agent_id)
                register_request(task_id, conversation_id, agent_id, response)

            # 处理流式响应
            if is_stream:
                if DEBUG_LLM_RESPONSE:
                    logger.debug("[API请求] 开始处理流式响应...")

                # 延迟导入，避免循环依赖
                from app.services.conversation.stream_handler import handle_streaming_response, register_streaming_task

                # 准备API配置，用于在工具调用后再次调用LLM
                api_config = {
                    "api_url": api_url,
                    "api_key": api_key,
                    "model": model,
                    "agent_info": agent_info
                }
                # 添加其他参数
                for key, value in kwargs.items():
                    if value is not None:
                        api_config[key] = value

                # 如果有任务ID和会话ID，注册流式任务
                task_id = kwargs.get('task_id')
                conversation_id = kwargs.get('conversation_id')
                agent_id = agent_info.get('id') if agent_info else None

                # 确保智能体ID是字符串类型
                if agent_id is not None:
                    agent_id = str(agent_id)

                # 记录调试信息
                if DEBUG_LLM_RESPONSE:
                    logger.debug(f"[API请求] 准备注册流式任务: 任务ID={task_id}, 会话ID={conversation_id}, 智能体ID={agent_id}")
                    logger.debug(f"[API请求] 回调对象: {callback}, 是否有result_queue: {hasattr(callback, 'result_queue')}")

                # 如果提供了任务ID和会话ID，注册流式任务
                if task_id and conversation_id and hasattr(callback, 'result_queue'):
                    # 即使没有智能体ID，也注册常规流式任务
                    register_streaming_task(
                        task_id=task_id,
                        conversation_id=conversation_id,
                        result_queue=callback.result_queue,
                        agent_id=agent_id
                    )

                    if DEBUG_LLM_RESPONSE:
                        if agent_id:
                            logger.debug(f"[API请求] 已注册智能体流式任务: 任务ID={task_id}, 会话ID={conversation_id}, 智能体ID={agent_id}")
                        else:
                            logger.debug(f"[API请求] 已注册常规流式任务: 任务ID={task_id}, 会话ID={conversation_id}")

                # 传递原始消息和API配置给handle_streaming_response
                return handle_streaming_response(
                    response,
                    callback,
                    original_messages=messages,
                    api_config=api_config
                )
            else:
                # 处理普通响应
                result = response.json()
                if DEBUG_LLM_RESPONSE:
                    logger.debug(f"[API请求] 收到普通响应: {json.dumps(result, ensure_ascii=False)[:200]}...")

                if result.get('choices') and result['choices'][0].get('message', {}).get('content'):
                    return result['choices'][0]['message']['content']
                else:
                    error_msg = "Error: 无法从响应中获取内容"
                    if DEBUG_LLM_RESPONSE:
                        logger.error(f"[API请求] {error_msg}")

                    # 发出错误事件
                    event_manager.emit(STREAM_ERROR, {
                        **event_data,
                        'error': error_msg
                    })
                    # 如果有回调函数和智能体信息，使用format_agent_error_done格式化错误消息
                    if callback and agent_info:
                        from app.services.conversation.message_formater import format_agent_error_done

                        # 获取智能体和角色信息
                        agent_id = str(agent_info.get('id', 'unknown'))
                        agent_name = agent_info.get('name', '智能体')
                        role_name = agent_info.get('role_name', '未知角色')

                        # 格式化错误消息 - 直接使用完整错误信息
                        formatted_error = format_agent_error_done(
                            agent_id=agent_id,
                            agent_name=agent_name,
                            role_name=role_name,
                            error_content=error_msg
                        )

                        # 发送格式化的错误消息
                        try:
                            # 尝试使用两个参数调用
                            callback(None, formatted_error["meta"])
                        except TypeError:
                            # 如果回调函数只接受一个参数，则使用一个参数调用
                            try:
                                callback(f"\n[错误] {error_msg}")
                            except:
                                logger.warning(f"[ModelClient] 警告: 无法调用回调函数")

                    return error_msg

        except requests.exceptions.RequestException as e:
            error_msg = f"API请求失败: {str(e)}"
            logger.error(f"[ModelClient] 错误: {error_msg}")
            if DEBUG_LLM_RESPONSE:
                logger.error(f"[API请求] 异常详情: {traceback.format_exc()}")

            # 检查是否是由于取消导致的异常
            is_cancelled = False
            task_id = kwargs.get('task_id')
            conversation_id = kwargs.get('conversation_id')
            agent_id = agent_info.get('id') if agent_info else None

            # 构建请求ID
            if agent_id:
                request_id = f"{task_id}:{conversation_id}:{agent_id}"
            else:
                request_id = f"{task_id}:{conversation_id}"

            # 检查是否是已注册的请求
            with _active_requests_lock:
                if request_id in _active_requests:
                    is_cancelled = True
                    logger.info(f"[ModelClient] 请求已被取消: {request_id}")
                    # 从活动请求中移除
                    del _active_requests[request_id]

            # 如果不是由于取消导致的异常，发出错误事件
            if not is_cancelled:
                # 发出错误事件
                event_manager.emit(STREAM_ERROR, {
                    'api_url': api_url,
                    'model': model,
                    'is_stream': is_stream,
                    'error': error_msg,
                    'agent_info': agent_info  # 添加智能体信息
                })

                # 如果有回调函数和智能体信息，使用format_agent_error_done格式化错误消息
                if callback and agent_info:
                    from app.services.conversation.message_formater import format_agent_error_done

                    # 获取智能体和角色信息
                    agent_id = str(agent_info.get('id', 'unknown'))
                    agent_name = agent_info.get('name', '智能体')
                    role_name = agent_info.get('role_name', '未知角色')

                    # 格式化错误消息 - 直接使用完整错误信息
                    formatted_error = format_agent_error_done(
                        agent_id=agent_id,
                        agent_name=agent_name,
                        role_name=role_name,
                        error_content=error_msg
                    )

                    # 发送格式化的错误消息
                    try:
                        # 尝试使用两个参数调用
                        callback(None, formatted_error["meta"])
                    except TypeError:
                        # 如果回调函数只接受一个参数，则使用一个参数调用
                        try:
                            callback(f"\n[错误] {error_msg}")
                        except:
                            logger.warning(f"[ModelClient] 警告: 无法调用回调函数")
                elif callback:
                    callback(f"\n[错误] {error_msg}")

            return f"Error: {error_msg}"

        except Exception as e:
            error_msg = f"处理消息时出错: {str(e)}"
            logger.error(f"[ModelClient] 错误: {error_msg}")
            if DEBUG_LLM_RESPONSE:
                logger.error(f"[API请求] 异常详情: {traceback.format_exc()}")

            # 检查是否是由于取消导致的异常
            is_cancelled = False
            task_id = kwargs.get('task_id')
            conversation_id = kwargs.get('conversation_id')
            agent_id = agent_info.get('id') if agent_info else None

            # 构建请求ID（只有在有task_id和conversation_id时才构建）
            request_id = None
            if task_id and conversation_id:
                if agent_id:
                    request_id = f"{task_id}:{conversation_id}:{agent_id}"
                else:
                    request_id = f"{task_id}:{conversation_id}"

            # 检查是否是已注册的请求（只有在有request_id时才检查）
            if request_id:
                with _active_requests_lock:
                    if request_id in _active_requests:
                        is_cancelled = True
                        logger.info(f"[ModelClient] 请求已被取消: {request_id}")
                        # 从活动请求中移除
                        del _active_requests[request_id]

            # 如果不是由于取消导致的异常，发出错误事件
            if not is_cancelled:
                # 发出错误事件
                event_manager.emit(STREAM_ERROR, {
                    'api_url': api_url,
                    'model': model,
                    'is_stream': is_stream,
                    'error': error_msg,
                    'agent_info': agent_info  # 添加智能体信息
                })
                # 如果有回调函数和智能体信息，使用format_agent_error_done格式化错误消息
                if callback and agent_info:
                    from app.services.conversation.message_formater import format_agent_error_done

                    # 获取智能体和角色信息
                    agent_id = str(agent_info.get('id', 'unknown'))
                    agent_name = agent_info.get('name', '智能体')
                    role_name = agent_info.get('role_name', '未知角色')

                    # 格式化错误消息 - 直接使用完整错误信息
                    formatted_error = format_agent_error_done(
                        agent_id=agent_id,
                        agent_name=agent_name,
                        role_name=role_name,
                        error_content=error_msg
                    )

                    # 发送格式化的错误消息
                    try:
                        # 尝试使用两个参数调用
                        callback(None, formatted_error["meta"])
                    except TypeError:
                        # 如果回调函数只接受一个参数，则使用一个参数调用
                        try:
                            callback(f"\n[错误] {error_msg}")
                        except:
                            logger.warning(f"[ModelClient] 警告: 无法调用回调函数")
                elif callback:
                        callback(f"\n[错误] {error_msg}")

                return f"Error: {error_msg}"

    # === 测试方法 (兼容ModelService接口) ===

    def test_model(self, config, prompt: str, system_prompt: str = None,
                  use_stream: bool = False, callback: Optional[Callable] = None, **kwargs):
        """
        测试模型配置 - ModelConfig层使用，兼容原ModelService.test_model接口

        Args:
            config: ModelConfig对象，只使用基础模型配置参数
            prompt: 测试提示词
            system_prompt: 系统提示词
            use_stream: 是否使用流式响应
            callback: 回调函数
            **kwargs: 额外参数

        Returns:
            dict: 测试结果，包含success状态和消息
        """
        try:
            # 准备测试消息
            test_messages = [
                {"role": "system", "content": system_prompt or "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ]

            # 构建参数
            params, prompt_info = self._build_parameters_from_hierarchy(
                model_config=config,
                runtime_params=kwargs
            )

            # 检测供应商
            provider = self._detect_provider(config=config)

            # 映射和过滤参数
            filtered_params = self._map_and_filter_parameters(provider, **params)

            if use_stream:
                # 流式测试
                def handle_stream_chunk(content):
                    if callback:
                        callback(content)

                # 构建agent_info以传递provider信息
                agent_info = {
                    'provider': config.provider if hasattr(config, 'provider') else 'unknown',
                    'name': '模型测试系统',
                    'role_name': '模型测试器'
                }

                # 调用现有的send_request方法进行流式测试
                result = self.send_request(
                    api_url=config.base_url,
                    api_key=config.api_key,
                    messages=test_messages,
                    model=config.model_id,
                    is_stream=True,
                    callback=handle_stream_chunk,
                    agent_info=agent_info,
                    **filtered_params
                )

                # 检查响应是否包含错误信息
                if result.startswith('Error:'):
                    return {
                        'success': False,
                        'message': result,
                        'stream': True
                    }

                return {
                    'success': True,
                    'message': '流式API连接测试成功',
                    'response': result,
                    'stream': True
                }
            else:
                # 构建agent_info以传递provider信息
                agent_info = {
                    'provider': config.provider if hasattr(config, 'provider') else 'unknown',
                    'name': '模型测试系统',
                    'role_name': '模型测试器'
                }

                # 非流式测试
                result = self.send_request(
                    api_url=config.base_url,
                    api_key=config.api_key,
                    messages=test_messages,
                    model=config.model_id,
                    is_stream=False,
                    agent_info=agent_info,
                    **filtered_params
                )

                # 检查响应是否包含错误信息
                if result.startswith('Error:'):
                    return {
                        'success': False,
                        'message': result
                    }

                return {
                    'success': True,
                    'message': f'API连接测试成功: {result[:50]}...',
                    'response': result
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'API连接测试失败: {str(e)}'
            }

    def test_model_stream(self, config, prompt: str, system_prompt: str = None,
                         callback: Optional[Callable] = None, **kwargs):
        """
        流式测试模型配置 - Role层使用，兼容原ModelService.test_model_stream接口

        Args:
            config: ModelConfig对象，包含基础参数
            prompt: 测试提示词
            system_prompt: 系统提示词
            callback: 流式回调函数
            **kwargs: 角色参数 (temperature, top_p, frequency_penalty, presence_penalty, max_tokens, stop_sequences)

        Returns:
            dict: 测试结果，包含success状态、响应内容和消息
        """
        try:
            logger.info(f"[ModelClient] 开始流式测试: URL={config.base_url}, 模型={config.model_id}")

            # 准备测试消息
            test_messages = [
                {"role": "system", "content": system_prompt or "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ]

            # 构建参数 - 将kwargs作为role_params传递
            params, prompt_info = self._build_parameters_from_hierarchy(
                model_config=config,
                role_params=kwargs
            )

            # 检测供应商
            provider = self._detect_provider(config=config)

            # 映射和过滤参数
            filtered_params = self._map_and_filter_parameters(provider, **params)

            # 定义流式回调函数
            def streaming_callback(content):
                if content and callback:
                    # 确保内容是字符串
                    if isinstance(content, bytes):
                        try:
                            content = content.decode('utf-8')
                        except UnicodeDecodeError:
                            content = content.decode('utf-8', errors='replace')

                    # 直接调用回调函数
                    callback(content)

            # 构建agent_info以传递provider信息
            agent_info = {
                'provider': config.provider if hasattr(config, 'provider') else 'unknown',
                'name': '模型流式测试系统',
                'role_name': '模型流式测试器'
            }

            # 调用现有的send_request方法进行流式测试
            result = self.send_request(
                api_url=config.base_url,
                api_key=config.api_key,
                messages=test_messages,
                model=config.model_id,
                is_stream=True,
                callback=streaming_callback,
                agent_info=agent_info,
                **filtered_params
            )

            # 如果响应以"Error:"开头，表示出错
            if result and result.startswith("Error:"):
                return {
                    'success': False,
                    'message': result
                }

            return {
                'success': True,
                'response': result,
                'message': f'模型流式测试成功'
            }

        except Exception as e:
            logger.error(f"[ModelClient] 模型流式测试出错: {str(e)}")
            error_message = f"API连接测试失败: {str(e)}"

            # 如果设置了回调，通知错误
            if callback:
                callback(f"Error: {error_message}")

            return {
                'success': False,
                'message': error_message
            }

    # === 内部方法 ===

    def _detect_provider(self, api_url: str = None, config = None, provider: str = None) -> str:
        """
        检测服务供应商

        Args:
            api_url: API地址
            config: 模型配置对象
            provider: 直接指定的供应商

        Returns:
            str: 供应商名称（小写）
        """
        # 优先使用直接指定的供应商
        if provider:
            return provider.lower()

        # 其次从配置对象中获取提供商信息
        if config and hasattr(config, 'provider') and config.provider:
            return config.provider.lower()

        # 从API URL推断（作为备选）
        if api_url:
            api_url_lower = api_url.lower()
            if 'anthropic' in api_url_lower:
                return 'anthropic'
            elif 'google' in api_url_lower or 'gemini' in api_url_lower:
                return 'google'
            elif 'ollama' in api_url_lower or ':11434' in api_url_lower:
                return 'ollama'
            elif 'gpustack' in api_url_lower:
                return 'gpustack'

        # 默认返回openai
        return 'openai'

    def _build_parameters_from_hierarchy(self, model_config=None, platform_params=None,
                                        role_params=None, runtime_params=None) -> Tuple[Dict, Dict]:
        """
        构建参数层级继承

        Args:
            model_config: ModelConfig对象，包含max_tokens等基础参数
            platform_params: Platform层参数，包含辅助提示词等平台功能配置
            role_params: Role层参数，包含temperature等扩展参数
            runtime_params: 运行时传入的参数，可覆盖上层参数

        Returns:
            Tuple[Dict, Dict]: (合并后的参数字典, 提示词信息)
        """
        final_params = {}
        prompt_info = {}

        # 1. 从ModelConfig继承基础参数
        if model_config:
            if hasattr(model_config, 'max_output_tokens') and model_config.max_output_tokens:
                final_params['max_tokens'] = model_config.max_output_tokens
            else:
                final_params['max_tokens'] = 2000  # 默认值

        # 2. 从Platform继承平台级参数 (与ModelConfig并行)
        if platform_params:
            prompt_info.update({
                'auxiliary_prompts': platform_params.get('auxiliary_prompts', []),
                'platform_specific_settings': platform_params.get('platform_specific_settings', {}),
                'platform_function_configs': platform_params.get('platform_function_configs', {})
            })

        # 3. 从Role继承扩展参数 (继承ModelConfig)
        if role_params:
            role_param_mapping = {
                'temperature': role_params.get('temperature'),
                'top_p': role_params.get('top_p'),
                'frequency_penalty': role_params.get('frequency_penalty'),
                'presence_penalty': role_params.get('presence_penalty'),
                'stop_sequences': role_params.get('stop_sequences'),
                'max_tokens': role_params.get('max_tokens')  # Role层可以覆盖ModelConfig的max_tokens
            }
            # 只添加非None的参数
            for key, value in role_param_mapping.items():
                if value is not None:
                    final_params[key] = value

            # 角色提示词
            if role_params.get('role_prompt'):
                prompt_info['role_prompt'] = role_params.get('role_prompt')

        # 4. 运行时参数覆盖
        if runtime_params:
            for key, value in runtime_params.items():
                if value is not None:
                    final_params[key] = value

        return final_params, prompt_info

    def _map_and_filter_parameters(self, provider: str, **kwargs) -> Dict:
        """
        根据供应商映射和过滤参数

        Args:
            provider: 供应商名称
            **kwargs: 平台标准参数

        Returns:
            Dict: 映射后的供应商参数
        """
        provider_mapping = PROVIDER_PARAMETER_MAPPING.get(provider, {})
        mapped_params = {}

        for platform_param, value in kwargs.items():
            if value is None:
                continue

            # 跳过特殊值
            if platform_param == 'max_tokens' and value == 0:
                continue

            # 检查是否有映射
            if platform_param in provider_mapping:
                provider_param = provider_mapping[platform_param]
                mapped_params[provider_param] = value
            # 如果没有映射但参数名相同，检查是否是标准参数
            elif platform_param in ['temperature', 'max_tokens', 'top_p', 'frequency_penalty', 'presence_penalty', 'stop_sequences']:
                # 对于标准参数，如果映射表中没有定义，说明该供应商不支持，跳过
                logger.debug(f"[ModelClient] 供应商 {provider} 不支持参数 {platform_param}，已跳过")
                continue
            else:
                # 其他参数直接传递
                mapped_params[platform_param] = value

        logger.debug(f"[ModelClient] 供应商 {provider} 参数映射结果: {mapped_params}")
        return mapped_params

    # === 供应商适配方法 ===

    def _handle_openai_request(self, **params) -> Dict:
        """处理OpenAI兼容请求"""
        # 当前的send_request方法已经处理OpenAI格式，这里作为扩展点
        return params

    def _handle_anthropic_request(self, **params) -> Dict:
        """处理Anthropic请求"""
        # 当前的send_request方法已经处理Anthropic格式，这里作为扩展点
        return params

    def _handle_google_request(self, **params) -> Dict:
        """处理Google AI请求 - 过滤不支持的参数"""
        # 当前的send_request方法已经处理Google格式，这里作为扩展点
        return params

    def _handle_ollama_request(self, **params) -> Dict:
        """处理Ollama请求"""
        # 当前的send_request方法已经处理Ollama格式，这里作为扩展点
        return params

    def _handle_gpustack_request(self, **params) -> Dict:
        """处理GPUStack请求"""
        # 当前的send_request方法已经处理GPUStack格式，这里作为扩展点
        return params
