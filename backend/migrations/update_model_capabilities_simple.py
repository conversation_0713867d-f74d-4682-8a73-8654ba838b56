#!/usr/bin/env python3
"""
简化的数据库迁移脚本：更新模型配置的能力标签和模态
"""

import os
import sys
import sqlite3
import json

def migrate_model_capabilities():
    """执行数据库迁移"""
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查modalities字段是否存在
        cursor.execute("PRAGMA table_info(model_configs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'modalities' not in columns:
            print("添加modalities字段...")
            cursor.execute("ALTER TABLE model_configs ADD COLUMN modalities JSON")
        
        # 获取所有模型配置
        cursor.execute("SELECT id, capabilities FROM model_configs")
        records = cursor.fetchall()
        
        print("开始更新模型配置...")
        
        for record_id, capabilities_json in records:
            old_capabilities = json.loads(capabilities_json) if capabilities_json else []
            
            # 设置默认模态（所有模型都有文本输入输出）
            new_modalities = ['text_input', 'text_output']
            
            # 根据旧能力推断模态
            if 'vision' in old_capabilities:
                new_modalities.append('image_input')
            if 'image' in old_capabilities:
                new_modalities.append('image_output')
            if 'audio' in old_capabilities or 'tts' in old_capabilities:
                new_modalities.extend(['audio_input', 'audio_output'])
            if 'embeddings' in old_capabilities:
                new_modalities.append('vector_output')
            
            # 去重并排序
            new_modalities = sorted(list(set(new_modalities)))
            
            # 设置新的能力（只保留专业能力）
            new_capabilities = []
            if 'function_calling' in old_capabilities:
                new_capabilities.append('function_calling')
            if 'reasoning' in old_capabilities:
                new_capabilities.append('reasoning')
            if 'code_execution' in old_capabilities:
                new_capabilities.append('code_execution')
            if 'tool_use' in old_capabilities:
                new_capabilities.append('tool_use')
            
            # 更新记录
            cursor.execute(
                "UPDATE model_configs SET modalities = ?, capabilities = ? WHERE id = ?",
                (json.dumps(new_modalities), json.dumps(new_capabilities), record_id)
            )
            
            print(f"更新模型 {record_id}: 模态={new_modalities}, 能力={new_capabilities}")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"成功更新 {len(records)} 个模型配置")
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == '__main__':
    print("开始数据库迁移：更新模型配置的能力标签和模态")
    success = migrate_model_capabilities()
    
    if success:
        print("迁移完成！")
        sys.exit(0)
    else:
        print("迁移失败！")
        sys.exit(1)
