import os
from flask import Flask
from flask_cors import CORS
from config import Config
from app.extensions import db
import logging
from sqlalchemy import inspect, text

def configure_logging(app):
    """配置应用的日志系统"""
    # 设置日志级别
    log_level_str = app.config.get('LOG_LEVEL', 'INFO')

    # 将字符串日志级别转换为logging模块的常量
    log_level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    log_level = log_level_map.get(log_level_str, logging.INFO)

    app.logger.info(f"设置日志级别为: {log_level_str}")

    # 配置基本日志
    logging.basicConfig(
        level=log_level,
        format='[%(levelname)s] %(asctime)s - %(name)s -  %(message)s'
    )

    # 获取应用的日志处理器
    app_logger = logging.getLogger(app.name)
    app_logger.setLevel(log_level)

    # 设置其他模块的日志级别
    logging.getLogger('app').setLevel(log_level)
    logging.getLogger('app.services').setLevel(log_level)
    logging.getLogger('app.api').setLevel(log_level)

    # 如果是DEBUG级别，为关键模块设置更详细的日志
    if log_level == logging.DEBUG:
        logging.getLogger('app.services.conversation').setLevel(logging.DEBUG)
        logging.getLogger('app.services.mcp_server_manager').setLevel(logging.DEBUG)
        logging.getLogger('app.services.tool_schema_cache').setLevel(logging.DEBUG)

    # 添加文件处理器，无论是否在生产环境中
    if not os.path.exists('logs'):
        os.mkdir('logs')
    file_handler = logging.FileHandler('logs/app.log')
    file_handler.setFormatter(logging.Formatter(
        '[%(levelname)s] %(asctime)s - %(name)s - %(message)s'
    ))
    file_handler.setLevel(log_level)
    app_logger.addHandler(file_handler)

def create_app(config_class=Config):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    if config_class is None:
        # 根据环境变量选择配置，默认为开发环境
        env = os.environ.get('FLASK_ENV', 'development')
        config_class = config.get(env, config['default'])

    app.config.from_object(config_class)

    # 确保instance目录存在
    os.makedirs(os.path.join(app.instance_path), exist_ok=True)

    # 强制设置数据库URI为当前目录下的app.db
    app_dir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
    db_path = os.path.join(app_dir, 'app.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.logger.info(f"使用数据库: {app.config['SQLALCHEMY_DATABASE_URI']}")

    # 检查数据库文件状态
    if os.path.exists(db_path):
        db_size = os.path.getsize(db_path)
        app.logger.info(f"数据库文件已存在，大小: {db_size} 字节")
    else:
        app.logger.info(f"数据库文件不存在，将在首次访问时创建")

    # 配置跨域资源共享，允许所有路径的请求
    CORS(app,
         resources={r"/*": {
             "origins": "*",
             "allow_headers": ["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin"],
             "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
             "supports_credentials": False
         }})

    # 初始化数据库
    db.init_app(app)

    # 配置日志
    configure_logging(app)

    # 注册按功能域分离的API路由
    # 不再使用旧的Blueprint和控制器路由
    from app.api.routes import register_api_blueprints
    register_api_blueprints(app)

    # 注册许可证中间件
    from app.middleware.license_middleware import register_license_middleware
    register_license_middleware(app)

    # 注册MCP服务器路由
    from app.services.mcp_server_manager import mcp_manager
    mcp_manager.register_routes(app)

    # 初始化外部变量监控器
    from app.services.external_variable_monitor import init_external_variable_monitor
    init_external_variable_monitor(app)

    # 创建数据库表（如果不存在）
    with app.app_context():
        try:
            # 确保导入所有模型类
            # 注意: 导入模型类时需要确保所有模型都已定义
            from app.models import Agent, Role, SystemSetting
            from app.models import ActionSpace, ActionSpaceEnvironmentVariable, RoleVariable, ExternalEnvironmentVariable

            # 尝试创建所有表
            db.create_all()

            # 验证表是否创建成功
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            app.logger.info(f"数据库表创建成功: {tables}")

            # 如果表创建失败（表为空），使用直接的SQL语句创建表
            if not tables:
                app.logger.warning("使用SQL直接创建表")
                with db.engine.connect() as conn:
                    # 创建用户表
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY,
                        username VARCHAR(64) NOT NULL UNIQUE,
                        password_hash VARCHAR(128),
                        email VARCHAR(120) UNIQUE,
                        is_active BOOLEAN DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                    """))

                    # 创建智能体表
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS agents (
                        id INTEGER PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        description TEXT,
                        avatar VARCHAR(255),
                        settings JSON,
                        status VARCHAR(20) DEFAULT 'active',
                        role_id INTEGER NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (role_id) REFERENCES roles (id)
                    )
                    """))

                    # 创建行动空间表
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS action_spaces (
                        id INTEGER PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        description TEXT,
                        settings JSON,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                    """))

                    # 创建规则集表
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS rule_sets (
                        id INTEGER PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        description TEXT,
                        rules JSON,
                        conditions JSON,
                        actions JSON,
                        settings JSON,
                        action_space_id INTEGER NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (action_space_id) REFERENCES action_spaces (id)
                    )
                    """))

                    # 创建行动任务表
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS action_tasks (
                        id INTEGER PRIMARY KEY,
                        title VARCHAR(100) NOT NULL,
                        description TEXT,
                        status VARCHAR(20) DEFAULT 'active',
                        mode VARCHAR(20) DEFAULT 'sequential',
                        rule_set_id INTEGER,
                        user_id INTEGER,
                        action_space_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (rule_set_id) REFERENCES rule_sets (id),
                        FOREIGN KEY (user_id) REFERENCES users (id),
                        FOREIGN KEY (action_space_id) REFERENCES action_spaces (id)
                    )
                    """))

                    # 创建行动任务-智能体关联表
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS action_task_agents (
                        id INTEGER PRIMARY KEY,
                        action_task_id INTEGER NOT NULL,
                        agent_id INTEGER NOT NULL,
                        is_default BOOLEAN DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (action_task_id) REFERENCES action_tasks (id),
                        FOREIGN KEY (agent_id) REFERENCES agents (id)
                    )
                    """))

                    # 创建环境变量表
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS environment_variables (
                        id INTEGER PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        value TEXT,
                        type VARCHAR(20) DEFAULT 'text',
                        history JSON,
                        action_task_id INTEGER NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (action_task_id) REFERENCES action_tasks (id)
                    )
                    """))

                    # 创建消息表
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS messages (
                        id INTEGER PRIMARY KEY,
                        content TEXT NOT NULL,
                        thinking TEXT,
                        raw_message TEXT,
                        role VARCHAR(20) NOT NULL,
                        action_task_id INTEGER NOT NULL,
                        agent_id INTEGER,
                        user_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (action_task_id) REFERENCES action_tasks (id),
                        FOREIGN KEY (agent_id) REFERENCES agents (id),
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                    """))

                    # 创建代理变量表
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS agent_variables (
                        id INTEGER PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        value TEXT,
                        type VARCHAR(20) DEFAULT 'text',
                        history JSON,
                        is_public BOOLEAN DEFAULT 1,
                        agent_id INTEGER NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (agent_id) REFERENCES agents (id)
                    )
                    """))

                    # 创建模型配置表
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS model_configs (
                        id INTEGER PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        provider VARCHAR(50) NOT NULL,
                        model_id VARCHAR(100) NOT NULL,
                        base_url VARCHAR(255),
                        api_key VARCHAR(255),
                        context_window INTEGER DEFAULT 65536,
                        max_output_tokens INTEGER DEFAULT 2000,
                        request_timeout INTEGER DEFAULT 60,
                        is_default BOOLEAN DEFAULT 0,
                        is_default_text BOOLEAN DEFAULT 0,
                        is_default_embedding BOOLEAN DEFAULT 0,
                        modalities JSON,
                        capabilities JSON,
                        additional_params JSON,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                    """))

                    # 检查并添加新字段（用于现有数据库的升级）
                    try:
                        # 检查是否存在新字段
                        result = conn.execute(text("PRAGMA table_info(model_configs)")).fetchall()
                        columns = [row[1] for row in result]

                        if 'is_default_text' not in columns:
                            conn.execute(text("ALTER TABLE model_configs ADD COLUMN is_default_text BOOLEAN DEFAULT 0"))
                            logger.info("添加 is_default_text 字段到 model_configs 表")

                        if 'is_default_embedding' not in columns:
                            conn.execute(text("ALTER TABLE model_configs ADD COLUMN is_default_embedding BOOLEAN DEFAULT 0"))
                            logger.info("添加 is_default_embedding 字段到 model_configs 表")

                        conn.commit()
                    except Exception as e:
                        logger.warning(f"升级模型配置表时出现警告: {e}")
                        conn.rollback()

            # 初始化默认数据，但在测试环境中跳过
            if not app.config.get('TESTING', False):
                try:
                    # 检查数据库文件是否已存在
                    app_dir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
                    db_path = os.path.join(app_dir, 'app.db')

                    # 检查并记录数据库文件状态
                    if os.path.exists(db_path):
                        db_size = os.path.getsize(db_path)
                        app.logger.info(f"数据库文件 {db_path} 状态检查 - 存在: 是, 大小: {db_size} 字节")

                        # 检查是否需要初始化数据
                        # 执行一个简单查询来检查数据库是否有数据
                        agent_count = Agent.query.count()
                        role_count = Role.query.count()
                        settings_count = SystemSetting.query.count()

                        app.logger.info(f"数据检查 - 智能体数量: {agent_count}, 角色数量: {role_count}, 系统设置数量: {settings_count}")

                        if agent_count == 0 and role_count == 0:
                            app.logger.info("数据库表为空，需要初始化种子数据")
                            # 设置更详细的日志级别，用于记录种子数据初始化过程
                            logging.getLogger('app.seed_data').setLevel(logging.DEBUG)

                            from app.seed_data import seed_data
                            seed_data()
                            app.logger.info("种子数据初始化完成")
                        else:
                            app.logger.info("数据库已有数据，跳过种子数据初始化")
                    else:
                        app.logger.info(f"数据库文件 {db_path} 不存在，初始化种子数据")
                        # 设置更详细的日志级别，用于记录种子数据初始化过程
                        logging.getLogger('app.seed_data').setLevel(logging.DEBUG)

                        from app.seed_data import seed_data
                        seed_data()
                        app.logger.info("种子数据初始化完成")

                    # 从数据库加载系统设置到app.config
                    app.logger.info("从数据库加载系统设置到app.config...")
                    try:
                        from app.models import SystemSetting
                        settings = SystemSetting.query.all()

                        for setting in settings:
                            config_key = setting.key.upper()

                            # 根据值类型进行转换
                            if setting.value_type == 'boolean':
                                config_value = setting.value.lower() in ('true', '1', 'yes')
                            elif setting.value_type == 'number':
                                try:
                                    if '.' in setting.value:
                                        config_value = float(setting.value)
                                    else:
                                        config_value = int(setting.value)
                                except (ValueError, TypeError):
                                    config_value = 0
                            else:
                                config_value = setting.value

                            # 更新app.config
                            app.config[config_key] = config_value
                            app.logger.debug(f"已加载系统设置: {setting.key} = {config_value}")

                        app.logger.info(f"成功从数据库加载了 {len(settings)} 个系统设置到app.config")
                    except Exception as e:
                        app.logger.error(f"加载系统设置到app.config时出错: {e}")
                        app.logger.exception(e)

                except Exception as e:
                    app.logger.error(f"初始化种子数据时出错: {e}")
                    app.logger.exception(e)  # 打印详细的异常堆栈信息


        except Exception as e:
            app.logger.error(f"初始化数据库时出错: {e}")
            app.logger.exception(e)  # 打印详细的异常堆栈信息

    @app.route('/health')
    def health_check():
        return {'status': 'ok'}

    return app