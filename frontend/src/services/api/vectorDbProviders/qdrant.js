import api from '../axios';

/**
 * Qdrant向量数据库API服务
 * 提供与Qdrant向量数据库相关的API函数
 */
const qdrantAPI = {
  /**
   * 测试Qdrant连接
   * @param {Object} config Qdrant配置
   * @returns {Promise<Object>} 连接测试结果
   */
  testConnection: async (config) => {
    try {
      const response = await api.post('/vector-db/test-connection', {
        provider: 'qdrant',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('测试Qdrant连接失败:', error);
      throw error;
    }
  },

  /**
   * 验证Qdrant配置
   * @param {Object} config Qdrant配置
   * @returns {Promise<Object>} 验证结果
   */
  validateConfig: async (config) => {
    try {
      const response = await api.post('/vector-db/validate-config', {
        provider: 'qdrant',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('验证Qdrant配置失败:', error);
      throw error;
    }
  },

  /**
   * 获取Qdrant集合列表
   * @param {Object} config Qdrant配置
   * @returns {Promise<Object>} 集合列表
   */
  listCollections: async (config) => {
    try {
      const response = await api.post('/vector-db/list-collections', {
        provider: 'qdrant',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('获取Qdrant集合列表失败:', error);
      throw error;
    }
  },

  /**
   * 创建Qdrant集合
   * @param {Object} config Qdrant配置
   * @param {string} collectionName 集合名称
   * @param {Object} collectionConfig 集合配置
   * @returns {Promise<Object>} 创建结果
   */
  createCollection: async (config, collectionName, collectionConfig) => {
    try {
      const response = await api.post('/vector-db/create-collection', {
        provider: 'qdrant',
        config: config,
        collection_name: collectionName,
        collection_config: collectionConfig
      });
      return response.data;
    } catch (error) {
      console.error('创建Qdrant集合失败:', error);
      throw error;
    }
  },

  /**
   * 向量搜索
   * @param {Object} config Qdrant配置
   * @param {string} collectionName 集合名称
   * @param {Object} searchParams 搜索参数
   * @returns {Promise<Object>} 搜索结果
   */
  vectorSearch: async (config, collectionName, searchParams) => {
    try {
      const response = await api.post('/vector-db/vector-search', {
        provider: 'qdrant',
        config: config,
        collection_name: collectionName,
        search_params: searchParams
      });
      return response.data;
    } catch (error) {
      console.error('Qdrant向量搜索失败:', error);
      throw error;
    }
  }
};

export default qdrantAPI;
