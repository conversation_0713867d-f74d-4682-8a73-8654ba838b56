# 变量停止会话功能使用说明

## 功能概述

变量停止会话（Variable Stop Conversation）是一种新的智能体自主行动模式，允许智能体持续进行轮次行动，直到满足预设的变量停止条件为止。

## 核心特性

### ✅ 已实现功能

- **变量监控**：支持环境变量和智能体变量
- **条件检查**：支持多种运算符（>, >=, =, <=, <）
- **逻辑组合**：支持 AND/OR 逻辑组合多个条件
- **时间限制**：可设置最大运行时间防止无限运行
- **流式处理**：实时显示智能体行动进度
- **任务管理**：支持手动启动和停止

### 🎯 使用场景

- 智能体需要持续工作直到达成特定目标
- 基于数据变化触发的自动化流程
- 需要灵活停止条件的长期任务
- 多智能体协作完成复杂任务

## 使用方法

### 1. 启动变量停止任务

1. 在行动任务页面点击**"自主行动"**按钮
2. 在弹出的模态框中选择**"变量停止"**模式
3. 配置任务参数：
   - **主题**：描述任务目标
   - **停止条件**：设置一个或多个停止条件
   - **条件逻辑**：选择 AND 或 OR 逻辑
   - **最大运行时间**：防止任务无限运行（可选）

### 2. 配置停止条件

每个停止条件包含以下要素：

- **变量类型**：
  - `环境变量`：行动任务级别的共享变量
  - `智能体变量`：特定智能体的私有变量

- **变量名**：
  - 环境变量：直接选择变量名
  - 智能体变量：格式为 `智能体ID.变量名`

- **运算符**：
  - `>` 大于
  - `>=` 大于等于
  - `=` 等于
  - `<=` 小于等于
  - `<` 小于

- **阈值**：比较的目标值

### 3. 示例配置

#### 示例1：进度监控
```
任务主题：智能体持续工作直到完成100%进度
停止条件：
  - 类型：环境变量
  - 变量：progress
  - 运算符：>=
  - 阈值：100
条件逻辑：AND
最大运行时间：60分钟
```

#### 示例2：多条件组合
```
任务主题：智能体工作直到进度完成或状态变为已完成
停止条件：
  - 条件1：环境变量 progress >= 100
  - 条件2：环境变量 status = completed
条件逻辑：OR
最大运行时间：120分钟
```

## 工作流程

### 执行流程

1. **任务启动**：
   - 创建自主任务记录
   - 注册到任务跟踪器
   - 发送系统消息

2. **循环执行**：
   - 智能体按顺序轮流行动
   - 每轮结束后检查停止条件
   - 满足条件时自动停止

3. **任务结束**：
   - 发送完成消息
   - 清理任务状态
   - 更新数据库记录

### 停止机制

任务会在以下情况下停止：
- ✅ 停止条件满足
- ⏰ 达到最大运行时间
- 🛑 用户手动停止
- ❌ 发生错误异常

## 技术实现

### 后端架构

- **核心模块**：`app/services/conversation/variable_stop_conversation.py`
- **API集成**：`app/api/routes/conversations.py`
- **数据模型**：使用现有的 `AutonomousTask` 模型
- **任务类型**：`conditional_stop`

### 前端支持

- **配置界面**：`AutonomousTaskModal` 组件
- **流式处理**：完整的 SSE 支持
- **状态显示**：实时进度和智能体状态

### 数据流

```
前端配置 → API路由 → 变量停止服务 → 智能体行动 → 条件检查 → 任务完成
```

## 注意事项

### ⚠️ 重要提醒

1. **变量准备**：确保相关环境变量或智能体变量已创建
2. **条件设置**：合理设置停止条件，避免永不满足的条件
3. **时间限制**：建议设置最大运行时间作为安全保障
4. **智能体配置**：确保智能体有能力更新相关变量

### 🔧 故障排除

- **任务不停止**：检查停止条件是否合理，变量是否正确更新
- **变量未找到**：确认变量名称正确，变量已创建
- **权限问题**：确保智能体有权限访问和修改相关变量

## 最佳实践

### 📋 推荐做法

1. **明确目标**：在任务主题中清楚描述期望的结果
2. **渐进式条件**：设置可以逐步达成的条件
3. **备用方案**：使用 OR 逻辑提供多种停止路径
4. **监控机制**：定期检查任务进度和变量状态
5. **测试验证**：在正式使用前进行小规模测试

### 🎯 优化建议

- 合理设置检查间隔，避免过于频繁的条件检查
- 使用有意义的变量名和描述
- 为复杂任务设置中间检查点
- 保持停止条件的简单和明确

## 版本信息

- **实现日期**：2025-06-05
- **版本**：v1.0
- **状态**：✅ 已完成并测试通过

---

*变量停止会话功能已完全实现并准备就绪，可以立即投入使用！*
