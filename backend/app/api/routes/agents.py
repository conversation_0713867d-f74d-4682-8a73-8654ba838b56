"""
智能体API路由

处理与智能体相关的所有API请求
"""
from flask import Blueprint, request, jsonify
from app.models import Agent, db
from app.services.agent_service import AgentService
from sqlalchemy.orm import joinedload

# 创建Blueprint
agent_bp = Blueprint('agent_api', __name__)

# 创建服务实例
agent_service = AgentService()

@agent_bp.route('/agents', methods=['GET'])
def get_agents():
    """获取所有智能体列表"""
    status = request.args.get('status')
    simplified = request.args.get('simplified', 'true').lower() == 'true'
    
    # 从数据库获取智能体
    try:
        # 使用SQLAlchemy的joinedload预加载关系
        query = Agent.query.options(
            joinedload(Agent.role),
            joinedload(Agent.action_task)
        )
        
        # 如果指定了status参数，按状态筛选
        if status:
            query = query.filter(Agent.status == status.lower())
            
        agents = query.all()
        
        # 根据simplified参数决定返回简化版还是完整版
        if simplified:
            result = [agent_service.format_agent_for_list(agent) for agent in agents]
        else:
            result = [agent_service.format_agent_for_api(agent) for agent in agents]
        
        return jsonify(result)
    except Exception as e:
        print(f"Error in get_agents: {e}")
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/agents/<int:agent_id>', methods=['GET'])
def get_agent(agent_id):
    """获取特定智能体详情"""
    agent = agent_service.get_agent_by_id(agent_id)
    if agent:
        return jsonify(agent)
    return jsonify({'error': 'Agent not found'}), 404

@agent_bp.route('/agents', methods=['POST'])
def create_agent():
    """创建新智能体"""
    data = request.get_json()
    agent = agent_service.create_agent(data)
    return jsonify(agent), 201

@agent_bp.route('/agents/<int:agent_id>', methods=['PUT'])
def update_agent(agent_id):
    """更新智能体信息"""
    data = request.get_json()
    agent = agent_service.update_agent(agent_id, data)
    if agent:
        return jsonify(agent)
    return jsonify({'error': 'Agent not found'}), 404

@agent_bp.route('/agents/<int:agent_id>', methods=['DELETE'])
def delete_agent(agent_id):
    """删除智能体"""
    success = agent_service.delete_agent(agent_id)
    if success:
        return jsonify({'success': True})
    return jsonify({'error': 'Agent not found or cannot be deleted'}), 404

@agent_bp.route('/agents/model-configs', methods=['GET'])
def get_agent_model_configs():
    """获取智能体可用的模型配置"""
    configs = agent_service.get_all_model_configs()
    return jsonify({'model_configs': configs}) 