#!/usr/bin/env python3
"""
数据库迁移脚本：添加默认模型字段
为 model_configs 表添加 is_default_text 和 is_default_embedding 字段
"""

import os
import sys
import logging
from sqlalchemy import text

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models import db

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """执行数据库迁移"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查当前表结构
            logger.info("检查当前 model_configs 表结构...")
            with db.engine.connect() as conn:
                result = conn.execute(text("PRAGMA table_info(model_configs)")).fetchall()

                print("当前 model_configs 表结构:")
                columns = []
                for row in result:
                    columns.append(row[1])
                    print(f"  {row[1]} {row[2]} (nullable: {not row[3]}, default: {row[4]})")

                # 检查并添加新字段
                migrations_needed = []

                if 'is_default_text' not in columns:
                    migrations_needed.append('is_default_text')
                else:
                    logger.info("is_default_text 字段已存在")

                if 'is_default_embedding' not in columns:
                    migrations_needed.append('is_default_embedding')
                else:
                    logger.info("is_default_embedding 字段已存在")

                if not migrations_needed:
                    logger.info("所有字段都已存在，无需迁移")
                    return

                # 执行迁移
                logger.info(f"需要添加字段: {migrations_needed}")

                for field in migrations_needed:
                    logger.info(f"添加字段 {field}...")
                    conn.execute(text(f"ALTER TABLE model_configs ADD COLUMN {field} BOOLEAN DEFAULT 0"))
                    logger.info(f"成功添加字段 {field}")

                # 提交更改
                conn.commit()
                logger.info("数据库迁移完成！")

                # 验证迁移结果
                logger.info("验证迁移结果...")
                result = conn.execute(text("PRAGMA table_info(model_configs)")).fetchall()

                print("\n迁移后的 model_configs 表结构:")
                for row in result:
                    print(f"  {row[1]} {row[2]} (nullable: {not row[3]}, default: {row[4]})")

                # 检查现有数据
                count_result = conn.execute(text("SELECT COUNT(*) FROM model_configs")).fetchone()
                logger.info(f"表中现有 {count_result[0]} 条记录")

                if count_result[0] > 0:
                    logger.info("检查现有记录的新字段值...")
                    sample_result = conn.execute(text(
                        "SELECT name, is_default, is_default_text, is_default_embedding FROM model_configs LIMIT 5"
                    )).fetchall()

                    for row in sample_result:
                        print(f"  {row[0]}: is_default={row[1]}, is_default_text={row[2]}, is_default_embedding={row[3]}")

        except Exception as e:
            logger.error(f"迁移失败: {e}")
            db.session.rollback()
            raise

if __name__ == "__main__":
    migrate_database()
