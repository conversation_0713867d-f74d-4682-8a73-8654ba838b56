#!/usr/bin/env python3
"""
数据库迁移脚本：更新模型配置的能力标签和模态
将旧的模态相关能力转换为新的能力标签，并设置正确的模态
"""

import os
import sys
import sqlite3
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def migrate_capabilities_and_modalities():
    """执行数据库迁移"""
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查modalities字段是否存在
        cursor.execute("PRAGMA table_info(model_configs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'modalities' not in columns:
            print("添加modalities字段...")
            cursor.execute("ALTER TABLE model_configs ADD COLUMN modalities JSON")
        
        # 获取所有模型配置
        cursor.execute("SELECT id, capabilities FROM model_configs")
        records = cursor.fetchall()
        
        # 旧能力到新能力和模态的映射
        capability_mapping = {
            'text': {'modalities': ['text_input', 'text_output'], 'capabilities': ['conversation']},
            'vision': {'modalities': ['image_input'], 'capabilities': ['reasoning']},
            'audio': {'modalities': ['audio_input', 'audio_output'], 'capabilities': ['conversation']},
            'tts': {'modalities': ['audio_output'], 'capabilities': ['conversation']},
            'embeddings': {'modalities': ['text_input'], 'capabilities': ['embeddings']},
            'image': {'modalities': ['image_output'], 'capabilities': ['creative_writing']},
            'function_calling': {'modalities': [], 'capabilities': ['function_calling']},
        }
        
        print("开始更新模型配置...")
        
        for record_id, capabilities_json in records:
            old_capabilities = json.loads(capabilities_json) if capabilities_json else []
            
            # 收集新的模态和能力
            new_modalities = set(['text_input', 'text_output'])  # 默认都有文本模态
            new_capabilities = set(['conversation'])  # 默认都有对话能力
            
            # 根据旧能力映射到新的模态和能力
            for old_cap in old_capabilities:
                if old_cap in capability_mapping:
                    mapping = capability_mapping[old_cap]
                    new_modalities.update(mapping['modalities'])
                    new_capabilities.update(mapping['capabilities'])
                elif old_cap in ['reasoning', 'code_execution', 'creative_writing', 'data_analysis', 'tool_use', 'search', 'translation']:
                    # 已经是新的能力标签，直接保留
                    new_capabilities.add(old_cap)
            
            # 转换为列表并排序
            final_modalities = sorted(list(new_modalities))
            final_capabilities = sorted(list(new_capabilities))
            
            # 更新记录
            cursor.execute(
                "UPDATE model_configs SET modalities = ?, capabilities = ? WHERE id = ?",
                (json.dumps(final_modalities), json.dumps(final_capabilities), record_id)
            )
            
            print(f"更新模型 {record_id}: 模态={final_modalities}, 能力={final_capabilities}")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"成功更新 {len(records)} 个模型配置")
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == '__main__':
    print("开始数据库迁移：更新模型配置的能力标签和模态")
    success = migrate_capabilities_and_modalities()
    
    if success:
        print("迁移完成！")
        sys.exit(0)
    else:
        print("迁移失败！")
        sys.exit(1)
