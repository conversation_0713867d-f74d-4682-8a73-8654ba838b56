#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多智能体行动任务系统启动脚本
"""

# 设置环境变量，确保和WSGI使用UTF-8编码
import os
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['LC_ALL'] = 'en_US.UTF-8'
os.environ['LANG'] = 'en_US.UTF-8'

import sys
import logging
from app import create_app
from app.controllers.routes import register_routes

# 初始化应用
app = create_app()

# 运行应用
if __name__ == '__main__':
    
    # 在应用启动前打印所有路由
    with app.app_context():
        print("\n==== 已注册的所有路由 ====")
        for rule in app.url_map.iter_rules():
            print(f"{rule.endpoint}: {', '.join(rule.methods)} {rule.rule}")
        print("==========================\n")
    
    host = '0.0.0.0'
    port = 8080
    
    print(f"启动服务器于 http://{host}:{port}")
    
    # 使用普通的Flask运行应用，而不是Socket.IO
    app.run(host=host, port=port, debug=True) 
