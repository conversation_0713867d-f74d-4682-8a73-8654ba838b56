# 行动空间管理系统 - 产品需求文档

## 1. 概述

### 1.1 产品背景

行动空间管理系统是基于智能体(ABM)建模平台的核心模块，提供行动空间的创建、配置与管理功能。行动空间作为平台的底层逻辑框架，包含背景设定、监督者角色、规则集和环境变量，为智能体交互提供基础环境和约束条件。

### 1.2 产品目标

- 提供直观高效的行动空间创建与管理界面
- 实现自然语言规则与逻辑规则双引擎结合的规则系统
- 支持行动空间监督者角色配置，实现规则执行监控
- 建立行动空间、规则集与会话之间的关联管理机制
- 实现每个行动空间独立的环境变量实例化

### 1.3 用户群体

- 平台管理员：创建和管理基础行动空间模板
- 研究人员：定制专业领域的模拟行动空间
- 教育工作者：为教学目的建立简化行动空间模型
- 决策分析师：构建决策场景模拟环境

## 2. 功能需求

### 2.1 行动空间概览

#### 2.1.1 行动空间列表展示
- 以卡片/表格形式展示所有行动空间
- 显示行动空间名称、描述、创建时间、关联会话数
- 提供排序、筛选和搜索功能

#### 2.1.2 行动空间创建
- 支持从零创建新行动空间
- 提供基础模板选择
- 复制现有行动空间创建新行动空间

#### 2.1.3 行动空间编辑
- 编辑行动空间基本信息
- 修改背景设定和环境参数
- 更新关联的规则集

#### 2.1.4 行动空间删除
- 支持删除不再使用的行动空间
- 提供关联资源处理选项
- 删除确认机制

### 2.2 监督者配置

#### 2.2.1 监督者角色定义
- 设置监督者名称和职责
- 配置监督者的权限范围
- 定义监督者的干预级别

#### 2.2.2 监控机制配置
- 设置需要监控的行为类型
- 配置异常行为的处理策略
- 定义观察报告的生成规则

#### 2.2.3 干预策略设置
- 配置允许自动干预的场景
- 设置干预的触发条件
- 定义干预后的处理流程

### 2.3 规则管理中心

#### 2.3.1 规则集管理
- 创建、编辑、删除规则集
- 将规则组织为逻辑集合
- 为规则集添加标签和分类

#### 2.3.2 自然语言规则编辑
- 自然语言规则输入界面
- 规则意图分析和澄清
- 规则效果预览

#### 2.3.3 逻辑规则构建
- 可视化规则编排工具
- 代码式规则编辑器
- 规则测试和调试

#### 2.3.4 规则联动配置
- 设置规则触发顺序
- 配置规则间依赖关系
- 管理规则冲突解决策略

### 2.4 环境变量管理

> **重要概念**：环境变量管理仅做基础定义和模板设计，实际变量在与具体行动空间绑定时才会实例化。每个行动空间拥有独立的环境变量实例，同一变量类型在不同行动空间中可具有不同的值和行为特性。

#### 2.4.1 环境变量模板
- 系统预定义的变量类型和结构
- 用户自定义环境变量模板
- 模板导入导出功能

#### 2.4.2 环境变量实例化
- 每个行动空间独立的环境变量实例
- 从模板批量创建环境变量
- 行动空间特有环境变量配置

#### 2.4.3 环境参数配置
- 定义行动空间中的基础参数
- 设置参数的初始值和变化范围
- 配置参数间的相互影响

#### 2.4.4 环境变量分类
- 公共环境变量（所有智能体共享的行动空间级变量）
- 角色变量（特定角色或智能体的私有属性和状态）
- 动态环境变量（随时间或事件变化的变量）

#### 2.4.5 资源管理
- 定义行动空间中的资源类型
- 配置资源的分配机制
- 设置资源限制和消耗规则

#### 2.4.6 时间流逝设置
- 配置行动空间内时间流逝规则
- 设置时间对环境的影响
- 定义时间触发的事件

### 2.5 行动空间-规则关联

#### 2.5.1 规则集分配
- 将规则集关联到行动空间
- 设置规则集的适用场景
- 管理规则集优先级

#### 2.5.2 推荐规则配置
- 基于会话类型推荐规则集
- 自定义推荐逻辑
- 规则集兼容性检查

#### 2.5.3 关联测试
- 测试行动空间与规则集的协同效果
- 识别潜在冲突和问题
- 生成兼容性报告

### 2.6 规则执行监控

#### 2.6.1 监控仪表盘
- 实时显示规则执行状态
- 展示双引擎使用情况
- 可视化规则触发频率

#### 2.6.2 执行日志
- 记录规则触发和执行详情
- 标记规则类型和来源
- 支持日志查询和分析

#### 2.6.3 异常处理
- 识别规则执行异常
- 提供处理建议
- 记录处理过程

### 2.7 环境变量版本控制

#### 2.7.1 变量历史追踪
- 环境变量配置的版本历史
- 重要节点自动保存
- 回滚到任意历史状态

#### 2.7.2 实验分支
- 从当前状态创建实验分支
- 测试不同环境参数组合
- 合并成功的实验结果

### 2.8 跨行动空间设置

#### 2.8.1 变量继承机制
- 父行动空间到子行动空间的变量继承
- 子行动空间可覆盖特定变量
- 全局变化传播设置

#### 2.8.2 多行动空间协同
- 相同初始条件下变量演化比较
- 配置允许跨行动空间的变量影响
- 多行动空间融合实验

## 3. 双引擎规则系统

### 3.1 规则引擎中心

#### 3.1.1 规则类型切换
- 自然语言规则（语义规则）编辑区
- 逻辑规则（程序规则）编辑区
- 混合规则视图（两种规则协同工作）

#### 3.1.2 规则优先级设置
- 配置规则冲突时的优先权
- 设置规则执行顺序
- 管理规则依赖关系

### 3.2 自然语言规则工作台

#### 3.2.1 自然语言规则编辑器
- 使用自然语言描述规则
- 规则模板库和示例
- 语义解析和歧义检测

#### 3.2.2 规则意图分析
- 自动分析规则意图
- 生成预期结果
- 提供改进建议

### 3.3 逻辑规则构建器

#### 3.3.1 可视化规则编排
- 拖拽式逻辑规则构建
- 条件-动作流程图
- 规则测试和调试工具

#### 3.3.2 规则代码编辑器
- 支持多种编程语言
- 内置函数库和API文档
- 语法检查和性能分析

### 3.4 规则联动配置

#### 3.4.1 规则转换工具
- 自然语言规则转换为逻辑规则建议
- 逻辑规则的自然语言解释

#### 3.4.2 触发机制设置
- 配置使用自然语言规则的情况
- 设置切换到逻辑规则的条件
- 定义规则协作方式

### 3.5 规则测试实验室

#### 3.5.1 场景模拟测试
- 测试不同场景下的规则效果
- 比较不同规则组合
- A/B测试规则策略

#### 3.5.2 规则冲突解析
- 识别和解决规则冲突
- 规则一致性检查
- 冲突解决建议

## 4. 环境变量实例化机制

### 4.1 环境变量模板

#### 4.1.1 系统预设模板
- 常用环境变量类型
- 默认值和取值范围
- 使用建议和最佳实践

#### 4.1.2 自定义模板
- 创建新的环境变量类型
- 保存常用变量组合
- 分享模板到公共库

### 4.2 行动空间变量实例化

#### 4.2.1 从模板创建
- 选择要包含的变量类型
- 一键应用完整变量集
- 批量调整初始值

#### 4.2.2 自定义实例化
- 手动添加单个环境变量
- 设置行动空间特有的变量
- 配置变量间特殊关系

#### 4.2.3 复制现有行动空间
- 从其他行动空间复制环境设置
- 选择性导入变量子集
- 批量修改差异项

### 4.3 行动空间特性定义

#### 4.3.1 行动空间类型特化
- 不同类型行动空间的特定规则
- 特定元素变量
- 时代特定参数

#### 4.3.2 行动空间环境预设
- 环境主题包（森林、城市等）
- 时代环境包（古代、现代、未来）
- 预设包自定义和修改

### 4.4 环境变量与智能体交互

#### 4.4.1 环境感知配置
- 设置智能体能感知的环境变量
- 配置感知精度和范围
- 定义感知延迟和失真

#### 4.4.2 环境影响机制
- 环境变量对智能体能力的修正
- 资源获取效率的环境因素
- 行动成功率的环境调整

## 5. 界面设计

### 5.1 整体导航结构

#### 5.1.1 一级导航菜单
- **行动空间管理**（平台的核心功能模块）

#### 5.1.2 二级导航菜单
- **行动空间** - 行动空间列表与详细配置
- **行动规则** - 规则集管理与规则编辑
- **环境变量** - 环境变量模板与配置
- **行动监控** - 系统运行监控与分析

#### 5.1.3 通用布局结构
- **左侧**：导航菜单，显示一级与二级菜单
- **中央**：主要内容区域，显示当前选择的功能页面
- **右侧**：上下文信息面板，显示选中项的详情和快捷操作

### 5.2 行动空间模块

#### 5.2.1 概览 Tab
- **功能区**：搜索、筛选、创建、批量操作按钮
- **展示区**：卡片/表格切换视图
- **卡片内容**：
  * 行动空间名称和描述
  * 创建时间和关联会话数
  * 规则集数量
  * 状态标识（活跃/暂停）
  * 快捷操作按钮（编辑、删除、复制）

#### 5.2.2 详情 Tab
- **页面结构**：选择行动空间后激活，包含以下子Tab
- **基本信息** 子Tab
  * 名称、描述、背景设定
  * ODD框架配置
  * 状态设置
- **监督者** 子Tab
  * 监督者角色定义
  * 监控机制配置
  * 干预策略设置
- **环境实例** 子Tab
  * 从模板应用环境变量
  * 公共/角色/动态变量配置
  * 资源与时间参数设置
- **规则关联** 子Tab
  * 规则集关联管理
  * 规则优先级设置
  * 规则兼容性检查

#### 5.2.3 跨空间 Tab
- **空间继承关系图**：可视化展示行动空间的父子关系
- **变量继承配置**：设置从父空间继承的变量和可覆盖选项
- **多空间协同管理**：配置跨空间的变量影响和融合实验

### 5.3 行动规则模块

#### 5.3.1 规则集 Tab
- **功能区**：创建、导入导出、搜索、筛选
- **规则集列表**：规则集名称、描述、规则数量、使用情况
- **规则集详情**：选中后显示包含的规则和相关属性

#### 5.3.2 规则编辑 Tab
- **规则类型切换**：自然语言规则/逻辑规则/混合视图
- **自然语言规则编辑区**：
  * 自然语言输入界面
  * 规则模板库
  * 意图分析与建议
- **逻辑规则编辑区**：
  * 可视化规则构建工具
  * 代码编辑器
  * 内置函数库

#### 5.3.3 规则测试 Tab
- **测试场景配置**：设置测试环境和参数
- **测试执行控制**：运行、暂停、步进按钮
- **结果分析面板**：规则触发情况和执行效果
- **冲突检测工具**：识别规则间冲突并提供解决建议

### 5.4 环境变量模块

#### 5.4.1 模板管理 Tab
- **模板库**：系统预设和用户自定义模板
- **模板创建工具**：定义新的环境变量模板
- **分类与组织**：按领域和用途分类模板
- **导入导出功能**：共享和使用外部模板

#### 5.4.2 变量历史 Tab
- **版本时间线**：环境变量配置的历史版本
- **变更对比工具**：比较不同版本的变化
- **实验分支管理**：创建和合并实验分支
- **回滚功能**：恢复到历史版本状态

#### 5.4.3 资源配置 Tab
- **资源类型定义**：创建和管理资源类型
- **分配机制设置**：资源分配策略配置
- **限制规则定义**：设置资源限制和消耗规则
- **时间影响设置**：配置时间对资源的影响

### 5.5 行动监控模块

#### 5.5.1 仪表盘 Tab
- **统计概览**：关键指标卡片展示
- **实时监控图表**：
  * 行动空间活跃情况
  * 规则执行频率
  * 环境变量变化趋势
  * 系统资源使用状况
- **过滤器**：行动空间选择、时间范围设置

#### 5.5.2 执行日志 Tab
- **日志列表**：规则触发和执行记录
- **高级搜索**：按多条件筛选日志
- **异常标记**：突出显示异常执行情况
- **导出功能**：日志数据导出分析

#### 5.5.3 监控设置 Tab
- **监控规则配置**：设置系统级监控规则
- **告警阈值设置**：配置触发告警的条件
- **通知方式管理**：设置告警通知方式和接收人
- **自动化响应**：配置对特定事件的自动处理

## 6. 交互流程

### 6.1 创建行动空间流程

1. 用户点击"创建行动空间"按钮
2. 选择创建方式（从零创建/使用模板/复制现有）
3. 填写基本信息（名称、描述）
4. 配置背景设定和环境参数
5. 选择初始规则集（可选）
6. 配置监督者角色
7. 保存并创建行动空间

### 6.2 环境变量配置流程

1. 选择目标行动空间
2. 进入环境变量管理界面
3. 选择预设模板或手动配置
4. 调整变量初始值和范围
5. 设置变量间依赖关系
6. 定义变量演化规则
7. 保存环境配置

### 6.3 规则创建流程

1. 进入规则管理中心
2. 选择规则类型（大模型/逻辑）
3. 编写/构建规则内容
4. 设置规则属性和触发条件
5. 测试规则效果
6. 将规则添加到规则集
7. 保存规则集

### 6.4 行动空间-规则关联流程

1. 选择目标行动空间
2. 进入规则关联界面
3. 浏览可用规则集
4. 选择要关联的规则集
5. 设置规则集优先级
6. 测试关联效果
7. 确认并保存关联

## 7. 技术需求

### 7.1 数据架构

#### 7.1.1 行动空间模型扩展
```json
// 行动空间模型结构示例
{
  "id": 1,
  "name": "生态系统模拟",
  "description": "模拟森林生态系统的互动",
  "rules": "森林生态系统的基本规则...",
  "settings": {
    "background": "森林环境描述...",
    "observer": {
      "name": "生态学家",
      "permissions": ["monitor", "intervene", "report"],
      "intervention_threshold": 0.7
    },
    "environment": {
      "public": {
        "time_scale": 1.0,
        "rainfall": 800,
        "temperature": 22
      },
      "role_variables": {
        "predator": {
          "hunting_efficiency": 0.7,
          "energy_consumption": 5
        },
        "prey": {
          "reproduction_rate": 0.2,
          "hiding_ability": 0.6
        }
      },
      "regions": [
        {
          "id": "forest_core",
          "name": "森林核心",
          "biodiversity": 0.9,
          "humidity": 75
        }
      ],
      "dynamic": {
        "seasonal_changes": true,
        "resource_regeneration_rate": 0.05
      }
    }
  },
  "rule_sets": [
    {
      "id": "rs_001",
      "name": "生态平衡规则",
      "description": "维持生态系统平衡的规则集",
      "rules": [
        {
          "id": "r_001",
          "type": "llm",
          "content": "如果捕食者数量过多，资源将迅速减少"
        },
        {
          "id": "r_002",
          "type": "logic",
          "condition": "predator_count > prey_count * 0.5",
          "action": "reduce_predator_reproduction_rate(0.5)"
        }
      ]
    }
  ],
  "odd_framework": {
    "purpose": "研究森林生态系统的稳定性",
    "entities": ["trees", "herbivores", "predators"],
    "processes": ["growth", "predation", "reproduction"],
    "emergent_properties": ["ecosystem_stability", "biodiversity"]
  }
}
```

#### 7.1.2 存储方案
- 利用行动空间模型的settings字段(JSON类型)存储结构化环境数据
- 为高频访问的关键环境变量建立索引或缓存
- 实现环境变量历史记录和回滚机制

### 7.2 后端需求

- 双引擎规则执行系统的实现
- 环境变量计算和更新机制
- 监督者角色功能实现
- 规则执行日志记录系统
- 版本控制和分支管理

### 7.3 前端需求

- 响应式界面设计
- 环境变量可视化配置工具
- 规则可视化编辑器
- 实时监控数据展示
- 交互式规则测试工具

### 7.4 集成需求

- 与大模型API的集成
- 与会话管理系统的关联
- 与智能体系统的接口
- 导入/导出功能实现

## 8. 实现优先级

### 8.1 第一阶段（核心功能）
- 行动空间基础管理（创建、编辑、删除）
- 基本环境变量配置
- 简化版规则管理
- 基本行动空间-规则关联

### 8.2 第二阶段（增强功能）
- 环境变量模板和实例化
- 自然语言规则编辑器
- 逻辑规则构建器
- 监督者角色基础功能

### 8.3 第三阶段（高级功能）
- 双引擎规则协作机制
- 环境变量版本控制
- 规则执行监控系统
- 多行动空间协同功能

## 9. 成功指标

- 行动空间创建效率：完成一个基础行动空间配置的平均时间＜15分钟
- 环境变量配置：从模板创建完整环境的平均时间＜10分钟
- 规则编写便捷性：通过自然语言规则接口创建规则的成功率＞80%
- 系统稳定性：规则执行异常率＜5%
- 用户满意度：行动空间管理功能用户满意度评分＞4.5（满分5分）

