import React, { useState, useEffect } from 'react';
import { Tabs, Card, Typography, Spin, Tag } from 'antd';
import { DatabaseOutlined, FileTextOutlined, ApiOutlined, SettingOutlined, BarChartOutlined, LinkOutlined, TeamOutlined, ShareAltOutlined } from '@ant-design/icons';
import KnowledgeList from './KnowledgeList';
import DocumentManager from './DocumentManager';
import GraphEnhancement from './GraphEnhancement';

import KnowledgeSettings from './KnowledgeSettings';
import UsageAnalytics from './UsageAnalytics';
// 新增外部知识库相关组件
import ExternalProviders from './external/ExternalProviders';
import ExternalKnowledges from './external/ExternalKnowledges';
import RoleKnowledgeBinding from './external/RoleKnowledgeBinding';
import ExternalKnowledgeStats from './external/ExternalKnowledgeStats';

const { Title, Text } = Typography;

const KnowledgeBaseMain = () => {
  const [activeTab, setActiveTab] = useState('internal');
  const [internalActiveTab, setInternalActiveTab] = useState('list');
  const [externalActiveTab, setExternalActiveTab] = useState('providers');
  const [loading, setLoading] = useState(false);

  // 处理主标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 处理内部知识库子标签页切换
  const handleInternalTabChange = (key) => {
    setInternalActiveTab(key);
  };

  // 处理外部知识库子标签页切换
  const handleExternalTabChange = (key) => {
    setExternalActiveTab(key);
  };

  return (
    <div className="knowledge-base-container">
      <div style={{ marginBottom: '24px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 20
        }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>知识库管理</Title>
            <Text type="secondary">
              创建和管理角色可用的知识库，支持文本、向量和结构化数据的存储与检索
            </Text>
          </div>
        </div>
      </div>

      <Spin spinning={loading}>
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          items={[
            {
              key: 'internal',
              label: (
                <span>
                  <DatabaseOutlined />内部知识库
                </span>
              ),
              children: (
                <Card
                  variant="borderless"
                  style={{
                    borderRadius: '12px',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
                  }}
                >
                  <Tabs
                    activeKey={internalActiveTab}
                    onChange={handleInternalTabChange}
                    items={[
                      {
                        key: 'list',
                        label: <span><DatabaseOutlined />知识库列表</span>,
                        children: <KnowledgeList />
                      },
                      {
                        key: 'documents',
                        label: <span><FileTextOutlined />文档管理</span>,
                        children: <DocumentManager />
                      },
                      {
                        key: 'graph-enhancement',
                        label: <span><ShareAltOutlined />图谱增强</span>,
                        children: <GraphEnhancement />
                      },
                      {
                        key: 'settings',
                        label: <span><SettingOutlined />设置</span>,
                        children: <KnowledgeSettings />
                      }
                    ]}
                  />
                </Card>
              )
            },
            {
              key: 'external',
              label: <span><LinkOutlined />外部知识库</span>,
              children: (
                <Card
                  variant="borderless"
                  style={{
                    borderRadius: '12px',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
                  }}
                >
                  <Tabs
                    activeKey={externalActiveTab}
                    onChange={handleExternalTabChange}
                    items={[
                      {
                        key: 'providers',
                        label: <span><ApiOutlined />提供商管理</span>,
                        children: <ExternalProviders />
                      },
                      {
                        key: 'knowledges',
                        label: <span><DatabaseOutlined />知识库列表</span>,
                        children: <ExternalKnowledges />
                      }
                    ]}
                  />
                </Card>
              )
            },
            {
              key: 'usage-stats',
              label: <span><BarChartOutlined />使用统计</span>,
              children: (
                <Card
                  variant="borderless"
                  style={{
                    borderRadius: '12px',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
                  }}
                >
                  <Tabs
                    items={[
                      {
                        key: 'internal-stats',
                        label: <span><DatabaseOutlined />内部知识库统计</span>,
                        children: <UsageAnalytics />
                      },
                      {
                        key: 'external-stats',
                        label: <span><LinkOutlined />外部知识库统计</span>,
                        children: <ExternalKnowledgeStats />
                      }
                    ]}
                  />
                </Card>
              )
            },
            {
              key: 'role-bindings',
              label: <span><TeamOutlined />角色关联</span>,
              children: (
                <Card
                  variant="borderless"
                  style={{
                    borderRadius: '12px',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
                  }}
                >
                  <RoleKnowledgeBinding />
                </Card>
              )
            }
          ]}
        />
      </Spin>
    </div>
  );
};

export default KnowledgeBaseMain;
