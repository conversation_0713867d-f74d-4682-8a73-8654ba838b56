[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:backend]
command=python run_app.py
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/backend.err.log
stdout_logfile=/var/log/supervisor/backend.out.log
environment=PYTHONIOENCODING=utf-8,LC_ALL=C.UTF-8,LANG=C.UTF-8,FLASK_ENV=production,HOST=0.0.0.0,PORT=8080

[program:frontend]
command=pnpm run dev
directory=/app/frontend
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/frontend.err.log
stdout_logfile=/var/log/supervisor/frontend.out.log
environment=NODE_ENV=development,PORT=3000,REACT_APP_API_URL=http://localhost:8080/api,CHOKIDAR_USEPOLLING=true,WATCHPACK_POLLING=true
