import axios from 'axios';

/**
 * 创建并配置全局axios实例
 */
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080/api',  // 使用环境变量，保留默认值作为备选
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 60000, // 60秒超时，适应大模型处理的时间需求
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 从localStorage获取认证令牌
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 调试信息
    console.log(`API请求: ${config.method.toUpperCase()} ${config.url}`, config);

    return config;
  },
  error => {
    console.error('请求配置错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 调试信息
    console.log(`API响应: ${response.config.method.toUpperCase()} ${response.config.url}`, response.data);
    return response;
  },
  error => {
    // 处理常见错误
    if (error.response) {
      // 服务器响应错误
      console.error('API Error:', error.response.status, error.response.data);
      console.error(`请求失败: ${error.config?.method?.toUpperCase()} ${error.config?.url}`);

      // 处理身份验证失败
      if (error.response.status === 401) {
        // 可选：重定向到登录页面
        // window.location.href = '/login';
      } else if (error.response.status === 403) {
        // 检查是否是许可证过期错误
        if (error.response.data?.code === 'LICENSE_EXPIRED') {
          console.warn('许可证已过期或无效');

          // 如果不是许可证相关的API请求，重定向到授权页面
          if (!error.config.url.includes('/license')) {
            // 检查当前是否已经在授权页面
            const currentPath = window.location.pathname;
            if (currentPath !== '/settings/about') {
              // 使用对话框管理器显示提示对话框
              // 动态导入以避免循环依赖
              import('../licenseDialogManager').then(({ showLicenseExpiredDialog, isLicenseDialogShowing }) => {
                // 检查是否已经显示对话框
                if (!isLicenseDialogShowing()) {
                  showLicenseExpiredDialog(() => {
                    window.location.href = '/settings/about';
                  });
                }
              });
            }
          }

          // 创建一个特殊的license错误对象，阻止错误继续传播到上层组件
          const licenseError = new Error('许可证无效或已过期，请激活系统');
          licenseError.isLicenseError = true;
          licenseError.code = 'LICENSE_EXPIRED';
          licenseError.handled = true; // 标记为已处理
          licenseError.originalError = error; // 保存原始错误信息

          // 对于license错误，我们不希望它传播到上层组件显示"后端调用失败"
          // 所以我们返回一个resolved Promise，但包含错误信息
          console.log('License错误已处理，阻止传播到上层组件');
          return Promise.resolve({
            data: null,
            isLicenseError: true,
            handled: true,
            message: '许可证无效或已过期，请激活系统'
          });
        }
      } else if (error.response.status === 404) {
        console.warn(`资源不存在: ${error.config?.url}`);

        // 检查是否是license相关的404错误
        if (error.config?.url?.includes('/license')) {
          console.warn('License相关资源不存在，这是正常情况（系统未激活）');
          // 对于license 404错误，我们让它正常传播，不做特殊处理
          // 这样前端可以正确处理license未激活的情况
        }
      }
    } else if (error.request) {
      // 请求已发送但未收到响应
      console.error('API Error: 请求未得到响应', error.request);
      console.error(`请求无响应: ${error.config?.method?.toUpperCase()} ${error.config?.url}`);

      // 显示服务器连接问题的详细信息
      if (error.code === 'ECONNABORTED') {
        console.error('请求超时，服务器响应时间过长');
      } else {
        console.error('无法连接到服务器，请检查网络连接或服务器状态');
      }
    } else {
      // 请求设置出错
      console.error('API Error:', error.message);
      console.error(`请求配置错误: ${error.config?.method?.toUpperCase()} ${error.config?.url}`);
    }

    return Promise.reject(error);
  }
);

// 开发环境模拟请求延迟
if (process.env.NODE_ENV === 'development') {
  api.interceptors.request.use(async (config) => {
    // 添加随机延迟以模拟网络延迟
    const delay = Math.floor(Math.random() * 800) + 200; // 200-1000ms
    await new Promise(resolve => setTimeout(resolve, delay));
    return config;
  });
}

export default api;