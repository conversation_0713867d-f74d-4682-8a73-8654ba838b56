# ABM-LLM-V2 Docker配置总结（统一镜像版）

## 已创建的文件

### 核心配置文件
1. **docker-compose.yml** - Docker Compose配置
   - 单一容器运行前后端服务
   - 使用supervisor管理多进程
   - 基础的服务编排和依赖管理

### Dockerfile文件
2. **Dockerfile** - 统一的Docker镜像文件
   - 基于Python 3.11-slim
   - 多阶段构建：前端构建 + 主镜像
   - 安装系统依赖（curl、git、build-essential、supervisor）
   - 安装Node.js和Python依赖
   - 健康检查配置
   - 使用supervisor管理前后端进程

### 配置文件
3. **supervisord.conf** - 进程管理配置文件
   - 管理后端Flask服务
   - 管理前端开发服务器
   - 日志输出配置

4. **.env.example** - 环境变量模板（可选）
   - 基础的后端和前端配置
   - 开发环境优化设置

5. **.dockerignore** - Docker构建忽略文件
   - 排除不必要的文件和目录
   - 优化构建上下文大小

### 脚本文件
6. **build-image.sh** - 镜像构建脚本
   - 专门负责Docker镜像构建
   - 支持多种构建选项（标签、仓库、缓存等）
   - 构建环境检查和验证
   - 可选的镜像测试功能
   - 彩色输出和详细的构建信息

7. **start.sh** - 便捷启动脚本
   - 基础服务管理功能（启动、停止、重启、清理）
   - 日志查看和状态检查
   - 彩色输出和用户友好的界面

8. **test.sh** - Docker配置测试脚本
   - Docker环境检查
   - 配置文件语法验证
   - 网络端口检查
   - 构建依赖验证

9. **README.md** - 使用文档
   - 完整的部署指南
   - 构建和启动流程说明

## 支持的部署模式

### 统一容器模式 (`./start.sh start`)
- 单一容器运行前后端服务
- 前端热重载开发模式
- 源代码挂载
- supervisor进程管理
- 适合开发调试和简单部署

## 主要特性

### 🚀 易用性
- 一键启动脚本
- 自动环境检查
- 详细的帮助信息

### 🔧 基础配置
- 前后端服务编排
- 开发环境优化
- 数据持久化

### 📊 监控和管理
- 健康检查配置
- 服务状态查看
- 日志聚合查看
- 优雅的服务重启

### 🔍 调试友好
- 详细的错误信息
- 配置验证脚本
- 构建测试功能

## 容器架构

```
外部访问
    ↓
┌─────────────────────────────┐
│     ABM-LLM-V2 容器          │
│  ┌─────────────────────────┐ │
│  │     Supervisor          │ │
│  │  ┌─────────┬─────────┐  │ │
│  │  │ Backend │Frontend │  │ │
│  │  │ :8080   │ :3000   │  │ │
│  │  └─────────┴─────────┘  │ │
│  └─────────────────────────┘ │
└─────────────────────────────┘
```

## 数据持久化

- **应用数据**: `../data` → `/app/data`
- **日志文件**: `../logs` → `/app/logs`
- **配置文件**: `../config.conf` → `/app/config.conf`
- **MCP配置**: `../mcp_config.json` → `/app/mcp_config.json`

## 端口映射

| 服务 | 内部端口 | 外部端口 | 说明 |
|------|----------|----------|------|
| Backend | 8080 | 8080 | Flask API服务 |
| Frontend | 3000 | 3000 | React应用（开发模式） |

## 使用建议

### 首次使用
```bash
# 1. 构建镜像
./build-image.sh

# 2. 验证配置
./test.sh

# 3. 启动服务
./start.sh start

# 4. 查看状态
./start.sh status
```

### 日常开发
```bash
./start.sh start       # 启动服务
./start.sh logs        # 查看日志
./start.sh restart     # 重启服务
./start.sh stop        # 停止服务
```

### 故障排除
```bash
./start.sh logs        # 查看日志
./start.sh status      # 检查状态
./start.sh restart     # 重启服务
./start.sh clean       # 清理重建
./build-image.sh --clean --test  # 重新构建并测试
```

这套统一镜像Docker配置提供了简单易用的单容器部署方案，适合快速开发和测试。
