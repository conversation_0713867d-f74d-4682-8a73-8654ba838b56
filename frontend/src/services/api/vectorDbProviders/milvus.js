import api from '../axios';

/**
 * Milvus向量数据库API服务
 * 提供与Milvus向量数据库相关的API函数
 */
const milvusAPI = {
  /**
   * 测试Milvus连接
   * @param {Object} config Milvus配置
   * @returns {Promise<Object>} 连接测试结果
   */
  testConnection: async (config) => {
    try {
      const response = await api.post('/vector-db/test-connection', {
        provider: 'milvus',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('测试Milvus连接失败:', error);
      throw error;
    }
  },

  /**
   * 验证Milvus配置
   * @param {Object} config Milvus配置
   * @returns {Promise<Object>} 验证结果
   */
  validateConfig: async (config) => {
    try {
      const response = await api.post('/vector-db/validate-config', {
        provider: 'milvus',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('验证Milvus配置失败:', error);
      throw error;
    }
  },

  /**
   * 获取Milvus集合列表
   * @param {Object} config Milvus配置
   * @returns {Promise<Object>} 集合列表
   */
  listCollections: async (config) => {
    try {
      const response = await api.post('/vector-db/list-collections', {
        provider: 'milvus',
        config: config
      });
      return response.data;
    } catch (error) {
      console.error('获取Milvus集合列表失败:', error);
      throw error;
    }
  },

  /**
   * 创建Milvus集合
   * @param {Object} config Milvus配置
   * @param {string} collectionName 集合名称
   * @param {Object} schema 集合schema
   * @returns {Promise<Object>} 创建结果
   */
  createCollection: async (config, collectionName, schema) => {
    try {
      const response = await api.post('/vector-db/create-collection', {
        provider: 'milvus',
        config: config,
        collection_name: collectionName,
        schema: schema
      });
      return response.data;
    } catch (error) {
      console.error('创建Milvus集合失败:', error);
      throw error;
    }
  },

  /**
   * 删除Milvus集合
   * @param {Object} config Milvus配置
   * @param {string} collectionName 集合名称
   * @returns {Promise<Object>} 删除结果
   */
  deleteCollection: async (config, collectionName) => {
    try {
      const response = await api.post('/vector-db/delete-collection', {
        provider: 'milvus',
        config: config,
        collection_name: collectionName
      });
      return response.data;
    } catch (error) {
      console.error('删除Milvus集合失败:', error);
      throw error;
    }
  },

  /**
   * 获取集合信息
   * @param {Object} config Milvus配置
   * @param {string} collectionName 集合名称
   * @returns {Promise<Object>} 集合信息
   */
  getCollectionInfo: async (config, collectionName) => {
    try {
      const response = await api.post('/vector-db/collection-info', {
        provider: 'milvus',
        config: config,
        collection_name: collectionName
      });
      return response.data;
    } catch (error) {
      console.error('获取Milvus集合信息失败:', error);
      throw error;
    }
  },

  /**
   * 向量搜索
   * @param {Object} config Milvus配置
   * @param {string} collectionName 集合名称
   * @param {Object} searchParams 搜索参数
   * @returns {Promise<Object>} 搜索结果
   */
  vectorSearch: async (config, collectionName, searchParams) => {
    try {
      const response = await api.post('/vector-db/vector-search', {
        provider: 'milvus',
        config: config,
        collection_name: collectionName,
        search_params: searchParams
      });
      return response.data;
    } catch (error) {
      console.error('Milvus向量搜索失败:', error);
      throw error;
    }
  },

  /**
   * 插入向量数据
   * @param {Object} config Milvus配置
   * @param {string} collectionName 集合名称
   * @param {Array} data 向量数据
   * @returns {Promise<Object>} 插入结果
   */
  insertVectors: async (config, collectionName, data) => {
    try {
      const response = await api.post('/vector-db/insert-vectors', {
        provider: 'milvus',
        config: config,
        collection_name: collectionName,
        data: data
      });
      return response.data;
    } catch (error) {
      console.error('插入Milvus向量数据失败:', error);
      throw error;
    }
  }
};

export default milvusAPI;
