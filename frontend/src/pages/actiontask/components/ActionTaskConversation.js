import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import {
  Input,
  Button,
  Select,
  Empty,
  Space,
  Avatar,
  Typography,
  Tag,
  Modal,
  Form,
  Row,
  Col,
  Tooltip,
  Dropdown,
  App
} from 'antd';
import {
  SendOutlined,
  RobotOutlined,
  PlusOutlined,
  TeamOutlined,
  UserOutlined,
  FileTextOutlined,
  SyncOutlined,
  StopOutlined,
  EyeOutlined,
  ReloadOutlined,
  ExpandAltOutlined,
  EditOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import conversationAPI from '../../../services/api/conversation';
import { actionTaskAPI } from '../../../services/api/actionTask';
import settingsAPI from '../../../services/api/settings';
import { modelConfigAPI } from '../../../services/api/model';
// 导入内容渲染组件
import ConversationExtraction from './ConversationExtraction';
// 导入KaTeX的样式
import 'katex/dist/katex.min.css';
// 导入工具调用解析组件
import MessageItem from './MessageItem';
// 导入自主任务模态框组件
import AutonomousTaskModal from './AutonomousTaskModal';

// 导入对话样式
import '../css/conversation.css';
// 导入智能体颜色工具函数
import { getAgentAvatarStyle } from '../../../utils/colorUtils';

const { Text } = Typography;
const { TextArea } = Input;

const ActionTaskConversation = forwardRef(({
  task,
  messages: externalMessages,
  setMessages: setExternalMessages,
  onMessagesUpdated,
  onAgentRespondingChange,
  onUserMessageSent,
  onRefreshAutonomousTaskCard
}, ref) => {
  // 获取 Ant Design App 上下文中的 message 实例
  const { message } = App.useApp();

  // 会话相关状态
  const [conversations, setConversations] = useState([]);
  const [activeConversationId, setActiveConversationId] = useState(null);
  const [conversationsLoading, setConversationsLoading] = useState(false);
  const [creatingConversation, setCreatingConversation] = useState(false);
  const [newConversationTitle, setNewConversationTitle] = useState('');
  const [showNewConversationModal, setShowNewConversationModal] = useState(false);
  const [refreshingMessages, setRefreshingMessages] = useState(false);

  // 消息相关状态
  const [messages, setMessages] = useState([]);

  // 统一的消息更新函数，同时更新内部和外部状态
  const updateMessages = (newMessages) => {
    if (typeof newMessages === 'function') {
      setMessages(prev => {
        const updated = newMessages(prev);
        // 使用 useEffect 来异步更新外部状态，避免在渲染期间更新
        if (setExternalMessages) {
          // 延迟到下一个事件循环执行，避免在渲染期间更新状态
          setTimeout(() => {
            setExternalMessages(updated);
          }, 0);
        }
        return updated;
      });
    } else {
      setMessages(newMessages);
      if (setExternalMessages) {
        // 延迟到下一个事件循环执行，避免在渲染期间更新状态
        setTimeout(() => {
          setExternalMessages(newMessages);
        }, 0);
      }
    }
  };
  const [userMessage, setUserMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const [isResponding, setIsResponding] = useState(false);
  const [targetAgentIds, setTargetAgentIds] = useState([]);
  const [isObserving, setIsObserving] = useState(false);
  const [streamingAgentId, setStreamingAgentId] = useState(null);
  const [currentStreamingResponse, setCurrentStreamingResponse] = useState('');

  // 消息辅助状态
  const [assistingMessage, setAssistingMessage] = useState(false);
  const [globalSettings, setGlobalSettings] = useState({
    enableAssistantGeneration: true,
    assistantGenerationModel: 'default'
  });
  const [models, setModels] = useState([]);

  // 添加引用，用于滚动到底部
  const messagesEndRef = useRef(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 监督者干预方法 - 直接发送消息而不修改UI状态
  const sendSupervisorIntervention = async (messageContent, supervisorAgentId) => {
    if (!activeConversationId) {
      message.warning('请先选择一个会话');
      return;
    }

    if (!messageContent.trim()) {
      message.warning('请输入消息内容');
      return;
    }

    try {
      console.log('监督者干预：直接发送消息', {
        content: messageContent,
        supervisorAgentId,
        conversationId: activeConversationId
      });

      // 设置发送状态
      setSendingMessage(true);

      // 清空流式响应状态
      setCurrentStreamingResponse('');

      // 调用用户发送消息回调
      if (onUserMessageSent) {
        onUserMessageSent();
      }

      // 先立即添加用户消息到任务会话中，让用户看到消息已发送
      const userMsg = {
        id: `supervisor-intervention-${Date.now()}`,
        role: 'human',
        content: messageContent,
        timestamp: new Date().toISOString(),
        target_agent_ids: [supervisorAgentId],
        source: 'supervisorConversation', // 标记为监督者会话来源
        meta: { type: 'info' }, // 添加干预标识
        isTemporary: true // 标记为临时消息，流式完成后会被数据库中的真实消息替换
      };

      // 立即添加到消息列表
      updateMessages(prev => [...prev, userMsg]);

      // 构建消息数据，使用干预标记
      const messageData = {
        content: messageContent,
        target_agent_id: supervisorAgentId, // 使用单个智能体ID
        send_target: 'task_intervention' // 标记为监督者干预
      };

      // 直接调用流式API，这会自动保存用户消息到数据库
      await conversationAPI.sendConversationMessageStream(
        task.id,
        activeConversationId,
        messageData,
        handleStreamResponse
      );

      console.log('监督者干预消息发送完成');
    } catch (error) {
      console.error('监督者干预发送失败:', error);
      message.error('监督者干预发送失败: ' + (error.message || '未知错误'));

      // 重置状态
      setSendingMessage(false);
      setIsResponding(false);
    }
  };

  // 注意：useImperativeHandle 将在 sendMessage 函数定义后添加

  // 获取全局设置
  const fetchGlobalSettings = async () => {
    try {
      const settings = await settingsAPI.getSettings();
      setGlobalSettings({
        enableAssistantGeneration: settings.enableAssistantGeneration !== undefined ? settings.enableAssistantGeneration : true,
        assistantGenerationModel: settings.assistantGenerationModel || 'default'
      });
    } catch (error) {
      console.error('获取全局设置失败:', error);
    }
  };

  // 获取模型配置列表
  const fetchModels = async () => {
    try {
      const configs = await modelConfigAPI.getAll();
      // 过滤出具备文本能力的模型
      const textCapableModels = configs.filter(config =>
        !config.capabilities ||
        config.capabilities.length === 0 ||
        config.capabilities.includes('text')
      );
      setModels(textCapableModels);
    } catch (error) {
      console.error('获取模型配置失败:', error);
      setModels([]);
    }
  };

  // 处理消息辅助 - 使用与其他辅助功能相同的模式
  const handleMessageAssist = async (assistMode) => {
    try {
      // 检查是否启用了辅助生成
      if (!globalSettings.enableAssistantGeneration) {
        message.warning('辅助生成功能未启用，请在系统设置中开启');
        return;
      }

      if (!userMessage.trim()) {
        message.warning('请先输入消息内容');
        return;
      }

      setAssistingMessage(true);

      // 获取系统设置的提示词模板
      let promptTemplate;
      try {
        const templates = await settingsAPI.getPromptTemplates();
        promptTemplate = templates.userMessageExpand;
      } catch (error) {
        console.error('获取提示词模板失败，使用默认模板:', error);
        // 使用默认模板
        promptTemplate = `请根据以下信息扩展和优化用户消息：

原始消息：{{original_message}}
当前行动空间：{{action_space_name}}
行动空间描述：{{action_space_description}}
参与角色：{{participant_roles}}
辅助模式：{{assist_mode}}

要求：
1. 保持原始消息的核心意图和目的
2. 根据行动空间背景和参与角色，调整语言风格和专业程度
3. 补充必要的上下文信息和细节
4. 确保消息清晰、具体、易于理解
5. 根据辅助模式进行相应的处理：
   - expand: 扩展内容，增加细节和背景信息
   - optimize: 优化表达，改善语言和逻辑结构
   - rewrite: 重新表述，用不同方式表达相同意思
   - professional: 专业化表达，提升正式程度
   - casual: 口语化表达，使语言更自然亲切

请直接返回优化后的消息内容，不需要额外的解释。`;
      }

      // 格式化参与角色
      const participantRoles = task?.agents?.filter(agent => !agent.is_observer).map(agent => agent.name).join(', ') || '';

      // 替换模板变量
      const generatePrompt = promptTemplate
        .replace('{{original_message}}', userMessage)
        .replace('{{action_space_name}}', task?.action_space?.name || '')
        .replace('{{action_space_description}}', task?.action_space?.description || '')
        .replace('{{participant_roles}}', participantRoles)
        .replace('{{assist_mode}}', assistMode);

      // 确定使用的模型
      let modelToUse = globalSettings.assistantGenerationModel;
      if (modelToUse === 'default') {
        // 使用默认文本生成模型
        const defaultModel = models.find(m => m.is_default_text) || models.find(m => m.is_default) || models[0];
        if (defaultModel) {
          modelToUse = defaultModel.id;
        } else {
          throw new Error('未找到可用的模型');
        }
      }

      let generatedContent = '';
      const handleStreamResponse = (content, meta) => {
        if (content !== null) {
          generatedContent += content;
          // 实时更新输入框内容
          setUserMessage(generatedContent);
        }
      };

      await modelConfigAPI.testModelStream(
        modelToUse,
        generatePrompt,
        handleStreamResponse,
        "你是一个专业的消息优化助手，擅长根据上下文和需求优化用户消息。",
        {
          temperature: 0.7,
          max_tokens: 1000
        }
      );

      // 最终清理生成的内容
      const cleanedContent = generatedContent
        .replace(/null/g, '')
        .replace(/undefined/g, '')
        .trim();

      setUserMessage(cleanedContent);
      message.success(`消息${getModeLabel(assistMode)}完成`);
    } catch (error) {
      console.error('辅助生成失败:', error);
      message.error(`辅助生成失败: ${error.message || '未知错误'}`);
    } finally {
      setAssistingMessage(false);
    }
  };

  // 获取模式标签
  const getModeLabel = (mode) => {
    const modeLabels = {
      expand: '扩展',
      optimize: '优化',
      rewrite: '重写',
      professional: '专业化',
      casual: '口语化'
    };
    return modeLabels[mode] || '处理';
  };

  // 滚动到底部的副作用
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 初始化时获取全局设置和模型配置
  useEffect(() => {
    fetchGlobalSettings();
    fetchModels();
  }, []);

  // 加载会话列表
  const fetchConversations = async () => {
    try {
      setConversationsLoading(true);
      const conversationsData = await conversationAPI.getConversations(task.id);
      setConversations(conversationsData);

      // 如果存在会话且没有选中的会话，则选择第一个
      if (conversationsData.length > 0 && !activeConversationId) {
        setActiveConversationId(conversationsData[0].id);
        // 加载该会话的任务消息（排除监督者相关消息）
        const messagesData = await actionTaskAPI.getTaskMessages(task.id, conversationsData[0].id);
        updateMessages(messagesData);
        // 如果有消息更新回调，则通知父组件
        if (onMessagesUpdated) {
          // 延迟到下一个事件循环执行，避免在渲染期间更新状态
          setTimeout(() => {
            onMessagesUpdated(messagesData);
          }, 0);
        }
      }
    } catch (error) {
      console.error('获取会话失败:', error);
      message.error('获取会话列表失败: ' + error.message);
    } finally {
      setConversationsLoading(false);
    }
  };

  // 处理会话切换
  const handleChangeConversation = async (conversationId) => {
    try {
      setActiveConversationId(conversationId);

      // 清空所有消息和流式状态
      updateMessages([]);
      setCurrentStreamingResponse('');
      setIsObserving(false);
      setStreamingAgentId(null);
      setIsResponding(false);

      // 加载新选择的会话的任务消息（排除监督者相关消息）
      const messagesData = await actionTaskAPI.getTaskMessages(task.id, conversationId);
      updateMessages(messagesData);

      // 如果有消息更新回调，则通知父组件
      if (onMessagesUpdated) {
        // 延迟到下一个事件循环执行，避免在渲染期间更新状态
        setTimeout(() => {
          onMessagesUpdated(messagesData);
        }, 0);
      }
    } catch (error) {
      console.error('切换会话失败:', error);
      message.error('获取会话消息失败: ' + error.message);
    }
  };

  // 创建会话
  const handleCreateConversation = async () => {
    if (!newConversationTitle.trim()) {
      message.warning('请输入会话标题');
      return;
    }

    try {
      setCreatingConversation(true);

      // 创建会话
      const newConversation = await conversationAPI.createConversation(task.id, {
        title: newConversationTitle,
        mode: task.mode || 'sequential'
      });

      // 更新会话列表并选择新创建的会话
      await fetchConversations();
      setActiveConversationId(newConversation.id);

      // 清空所有消息和流式状态
      updateMessages([]);
      setCurrentStreamingResponse('');
      setIsObserving(false);
      setStreamingAgentId(null);
      setIsResponding(false);

      // 关闭模态框并清空输入
      setShowNewConversationModal(false);
      setNewConversationTitle('');

      message.success('会话创建成功');
    } catch (error) {
      console.error('创建会话失败:', error);
      message.error('创建会话失败: ' + error.message);
    } finally {
      setCreatingConversation(false);
    }
  };

  // 刷新当前会话消息
  const handleRefreshMessages = async () => {
    if (!activeConversationId) {
      message.warning('请先选择一个会话');
      return;
    }

    try {
      setRefreshingMessages(true);

      // 清空当前流式状态
      setCurrentStreamingResponse('');
      setIsObserving(false);
      setStreamingAgentId(null);
      setIsResponding(false);

      // 重新加载当前会话的任务消息
      const messagesData = await actionTaskAPI.getTaskMessages(task.id, activeConversationId);
      updateMessages(messagesData);

      // 如果有消息更新回调，则通知父组件
      if (onMessagesUpdated) {
        setTimeout(() => {
          onMessagesUpdated(messagesData);
        }, 0);
      }

      message.success('消息刷新成功');
    } catch (error) {
      console.error('刷新消息失败:', error);
      message.error('刷新消息失败: ' + error.message);
    } finally {
      setRefreshingMessages(false);
    }
  };

  // 监听streamingAgentId变化并通知父组件
  useEffect(() => {
    if (onAgentRespondingChange) {
      onAgentRespondingChange(streamingAgentId !== null, streamingAgentId);
    }
    // 当streamingAgentId改变时，更新isResponding状态
    setIsResponding(streamingAgentId !== null);
  }, [streamingAgentId, onAgentRespondingChange]);

  // 处理流式响应回调
  const handleStreamResponse = (content, meta) => {
    // 处理虚拟消息
    if (meta && meta.type === 'virtualMessage' && meta.isVirtual) {
      const virtualMessage = {
        id: `virtual-${Date.now()}`,
        role: meta.virtualRole || 'human',
        content: content,
        timestamp: meta.timestamp || new Date().toISOString(),
        isVirtual: true
      };

      updateMessages(prev => [...prev, virtualMessage]);
      if (onMessagesUpdated) {
        // 延迟到下一个事件循环执行，避免在渲染期间更新状态
        setTimeout(() => {
          onMessagesUpdated([...messages, virtualMessage]);
        }, 0);
      }
      return;
    }

    // 处理连接状态
    if (meta) {
      // 连接建立
      if (meta.connectionStatus === 'connected') {
        console.log('流式连接已建立');
        // 连接成功建立后，重置发送消息状态，允许用户再次发送
        setSendingMessage(false);
      }
      // 连接错误
      else if (meta.connectionStatus === 'error') {
        console.error('流式连接错误:', meta.error);

        // 如果有智能体信息，添加一个错误消息
        if (meta.agentId && task.agents) {
          const agent = task.agents.find(a =>
            a.id === meta.agentId ||
            String(a.id) === String(meta.agentId)
          );

          if (agent) {
            // 创建错误消息
            const errorMessage = {
              id: `error-${Date.now()}`,
              role: 'system',
              content: `[错误] API请求失败: ${meta.error}`,
              timestamp: new Date().toISOString(),
              agent_name: agent.name,
              agent_id: agent.id,
              role_name: agent.role_name,
              agent: {
                id: agent.id,
                name: agent.name,
                role_name: agent.role_name
              }
            };

            // 添加到消息列表
            updateMessages(prev => [...prev, errorMessage]);

            // 通知父组件
            if (onMessagesUpdated) {
              // 延迟到下一个事件循环执行，避免在渲染期间更新状态
              setTimeout(() => {
                onMessagesUpdated([...messages, errorMessage]);
              }, 0);
            }
          }
        }

        // 重置状态
        setIsObserving(false);
        setStreamingAgentId(null);
        setSendingMessage(false); // 解除发送状态
        setIsResponding(false); // 重置响应状态
        message.error(`错误: ${meta.error}`);
      }
      // 连接完成
      else if (meta.connectionStatus === 'done') {
        console.log('流式连接完成:', meta.responseObj);

        // 保存当前流式内容的临时变量，防止清空后无法正确添加到历史消息
        const finalStreamContent = currentStreamingResponse || '';

        // 立即清空流式响应内容，防止流式响应框继续显示
        setCurrentStreamingResponse('');

        // 重置状态
        setIsObserving(false);
        setStreamingAgentId(null);
        setSendingMessage(false); // 解除发送状态
        setIsResponding(false); // 重置响应状态

        // 检查是否是监督者干预消息（通过检查智能体是否为监督者）
        let isSupervisorIntervention = false;
        if (meta.responseObj && meta.responseObj.response && meta.responseObj.response.agent_id) {
          const agentId = meta.responseObj.response.agent_id;
          const agent = task.agents?.find(a => a.id === agentId);
          isSupervisorIntervention = agent && agent.is_observer;
        }

        // 如果是监督者干预，刷新消息列表以获取完整的数据库记录
        if (isSupervisorIntervention) {
          console.log('监督者干预完成，刷新消息列表');
          actionTaskAPI.getTaskMessages(task.id, activeConversationId)
            .then(messagesData => {
              updateMessages(messagesData);

              // 通知父组件
              if (onMessagesUpdated) {
                setTimeout(() => {
                  onMessagesUpdated(messagesData);
                }, 0);
              }
            })
            .catch(error => {
              console.error('刷新监督者干预消息失败:', error);
              // 如果刷新失败，回退到原来的逻辑
              if (meta.responseObj && meta.responseObj.response) {
                const completeResponse = meta.responseObj.response;
                const agentResponse = {
                  id: completeResponse.id || `stream-${Date.now()}`,
                  role: 'assistant',
                  content: finalStreamContent || completeResponse.content || '无内容',
                  timestamp: completeResponse.timestamp || new Date().toISOString(),
                  agent_name: completeResponse.agent_name,
                  agent_id: completeResponse.agent_id,
                  agent: {
                    id: completeResponse.agent_id,
                    name: completeResponse.agent_name
                  },
                  response_order: completeResponse.response_order || null
                };
                updateMessages(prev => [...prev, agentResponse]);
                if (onMessagesUpdated) {
                  setTimeout(() => {
                    onMessagesUpdated([...messages, agentResponse]);
                  }, 0);
                }
              }
            });
        } else {
          // 普通消息，使用原来的逻辑
          if (meta.responseObj && meta.responseObj.response) {
            const completeResponse = meta.responseObj.response;

            // 创建完整的智能体响应对象 - 使用已流式显示的内容，不使用后端返回的可能重复的内容
            const agentResponse = {
              id: completeResponse.id || `stream-${Date.now()}`,
              role: 'assistant',
              content: finalStreamContent || completeResponse.content || '无内容',
              timestamp: completeResponse.timestamp || new Date().toISOString(),
              agent_name: completeResponse.agent_name,
              agent_id: completeResponse.agent_id,
              agent: {
                id: completeResponse.agent_id,
                name: completeResponse.agent_name
              },
              response_order: completeResponse.response_order || null
            };

            // 添加到消息列表
            updateMessages(prev => [...prev, agentResponse]);

            // 通知父组件
            if (onMessagesUpdated) {
              // 延迟到下一个事件循环执行，避免在渲染期间更新状态
              setTimeout(() => {
                onMessagesUpdated([...messages, agentResponse]);
              }, 0);
            }
          }
        }
      }

      // 处理单个智能体完成事件 (agentDone)
      else if (meta.connectionStatus === 'agentDone') {
        console.log('单个智能体完成:', meta.responseObj);

        // 保存当前流式内容的临时变量
        const finalStreamContent = currentStreamingResponse || '';

        // 更新消息列表 (如果有完整响应对象)
        if (meta.responseObj && meta.responseObj.response) {
          const completeResponse = meta.responseObj.response;

          // 创建完整的智能体响应对象
          const agentResponse = {
            id: completeResponse.id || `stream-${Date.now()}`,
            role: 'assistant',
            content: finalStreamContent || completeResponse.content || '无内容',
            timestamp: completeResponse.timestamp || new Date().toISOString(),
            agent_name: completeResponse.agent_name,
            agent_id: completeResponse.agent_id,
            agent: {
              id: completeResponse.agent_id,
              name: completeResponse.agent_name
            },
            response_order: completeResponse.response_order || null
          };

          // 添加到消息列表
          updateMessages(prev => [...prev, agentResponse]);

          // 通知父组件
          if (onMessagesUpdated) {
            // 延迟到下一个事件循环执行，避免在渲染期间更新状态
            setTimeout(() => {
              onMessagesUpdated([...messages, agentResponse]);
            }, 0);
          }
        }

        // 清空当前智能体的流式内容
        setCurrentStreamingResponse('');
        setIsObserving(false);

        // 判断是否为单智能体场景或最后一个智能体
        const isMultiAgentScenario = targetAgentIds.length > 1 || (targetAgentIds.length === 0 && task.agents?.length > 1);

        if (!isAutoDiscussing && !isMultiAgentScenario) {
          // 单智能体场景，完全清理状态
          console.log('单智能体场景，完全清理流式状态');
          setStreamingAgentId(null);
          setIsResponding(false);
          setSendingMessage(false);
        } else {
          // 多智能体场景，保持响应状态等待下一个智能体
          console.log('多智能体场景，等待下一个智能体');
          // 不清理 streamingAgentId 和 isResponding，等待下一个 agentInfo 事件
        }
      }


      // 处理工具调用结果处理通知
      if (meta.type === 'processingToolResults') {
        console.log('处理工具调用结果:', meta);

        // 添加系统消息通知用户正在处理工具结果
        const processingMessage = {
          id: `processing-tools-${Date.now()}`,
          role: 'system',
          content: meta.message || '正在处理工具调用结果，继续生成回复...',
          timestamp: new Date().toISOString(),
          isTemporary: true // 标记为临时消息，如果需要可以在UI中特殊处理
        };

        // 不添加到历史消息，只在UI中显示
        message.info(processingMessage.content);

        // 保持响应状态，确保继续接收流式内容
        setIsResponding(true);

        // 注意：我们不在这里触发变量刷新，而是在检测到实际工具调用结果时触发一次
      }

      // 处理工具结果处理状态变更
      if (meta.type === 'toolResultsProcessing') {
        console.log('工具结果处理状态:', meta);

        // 根据状态处理
        if (meta.status === 'starting') {
          // 二次响应开始，显示通知
          message.info(meta.message || '处理工具调用结果完成，正在生成最终回复...');

          // 如果是连续响应，保持当前响应状态
          if (meta.isContinuation) {
            // 保持响应状态，确保继续接收流式内容
            setIsResponding(true);
          }
        } else if (meta.status === 'completed') {
          // 处理完成，可以添加一个完成标记（可选）
          console.log('工具调用结果处理完成');

          // 保持响应状态，直到收到最终的 'done' 事件
          setIsResponding(true);

          // 注意：我们不在这里触发变量刷新，而是在检测到实际工具调用结果时触发一次
        }
      }

      // 处理智能体信息事件
      if (meta.type === 'agentInfo') {
        console.log('智能体信息:', meta);
        // 设置当前流式智能体ID
        if (meta.agentId) {
          setStreamingAgentId(String(meta.agentId));
          // 一旦收到智能体信息，表示响应开始，解除发送按钮的加载状态
          setSendingMessage(false);
        }

        // 显示智能体信息通知
        const turnPrompt = meta.turnPrompt || `轮到智能体回应`;
        if (meta.responseOrder && meta.totalAgents) {
          message.info(`${turnPrompt} (${meta.responseOrder}/${meta.totalAgents})`);
        } else {
          message.info(turnPrompt);
        }
      }

      // 处理智能体取消完成事件 - 标准格式
      if (meta.connectionStatus === 'agentDone' && meta.responseObj && meta.responseObj.response && meta.responseObj.response.is_cancelled) {
        console.log('智能体取消完成:', meta);

        // 保存当前流式内容的临时变量
        const finalStreamContent = currentStreamingResponse || '';

        // 立即清空流式响应内容，防止流式响应框继续显示
        setCurrentStreamingResponse('');
        setIsObserving(false);

        // 不结束总流程，但添加该智能体的响应（包含已经流式输出的内容）
        const completeResponse = meta.responseObj.response;

        // 创建完整的智能体响应对象，使用已经流式输出的内容
        const agentResponse = {
          id: completeResponse.id || `cancel-${Date.now()}`,
          role: 'assistant',
          content: finalStreamContent, // 使用已经流式输出的内容
          timestamp: completeResponse.timestamp || new Date().toISOString(),
          agent_name: completeResponse.agent_name,
          agent_id: completeResponse.agent_id,
          agent: {
            id: completeResponse.agent_id,
            name: completeResponse.agent_name
          },
          response_order: completeResponse.response_order || null,
          is_cancelled: true
        };

        // 添加到消息列表
        updateMessages(prev => [...prev, agentResponse]);

        // 通知父组件
        if (onMessagesUpdated) {
          // 延迟到下一个事件循环执行，避免在渲染期间更新状态
          setTimeout(() => {
            onMessagesUpdated([...messages, agentResponse]);
          }, 0);
        }

        // 添加一个系统消息，说明智能体响应被取消
        const systemMessage = {
          id: `system-cancel-${Date.now()}`,
          role: 'system',
          content: completeResponse.content || `智能体 ${completeResponse.agent_name} 的响应被用户取消`,
          timestamp: new Date().toISOString()
        };

        // 添加系统消息到消息列表
        updateMessages(prev => [...prev, systemMessage]);

        // 通知父组件
        if (onMessagesUpdated) {
          // 延迟到下一个事件循环执行，避免在渲染期间更新状态
          setTimeout(() => {
            onMessagesUpdated([...messages, agentResponse, systemMessage]);
          }, 0);
        }

        // 判断是否为单智能体场景
        const isMultiAgentScenario = targetAgentIds.length > 1 || (targetAgentIds.length === 0 && task.agents?.length > 1);
        if (!isAutoDiscussing && !isMultiAgentScenario) {
          // 单智能体场景，完全清理状态
          console.log('智能体取消完成 - 单智能体场景，完全清理流式状态');
          setStreamingAgentId(null);
          setIsResponding(false);
          setSendingMessage(false);
        } else {
          // 多智能体场景，保持响应状态等待下一个智能体
          console.log('智能体取消完成 - 多智能体场景，等待下一个智能体');
          // 不清理 streamingAgentId 和 isResponding，等待下一个 agentInfo 事件或 done 事件
        }

        // 显示通知
        message.info('智能体响应被取消，将继续处理下一个智能体');
      }

      // 处理思考内容（观察）
      if (meta.type === 'thinking') {
        setIsObserving(true);
        // 确保agentId存储为字符串类型
        if (meta.agentId) {
          console.log(`设置流式智能体ID: ${meta.agentId}, 类型: ${typeof meta.agentId}`);
          setStreamingAgentId(String(meta.agentId));
          // 一旦收到思考内容，表示响应开始，解除发送按钮的加载状态
          setSendingMessage(false);
        }
      }

      // 处理智能体响应顺序信息
      if (meta.responseOrder) {
        console.log(`智能体响应顺序: ${meta.responseOrder}`);
      }
    }

    // 不再需要处理HTML注释分隔符
    // 前端可以通过消息类型识别工具调用和结果处理的状态

    // 检测工具调用结果 - 使用更精确的匹配，避免频繁触发
    if (content && typeof content === 'string' &&
        (content.includes('"meta":{"ToolCallResult"') ||
         content.includes('"toolName"') && content.includes('"toolCallId"') ||
         content.includes('"type":"toolResult"') && content.includes('"role":"tool"') ||
         content.includes('tool_call_id') && content.includes('name') && content.includes('content'))) {
      console.log('检测到工具调用结果内容:', content.substring(0, 100));
      // 通知父组件刷新变量，但只在真正有工具调用结果时触发一次
      if (onUserMessageSent && !window._toolCallResultProcessed) {
        console.log('检测到工具调用结果内容，触发一次变量刷新');
        window._toolCallResultProcessed = true;
        // 使用setTimeout确保在DOM更新后执行
        setTimeout(() => {
          console.log('执行变量刷新回调');
          onUserMessageSent();
          // 5秒后重置标志，允许下一次工具调用触发刷新
          setTimeout(() => {
            console.log('重置工具调用结果处理标志');
            window._toolCallResultProcessed = false;
          }, 5000);
        }, 100);
      }
    }

    // 处理实际内容
    if (content) {
      // 收到内容表示已经开始响应，解除发送按钮的加载状态
      setSendingMessage(false);
      setCurrentStreamingResponse(prev => prev + content);

      // 自动滚动到底部
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  // 添加一个防护机制，定期检查并清理异常的流式状态
  useEffect(() => {
    const checkStreamingState = () => {
      // 如果有流式内容但没有响应状态，或者没有智能体ID，则清理状态
      if (currentStreamingResponse && (!isResponding || !streamingAgentId)) {
        console.log('检测到异常的流式状态，执行清理:', {
          hasStreamingResponse: !!currentStreamingResponse,
          isResponding,
          streamingAgentId
        });
        setCurrentStreamingResponse('');
        setIsObserving(false);
        if (!isResponding) {
          setStreamingAgentId(null);
        }
      }
    };

    // 每2秒检查一次
    const interval = setInterval(checkStreamingState, 2000);

    return () => clearInterval(interval);
  }, [currentStreamingResponse, isResponding, streamingAgentId]);

  // 发送消息 (使用流式API)
  const sendMessage = async () => {
    // 如果当前正在响应中，先取消当前响应
    if (isResponding) {
      // 获取当前正在流式输出的智能体ID
      const currentAgentId = streamingAgentId;

      // 保存当前流式内容的临时变量
      const finalStreamContent = currentStreamingResponse || '';

      // 查找当前智能体的详细信息
      let agentInfo = null;
      if (currentAgentId && task.agents) {
        agentInfo = task.agents.find(agent =>
          agent.id === Number(currentAgentId) ||
          String(agent.id) === String(currentAgentId)
        );
      }

      // 先设置加载状态，防止用户重复点击
      setSendingMessage(true);

      try {
        // 如果有流式内容，先添加一个智能体响应消息，保留已经流式输出的内容
        if (finalStreamContent) {
          // 创建智能体响应对象
          const agentResponse = {
            id: `stream-${Date.now()}`,
            role: 'assistant',
            content: finalStreamContent,
            timestamp: new Date().toISOString(),
            agent_id: currentAgentId,
            agent_name: agentInfo ? agentInfo.name : null,
            agent: agentInfo ? {
              id: agentInfo.id,
              name: agentInfo.name,
              role_name: agentInfo.role_name
            } : null,
            is_cancelled: true
          };

          // 添加到消息列表
          updateMessages(prev => [...prev, agentResponse]);
        }

        // 调用API取消当前正在进行的流式响应，传递智能体ID
        await conversationAPI.cancelStreamingResponse(currentAgentId);
        console.log(`已发送取消流式响应请求，智能体ID: ${currentAgentId || '无'}`);

        // 记录取消信息
        const cancellationMessage = {
          id: `system-${Date.now()}`,
          role: 'system',
          content: agentInfo
            ? `用户中断了智能体 ${agentInfo.name}${agentInfo.role_name ? ` [${agentInfo.role_name}]` : ''} 的响应`
            : `用户中断了智能体的响应`,
          timestamp: new Date().toISOString()
        };

        // 添加到消息列表
        updateMessages(prev => [...prev, cancellationMessage]);

        // 通知父组件（只发送一次更新，包含所有新消息）
        if (onMessagesUpdated) {
          const updatedMessages = [...messages];

          // 如果有流式内容，添加智能体响应
          if (finalStreamContent) {
            updatedMessages.push({
              id: `stream-${Date.now()}`,
              role: 'assistant',
              content: finalStreamContent,
              timestamp: new Date().toISOString(),
              agent_id: currentAgentId,
              agent_name: agentInfo ? agentInfo.name : null,
              agent: agentInfo ? {
                id: agentInfo.id,
                name: agentInfo.name,
                role_name: agentInfo.role_name
              } : null,
              is_cancelled: true
            });
          }

          // 添加取消消息
          updatedMessages.push(cancellationMessage);

          // 延迟到下一个事件循环执行，避免在渲染期间更新状态
          setTimeout(() => {
            onMessagesUpdated(updatedMessages);
          }, 0);
        }

        // 显示取消成功通知
        if (isAutoDiscussing) {
          message.success(`已成功中断智能体${agentInfo ? ` ${agentInfo.name}` : ''}，自主任务将继续下一个智能体`);
        } else {
          message.success('已成功中断智能体响应');
        }
      } catch (error) {
        console.error('取消流式响应出错:', error);
        message.error('中断智能体响应时出错: ' + error.message);
      } finally {
        // 无论成功与否，都清空当前的流式响应状态
        setCurrentStreamingResponse('');
        setIsObserving(false);
        // 在自主任务或多智能体场景中，不要立即清空streamingAgentId，等待下一个agentInfo事件或done事件
        const isMultiAgentScenario = targetAgentIds.length > 1 || (targetAgentIds.length === 0 && task.agents?.length > 1);
        if (!isAutoDiscussing && !isMultiAgentScenario) {
          console.log('取消流式响应 - 单智能体场景，完全清理流式状态');
          setStreamingAgentId(null);
          setIsResponding(false);
        } else {
          console.log('取消流式响应 - 多智能体场景，保持部分状态等待下一个智能体');
        }
        setSendingMessage(false);
      }

      return;
    }

    // 如果没有消息要发送，直接返回
    if (!userMessage.trim()) return;

    setSendingMessage(true);

    try {
      // 创建新的用户消息
      const userMsg = {
        id: `msg-${Date.now()}`,
        role: 'human',
        content: userMessage,
        timestamp: new Date().toISOString(),
        target_agent_ids: targetAgentIds.length > 0 ? targetAgentIds : null
      };

      // 添加到消息列表
      updateMessages(prev => [...prev, userMsg]);

      // 清空输入框和重置流媒体状态
      setUserMessage('');
      setCurrentStreamingResponse('');

      // 调用用户发送消息回调
      if (onUserMessageSent) {
        onUserMessageSent();
      }

      // 尝试发送到真实API
      if (task.status === 'active') {
        try {
          let currentConversationId = activeConversationId;

          // 如果没有活动会话，获取或创建默认会话
          if (!currentConversationId) {
            console.log('未选择会话，获取或创建默认会话');

            // 获取所有会话
            const conversations = await conversationAPI.getConversations(task.id);

            if (conversations && conversations.length > 0) {
              // 使用第一个会话作为默认会话
              currentConversationId = conversations[0].id;
              setActiveConversationId(currentConversationId);
              console.log(`使用现有默认会话ID: ${currentConversationId}`);
            } else {
              // 创建新的默认会话
              const newConversation = await conversationAPI.createConversation(task.id, {
                title: `${task.title || '行动任务'} - 默认会话`,
                description: '自动创建的默认会话',
                mode: task.mode || 'sequential'
              });

              currentConversationId = newConversation.id;
              setActiveConversationId(currentConversationId);
              console.log(`创建并使用新默认会话ID: ${currentConversationId}`);

              // 刷新会话列表
              fetchConversations();
            }
          }

          // 发送消息到会话
          console.log(`流式发送消息到会话:${currentConversationId}`, userMessage, targetAgentIds);

          const messageData = {
            content: userMessage,
            target_agent_ids: targetAgentIds.length > 0 ? targetAgentIds : null
          };

          // 使用流式API
          await conversationAPI.sendConversationMessageStream(
            task.id,
            currentConversationId,
            messageData,
            handleStreamResponse
          );

          // 发送成功
        } catch (error) {
          console.error('API发送消息失败:', error);
          // 发送失败

          // 添加一个错误消息
          // 获取当前正在流式输出的智能体ID
          const currentAgentId = streamingAgentId;

          // 查找当前智能体的详细信息
          let agentInfo = null;
          if (currentAgentId && task.agents) {
            agentInfo = task.agents.find(agent =>
              agent.id === Number(currentAgentId) ||
              String(agent.id) === String(currentAgentId)
            );
          }

          const errorResponse = {
            id: `error-${Date.now()}`,
            role: 'system',
            content: `错误: ${error.message || '未知错误'}。请检查网络连接或联系管理员。`,
            timestamp: new Date().toISOString(),
            thinking: `错误详情: ${error.stack || error.message}`
          };

          // 如果有智能体信息，添加到错误消息中
          if (agentInfo) {
            errorResponse.agent_id = agentInfo.id;
            errorResponse.agent_name = agentInfo.name;
            errorResponse.role_name = agentInfo.role_name;
            errorResponse.agent = {
              id: agentInfo.id,
              name: agentInfo.name,
              role_name: agentInfo.role_name
            };
          }

          updateMessages(prev => [...prev, errorResponse]);

          // 如果有消息更新回调，则通知父组件
          if (onMessagesUpdated) {
            // 延迟到下一个事件循环执行，避免在渲染期间更新状态
            setTimeout(() => {
              onMessagesUpdated([...messages, userMsg, errorResponse]);
            }, 0);
          }

          message.error(`消息发送失败: ${error.message}`);
        }
      } else {
        message.warning(`行动任务当前状态(${task.status})不允许发送消息`);

        // 添加一个系统消息说明任务状态
        const statusMessage = {
          id: `status-${Date.now()}`,
          role: 'system',
          content: `当前任务状态为 "${task.status}"，无法发送消息。请先激活任务。`,
          timestamp: new Date().toISOString()
        };

        updateMessages(prev => [...prev, statusMessage]);

        // 如果有消息更新回调，则通知父组件
        if (onMessagesUpdated) {
          // 延迟到下一个事件循环执行，避免在渲染期间更新状态
          setTimeout(() => {
            onMessagesUpdated([...messages, userMsg, statusMessage]);
          }, 0);
        }
      }
    } catch (error) {
      console.error('发送消息错误:', error);
      message.error('发送消息失败: ' + (error.message || '未知错误'));
      // 确保在出错时恢复按钮状态
      setSendingMessage(false);
      setIsResponding(false);
    }
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    sendSupervisorIntervention
  }), [activeConversationId, task.id, updateMessages, handleStreamResponse, onUserMessageSent]);

  // 加载环境变量和智能体变量
  const fetchVariables = async () => {
    if (!task || !task.id) return;

    try {
      // 使用批量API获取所有变量
      const batchVariables = await actionTaskAPI.getBatchVariables(task.id);

      // 设置环境变量
      setEnvironmentVariables(batchVariables.environmentVariables || []);

      // 处理智能体变量
      const agentVars = {};
      if (task.agents && task.agents.length > 0) {
        // 按智能体ID分组变量
        for (const variable of batchVariables.agentVariables) {
          if (variable.agent_id) {
            if (!agentVars[variable.agent_id]) {
              agentVars[variable.agent_id] = [];
            }
            agentVars[variable.agent_id].push(variable);
          }
        }

        // 将变量添加到智能体对象中
        for (const agent of task.agents) {
          if (agentVars[agent.id]) {
            agent.variables = agentVars[agent.id];
          } else {
            agent.variables = [];
          }
        }
      }

      setAgentVariables(agentVars);
      console.log('批量获取变量成功，最后更新时间:', batchVariables.lastUpdated);
    } catch (error) {
      console.error('获取变量失败:', error);
      message.error('获取环境变量失败: ' + error.message);
    }
  };

  // 同步外部messages状态
  useEffect(() => {
    if (externalMessages && Array.isArray(externalMessages)) {
      // 只更新内部状态，避免循环更新
      setMessages(externalMessages);
    }
  }, [externalMessages]);

  // 初始化加载会话列表和变量
  useEffect(() => {
    if (task && task.id) {
      fetchConversations();
      fetchVariables();
    }
  }, [task]);

  // 添加自动讨论相关状态
  const [autoDiscussModalVisible, setAutoDiscussModalVisible] = useState(false);
  const [autoDiscussionOptions, setAutoDiscussionOptions] = useState({
    rounds: 1,
    topic: '',
    summarize: true,
    summarizerAgentId: null, // 添加总结智能体ID
    isInfinite: false,
    isTimeTrigger: false,
    isVariableTrigger: false,
    stopConditions: [],
    speakingMode: 'sequential',
    // 计划功能相关字段
    enablePlanning: false,
    plannerAgentId: null,
    // 时间触发模式相关字段
    timeInterval: 30,
    maxExecutions: 0,
    triggerAction: 'single_round',
    triggerRounds: 2,
    enableTimeLimit: false,
    totalTimeLimit: 1440,
    // 变量触发模式相关字段
    apiUrl: '',
    apiMethod: 'GET',
    apiHeaders: [],
    apiBody: '',
    responseDataPath: '',
    triggerConditions: [],
    checkInterval: 60,
    maxTriggerExecutions: 0,
    variableTriggerAction: 'single_round',
    variableTriggerRounds: 2
  });

  // 添加环境变量和智能体变量状态
  const [environmentVariables, setEnvironmentVariables] = useState([]);
  const [agentVariables, setAgentVariables] = useState({});
  const [startingAutoDiscussion, setStartingAutoDiscussion] = useState(false);
  const [stoppingDiscussion, setStoppingDiscussion] = useState(false);
  const [isAutoDiscussing, setIsAutoDiscussing] = useState(false);
  const [currentDiscussionRound, setCurrentDiscussionRound] = useState(0);
  const [currentDiscussionTotalRounds, setCurrentDiscussionTotalRounds] = useState(0);
  const [discussionAgentInfo, setDiscussionAgentInfo] = useState(null);

  // 处理自动讨论模态框显示
  const showAutoDiscussModal = () => {
    // 检查会话是否存在
    if (!activeConversationId) {
      message.warning('请先选择或创建一个会话');
      return;
    }

    // 检查当前会话的智能体数量
    const currentConversation = conversations.find(conv => conv.id === activeConversationId);
    if (!currentConversation || currentConversation.agent_count < 2) {
      message.warning('当前会话中智能体数量不足，自动讨论需要至少两个智能体');
      return;
    }

    setAutoDiscussModalVisible(true);
  };

  // 处理自动讨论模态框取消
  const handleAutoDiscussCancel = () => {
    setAutoDiscussModalVisible(false);
  };

  // 处理自动讨论模态框确认
  const handleAutoDiscussConfirm = async () => {
    try {
      setStartingAutoDiscussion(true);
      setAutoDiscussModalVisible(false);

      // 检查会话是否存在
      if (!activeConversationId) {
        message.warning('请先选择或创建一个会话');
        setStartingAutoDiscussion(false);
        return;
      }

      // 检查当前会话的智能体数量
      const currentConversation = conversations.find(conv => conv.id === activeConversationId);
      if (!currentConversation || currentConversation.agent_count < 2) {
        message.warning('当前会话中智能体数量不足，自动讨论需要至少两个智能体');
        setStartingAutoDiscussion(false);
        return;
      }

      // 开始自动讨论
      setIsAutoDiscussing(true);

      // 刷新自主行动卡片
      if (onRefreshAutonomousTaskCard) {
        onRefreshAutonomousTaskCard();
      }

      await conversationAPI.startAutoDiscussion(
        task.id,
        activeConversationId,
        autoDiscussionOptions,
        handleAutoDiscussionResponse
      );
    } catch (error) {
      console.error('启动自动讨论失败:', error);
      message.error('启动自动讨论失败: ' + error.message);
      setIsAutoDiscussing(false);

      // 刷新自主行动卡片
      if (onRefreshAutonomousTaskCard) {
        onRefreshAutonomousTaskCard();
      }
    } finally {
      setStartingAutoDiscussion(false);
    }
  };

  // 处理自动讨论响应
  const handleAutoDiscussionResponse = (content, meta) => {
    console.log('自动讨论收到数据:', { content, meta });

    // 处理连接状态
    if (meta) {
      // 输出详细的meta数据，帮助调试
      console.log('自动讨论meta详情:', JSON.stringify(meta));

      // 检查是否包含工具调用结果
      if (meta.type === 'toolCallResult' ||
          (meta.ToolCallResult && meta.toolCallId) ||
          (meta.toolName && meta.toolCallId) ||
          (meta.type === 'toolResult' && meta.role === 'tool')) {
        console.log('自动讨论中检测到工具调用结果:', meta);

        // 通知父组件刷新变量，但只在真正有工具调用结果时触发一次
        if (onUserMessageSent && !window._toolCallResultProcessed) {
          console.log('自动讨论中检测到工具调用结果，触发一次变量刷新');
          window._toolCallResultProcessed = true;
          // 使用setTimeout确保在DOM更新后执行
          setTimeout(() => {
            onUserMessageSent();
            // 5秒后重置标志，允许下一次工具调用触发刷新
            setTimeout(() => {
              window._toolCallResultProcessed = false;
            }, 5000);
          }, 100);
        }
      }

      // 连接建立
      if (meta.connectionStatus === 'connected') {
        console.log('自动讨论流式连接已建立');
      }
      // 连接错误
      else if (meta.connectionStatus === 'error') {
        console.error('自动讨论流式连接错误:', meta.error);
        message.error(`自动讨论错误: ${meta.error}`);
        // 重置所有讨论状态
        setIsAutoDiscussing(false);
        setStreamingAgentId(null);
        setIsResponding(false);
        setCurrentDiscussionRound(0);
        setCurrentDiscussionTotalRounds(0);
        setDiscussionAgentInfo(null);
        setCurrentStreamingResponse('');

        // 刷新自主行动卡片
        if (onRefreshAutonomousTaskCard) {
          onRefreshAutonomousTaskCard();
        }
      }
      // 连接完成
      else if (meta.connectionStatus === 'done') {
        console.log('自动讨论流式连接完成 - 收到done状态:', meta);
        message.success(meta.message || '自动讨论已完成');

        // 强制重置所有讨论状态
        setIsAutoDiscussing(false);
        setCurrentDiscussionRound(0);
        setCurrentDiscussionTotalRounds(0);
        setDiscussionAgentInfo(null);
        setStreamingAgentId(null);
        setIsResponding(false);
        setCurrentStreamingResponse('');

        // 刷新自主行动卡片
        if (onRefreshAutonomousTaskCard) {
          onRefreshAutonomousTaskCard();
        }
      }

      // 处理轮次信息
      if (meta.roundInfo) {
        console.log('轮次信息:', meta.roundInfo);
        setCurrentDiscussionRound(meta.roundInfo.current || 0);
        setCurrentDiscussionTotalRounds(meta.roundInfo.total || 0);

        // 添加轮次信息系统消息，直接显示而非等待刷新
        const roundMessage = {
          id: `round-${Date.now()}`,
          role: 'system',
          content: `开始第${meta.roundInfo.current || 0}/${meta.roundInfo.total || 0}轮讨论`,
          timestamp: new Date().toISOString()
        };
        updateMessages(prev => {
          const updatedMessages = [...prev, roundMessage];
          // 在状态更新回调中通知父组件，确保使用最新的消息数组
          if (onMessagesUpdated) {
            setTimeout(() => {
              onMessagesUpdated(updatedMessages);
            }, 0);
          }
          return updatedMessages;
        });

        message.info(`开始第${meta.roundInfo.current || 0}/${meta.roundInfo.total || 0}轮讨论`);
      }

      // 处理智能体信息
      if (meta.type === 'agentInfo') {
        console.log('智能体信息:', meta);
        const turnPrompt = meta.turnPrompt || `轮到智能体行动`;
        const agentId = meta.agentId;
        const agentName = meta.agentName;

        setDiscussionAgentInfo({
          id: agentId,
          name: agentName,
          responseOrder: meta.responseOrder,
          totalAgents: meta.totalAgents,
          round: meta.round,
          totalRounds: meta.totalRounds,
          isSummarizing: meta.isSummarizing || false,
          turnPrompt: turnPrompt
        });

        // 添加轮到智能体行动的系统消息
        const promptMessage = {
          id: `prompt-${Date.now()}`,
          role: 'system',
          content: meta.isSummarizing ?
            turnPrompt :
            `${turnPrompt} (${meta.responseOrder}/${meta.totalAgents})`,
          timestamp: new Date().toISOString()
        };
        updateMessages(prev => {
          const updatedMessages = [...prev, promptMessage];
          // 在状态更新回调中通知父组件，确保使用最新的消息数组
          if (onMessagesUpdated) {
            setTimeout(() => {
              onMessagesUpdated(updatedMessages);
            }, 0);
          }
          return updatedMessages;
        });

        // 设置当前流式智能体ID，这样流式响应才能正确显示
        setStreamingAgentId(String(agentId));
        // 同时设置正在响应状态
        setIsResponding(true);
        // 每次切换智能体时清空当前流式响应
        setCurrentStreamingResponse('');

        if (meta.isSummarizing) {
          message.info(turnPrompt);
        } else {
          message.info(`${turnPrompt} (${meta.responseOrder}/${meta.totalAgents})`);
        }
      }

      // 处理自动讨论中的消息
      if (meta.message) {
        console.log('收到系统消息:', meta.message);

        // 添加系统消息，直接显示而非等待刷新
        const systemMessage = {
          id: meta.message.id || `system-${Date.now()}`,
          role: 'system',
          content: meta.message.content || meta.message, // 优先使用content字段，兼容旧格式
          timestamp: meta.message.created_at || new Date().toISOString()
        };
        updateMessages(prev => [...prev, systemMessage]);
        if (onMessagesUpdated) {
          // 延迟到下一个事件循环执行，避免在渲染期间更新状态
          setTimeout(() => {
            onMessagesUpdated([...messages, systemMessage]);
          }, 0);
        }
      }

      // 处理智能体完成响应事件
      if (meta.connectionStatus === 'agentDone' && meta.responseObj && meta.responseObj.response) {
        console.log('智能体响应完成:', meta.responseObj.response);

        // 清空当前流式响应，为下一个智能体准备
        setCurrentStreamingResponse('');

        // 在agentDone事件中，不清空streamingAgentId，等待下一个agentInfo事件或done事件
        // 这样可以避免在多智能体场景中显示"系统"
        console.log('智能体完成，保留streamingAgentId等待下一个智能体或done事件');

        // 直接更新消息列表，而不是重新加载
        const response = meta.responseObj.response;
        const newMessage = {
          id: response.id || `msg-${Date.now()}`,
          role: 'assistant',
          content: response.content,
          timestamp: response.timestamp || new Date().toISOString(),
          agent_name: response.agent_name,
          agent_id: response.agent_id,
          agent: {
            id: response.agent_id,
            name: response.agent_name
          }
        };

        updateMessages(prev => {
          const updatedMessages = [...prev, newMessage];
          // 在状态更新回调中通知父组件，确保使用最新的消息数组
          if (onMessagesUpdated) {
            setTimeout(() => {
              onMessagesUpdated(updatedMessages);
            }, 0);
          }
          return updatedMessages;
        });
      }
    }

    // 处理实际内容（流式返回的内容）
    if (content) {
      console.log('自动讨论收到内容:', typeof content, content.length > 50 ? content.substring(0, 50) + '...' : content);

      // 检查内容中是否包含工具调用结果
      if (typeof content === 'string' &&
          (content.includes('"meta":{"ToolCallResult"') ||
           content.includes('"toolName"') && content.includes('"toolCallId"') ||
           content.includes('"type":"toolResult"') && content.includes('"role":"tool"') ||
           content.includes('tool_call_id') && content.includes('name') && content.includes('content'))) {
        console.log('自动讨论内容中检测到工具调用结果:', content.substring(0, 100));
        // 通知父组件刷新变量，但只在真正有工具调用结果时触发一次
        if (onUserMessageSent && !window._toolCallResultProcessed) {
          console.log('自动讨论内容中检测到工具调用结果，触发一次变量刷新');
          window._toolCallResultProcessed = true;
          // 使用setTimeout确保在DOM更新后执行
          setTimeout(() => {
            console.log('执行自动讨论变量刷新回调');
            onUserMessageSent();
            // 5秒后重置标志，允许下一次工具调用触发刷新
            setTimeout(() => {
              console.log('重置自动讨论工具调用结果处理标志');
              window._toolCallResultProcessed = false;
            }, 5000);
          }, 100);
        }
      }

      // 更新当前流式响应内容
      setCurrentStreamingResponse(prev => prev + content);
      // 确保响应状态是激活的
      setIsResponding(true);

      // 滚动到底部
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  // 取消自动讨论
  const handleCancelAutoDiscussion = async () => {
    if (isAutoDiscussing) {
      try {
        // 先设置按钮加载状态
        setStoppingDiscussion(true);

        // 获取当前正在流式输出的智能体ID
        const currentAgentId = streamingAgentId;

        // 如果有正在输出的智能体，先中断它
        if (currentAgentId) {
          console.log(`停止任务：先中断当前智能体 ${currentAgentId}`);
          await conversationAPI.cancelStreamingResponse(currentAgentId);
        }

        // 然后停止整个自主任务（不传递智能体ID）
        console.log('停止任务：停止整个自主任务');
        const cancelSuccess = await conversationAPI.cancelStreamingResponse();
        console.log(`停止自主任务: ${cancelSuccess ? '成功' : '失败'}, 当前智能体ID: ${currentAgentId || '无'}`);

        // 无论API调用是否成功，都重置前端状态
        message.info('已停止自动讨论');
        setIsAutoDiscussing(false);
        setCurrentDiscussionRound(0);
        setCurrentDiscussionTotalRounds(0);
        setDiscussionAgentInfo(null);
        setStreamingAgentId(null);
        setIsResponding(false);

        // 刷新自主行动卡片
        if (onRefreshAutonomousTaskCard) {
          onRefreshAutonomousTaskCard();
        }

        // 添加一个系统消息说明讨论已被手动停止
        const cancelMessage = {
          id: `system-${Date.now()}`,
          role: 'system',
          content: `用户手动停止了自动讨论`,
          timestamp: new Date().toISOString()
        };

        // 添加到消息列表
        updateMessages(prev => [...prev, cancelMessage]);

        // 通知父组件
        if (onMessagesUpdated) {
          // 延迟到下一个事件循环执行，避免在渲染期间更新状态
          setTimeout(() => {
            onMessagesUpdated([...messages, cancelMessage]);
          }, 0);
        }

        // 重新获取消息
        if (activeConversationId) {
          await handleChangeConversation(activeConversationId);
        }
      } catch (error) {
        console.error('停止自动讨论失败:', error);
        message.error('停止自动讨论失败: ' + error.message);

        // 即使出错也要重置讨论状态
        setIsAutoDiscussing(false);
        setCurrentDiscussionRound(0);
        setCurrentDiscussionTotalRounds(0);
        setDiscussionAgentInfo(null);
        setStreamingAgentId(null);
        setIsResponding(false);

        // 刷新自主行动卡片
        if (onRefreshAutonomousTaskCard) {
          onRefreshAutonomousTaskCard();
        }
      } finally {
        // 确保无论如何都重置按钮加载状态
        setStoppingDiscussion(false);
      }
    }
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      backgroundColor: '#f5f5f5'
    }}>
      {/* 会话选择器 */}
      <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
        <Row gutter={[8, 8]} align="middle">
          <Col flex="auto">
            <Select
              loading={conversationsLoading}
              placeholder="选择或创建会话..."
              style={{ width: 220 }}
              value={activeConversationId}
              onChange={handleChangeConversation}
              disabled={isResponding || sendingMessage || isAutoDiscussing}
            >
              {conversations.map(conversation => (
                <Select.Option key={conversation.id} value={conversation.id}>
                  {conversation.title} ({conversation.message_count}条消息)
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col>
            <Tooltip title="刷新当前会话消息">
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefreshMessages}
                disabled={isResponding || sendingMessage || isAutoDiscussing || !activeConversationId}
                loading={refreshingMessages}
              >
                刷新
              </Button>
            </Tooltip>
          </Col>
          <Col>
            <Button
              icon={<PlusOutlined />}
              onClick={() => setShowNewConversationModal(true)}
              disabled={isResponding || sendingMessage || isAutoDiscussing}
            >
              新建会话
            </Button>
          </Col>
          <Col>
            <Tooltip title="启动智能体自主任务">
              <Button
                type="primary"
                icon={<TeamOutlined />}
                onClick={showAutoDiscussModal}
                disabled={isResponding || sendingMessage || !activeConversationId || isAutoDiscussing}
                loading={startingAutoDiscussion}
              >
                自主行动
              </Button>
            </Tooltip>
          </Col>
          {isAutoDiscussing && (
            <Col>
              <Button
                danger
                onClick={handleCancelAutoDiscussion}
                loading={stoppingDiscussion}
              >
                停止行动
              </Button>
            </Col>
          )}
        </Row>

      </div>

      {/* 消息历史区域 */}
      <div className="message-history" style={{
        flex: 1,
        padding: '16px',
        overflowY: 'auto',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative'  // 添加相对定位，作为横幅的定位参考
      }}>
        {/* 显示自动讨论进度 - 美化版横幅 */}
        {isAutoDiscussing && (
          <div className="discussion-banner" style={{
            padding: '16px 20px',
            borderRadius: '8px',
            background: 'linear-gradient(135deg, #1890ff 0%, #36cfc9 100%)',
            boxShadow: '0 4px 12px rgba(24, 144, 255, 0.15)',
            color: 'white',
            position: 'sticky',
            overflow: 'hidden',
            width: '80%',
            margin: '0 auto 16px auto',
            zIndex: 10,
            top: 0
          }}>
            {/* 动态脉冲背景 */}
            <div className="discussion-pulse-bg" style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%)'
            }} />

            <div style={{ display: 'flex', alignItems: 'center', position: 'relative', zIndex: 1, width: '100%' }}>
              <SyncOutlined spin style={{ marginRight: 12, fontSize: 18 }} />
              <div style={{ flex: 1 }}>
                <div style={{ fontWeight: 'bold', fontSize: '16px', marginBottom: '6px' }}>
                  自动讨论进行中
                  {currentDiscussionRound > 0 &&
                    <span style={{ marginLeft: 10, padding: '2px 8px', background: 'rgba(255,255,255,0.2)', borderRadius: '12px', fontSize: '12px' }}>
                      第 {currentDiscussionRound}/{currentDiscussionTotalRounds} 轮
                    </span>
                  }
                </div>

                {discussionAgentInfo && (
                  <div style={{ fontSize: '14px', opacity: 0.95, marginTop: '3px' }}>
                    {!discussionAgentInfo.isSummarizing ? (
                      <>
                        <UserOutlined style={{ marginRight: 6 }} />
                        <span style={{ fontWeight: 500 }}>{discussionAgentInfo.name}</span>
                        <span>
                          {discussionAgentInfo.turnPrompt
                            ? ` - ${discussionAgentInfo.turnPrompt}`
                            : ` 正在思考 (${discussionAgentInfo.responseOrder}/${discussionAgentInfo.totalAgents})`}
                        </span>
                      </>
                    ) : (
                      <>
                        <FileTextOutlined style={{ marginRight: 6 }} />
                        <span style={{ fontWeight: 500 }}>{discussionAgentInfo.name}</span>
                        <span>{discussionAgentInfo.turnPrompt ? ` - ${discussionAgentInfo.turnPrompt}` : ` 正在总结讨论结果`}</span>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {messages.length === 0 ? (
          <Empty
            description="暂无消息"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            style={{ margin: 'auto' }}
          />
        ) : (
          messages.map((msg, index) => (
            <MessageItem
              key={msg.id || index}
              message={msg}
              index={index}
              task={task}
              isVirtual={msg.isVirtual}
              isObserving={isObserving}
              streamingAgentId={streamingAgentId}
            />
          ))
        )}

        {/* 显示当前正在生成的流式响应 */}
        {currentStreamingResponse && isResponding && (
          <div
            className="message-item received"
            style={{
              marginBottom: '16px',
              alignSelf: 'flex-start',
              width: '80%', // 使用固定宽度而不是最大宽度
              padding: '10px 16px',
              borderRadius: '10px 10px 10px 0',
              backgroundColor: '#ffffff',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}
          >
            <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
              {(() => {
                // 检查当前流式智能体是否为监督者
                const streamingAgent = task.agents?.find(agent =>
                  agent.id === Number(streamingAgentId) || String(agent.id) === String(streamingAgentId)
                );
                const isObserverAgent = streamingAgent && (streamingAgent.is_observer || streamingAgent.type === 'observer');

                return (
                  <Avatar
                    icon={isObserverAgent ?
                      <EyeOutlined style={{ color: '#ffffff' }} /> :
                      <RobotOutlined style={{ color: '#ffffff' }} />
                    }
                    size="small"
                    style={{
                      ...getAgentAvatarStyle(streamingAgentId, isObserving, isObserverAgent),
                      marginRight: '8px'
                    }}
                  />
                );
              })()}
              <Text strong>
                {(() => {
                  if (streamingAgentId) {
                    // 调试: 输出所有可用智能体和当前流式智能体ID
                    console.log('流式响应框 - 当前智能体列表:', task.agents?.map(a => ({ id: a.id, name: a.name })));
                    console.log('流式响应框 - 当前流式智能体ID:', streamingAgentId, typeof streamingAgentId);
                    console.log('流式响应框 - 自主任务状态:', isAutoDiscussing);

                    // 先尝试数字类型匹配
                    const numericId = Number(streamingAgentId);
                    const agentByNumericId = task.agents?.find(agent => agent.id === numericId);
                    if (agentByNumericId) {
                      console.log('流式响应框 - 找到匹配的智能体 (数字ID):', agentByNumericId.name);
                      return agentByNumericId.role_name ?
                        `${agentByNumericId.name} [${agentByNumericId.role_name}]` :
                        agentByNumericId.name;
                    }

                    // 再尝试字符串类型匹配
                    const agentByStringId = task.agents?.find(agent => String(agent.id) === String(streamingAgentId));
                    if (agentByStringId) {
                      console.log('流式响应框 - 找到匹配的智能体 (字符串ID):', agentByStringId.name);
                      return agentByStringId.role_name ?
                        `${agentByStringId.name} [${agentByStringId.role_name}]` :
                        agentByStringId.name;
                    }

                    // 尝试其他匹配方法
                    const agentByAnyMatch = task.agents?.find(agent => {
                      console.log(`流式响应框 - 比较: agent.id=${agent.id} (${typeof agent.id}) 与 streamingAgentId=${streamingAgentId} (${typeof streamingAgentId})`);
                      return agent.id === streamingAgentId || String(agent.id) === String(streamingAgentId); // 使用严格比较
                    });

                    if (agentByAnyMatch) {
                      console.log('流式响应框 - 找到匹配的智能体 (任意匹配):', agentByAnyMatch.name);
                      return agentByAnyMatch.role_name ?
                        `${agentByAnyMatch.name} [${agentByAnyMatch.role_name}]` :
                        agentByAnyMatch.name;
                    }

                    console.log('流式响应框 - 未找到匹配的智能体，使用备用名称');
                    return `智能体-${streamingAgentId}`;
                  }
                  console.log('流式响应框 - streamingAgentId为空，显示系统');
                  return '系统';
                })()}
              </Text>

              <div style={{ display: 'flex', alignItems: 'center', marginLeft: '8px' }}>
                {isResponding && (
                  <Tag color="red">可随时中断</Tag>
                )}
              </div>

              <Text type="secondary" style={{ fontSize: '12px', marginLeft: 'auto' }}>
                {new Date().toLocaleString()}
              </Text>
            </div>
            <div style={{ margin: 0 }}>
              {/* 使用ConversationExtraction组件处理所有内容渲染，包括思考内容和工具调用 */}
              <ConversationExtraction
                content={currentStreamingResponse}
                messageRole="assistant"
              />
            </div>
          </div>
        )}

        {/* 消息列表末尾的引用，用于滚动 */}
        <div ref={messagesEndRef} />
      </div>

      {/* 消息输入区域 */}
      <div className="message-input-area" style={{
        padding: '16px',
        borderTop: '1px solid #f0f0f0',
        backgroundColor: '#fafafa',
        position: 'sticky',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 10,
        boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.06)',
        marginTop: 'auto'
      }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Select
            placeholder="选择目标智能体（可多选，默认所有任务智能体会按顺序响应，监督者智能体不参与）"
            style={{ width: '100%', backgroundColor: '#fff' }}
            allowClear
            mode="multiple"
            value={targetAgentIds}
            onChange={setTargetAgentIds}
            disabled={task.status !== 'active'}
            optionLabelProp="label"
          >
            {task.agents?.filter(agent => !agent.is_observer && agent.type !== 'observer').map((agent) => {
              const isObserver = agent.is_observer || agent.type === 'observer';
              return (
                <Select.Option
                  key={agent.id}
                  value={agent.id}
                  label={agent.role_name ? `${agent.name} [${agent.role_name}]` : agent.name}
                >
                  <Space>
                    <Avatar
                      icon={isObserver ?
                        <EyeOutlined style={{ color: '#ffffff' }} /> :
                        <RobotOutlined style={{ color: '#ffffff' }} />
                      }
                      size="small"
                      style={getAgentAvatarStyle(agent.id || agent.name, false, isObserver)}
                    />
                    {agent.role_name ? `${agent.name} [${agent.role_name}]` : agent.name}
                  </Space>
                </Select.Option>
              );
            })}
          </Select>
          <div style={{ display: 'flex' }}>
            <TextArea
              value={userMessage}
              onChange={(e) => setUserMessage(e.target.value)}
              placeholder={isResponding ? "当前智能体正在响应中..." : "输入消息..."}
              autoSize={{ minRows: 3, maxRows: 6 }}
              disabled={task.status !== 'active'}
              onPressEnter={(e) => {
                if (e.ctrlKey || e.metaKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
              style={{ flex: 1, backgroundColor: '#fff' }}
            />
            <div style={{ marginLeft: 8, display: 'flex', flexDirection: 'column', gap: 4 }}>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'expand',
                      label: '扩展内容',
                      icon: <ExpandAltOutlined />,
                      onClick: () => handleMessageAssist('expand')
                    },
                    {
                      key: 'optimize',
                      label: '优化表达',
                      icon: <EditOutlined />,
                      onClick: () => handleMessageAssist('optimize')
                    },
                    {
                      key: 'rewrite',
                      label: '重新表述',
                      icon: <ReloadOutlined />,
                      onClick: () => handleMessageAssist('rewrite')
                    },
                    {
                      key: 'professional',
                      label: '专业化',
                      icon: <ThunderboltOutlined />,
                      onClick: () => handleMessageAssist('professional')
                    },
                    {
                      key: 'casual',
                      label: '口语化',
                      icon: <UserOutlined />,
                      onClick: () => handleMessageAssist('casual')
                    }
                  ]
                }}
                trigger={['hover']}
                disabled={task.status !== 'active' || !userMessage.trim() || assistingMessage || !globalSettings.enableAssistantGeneration}
              >
                <Button
                  type="default"
                  icon={<RobotOutlined />}
                  disabled={task.status !== 'active' || !userMessage.trim() || !globalSettings.enableAssistantGeneration}
                  loading={assistingMessage}
                  size="small"
                  title={!globalSettings.enableAssistantGeneration ? "辅助生成功能未启用" : "消息辅助生成 - 悬停查看选项"}
                  style={{
                    fontSize: '12px',
                    height: '32px',
                    borderColor: '#1890ff',
                    color: '#1890ff'
                  }}
                >
                  辅助
                </Button>
              </Dropdown>
              <Button
                type={isResponding ? "primary" : "primary"}
                danger={isResponding}
                icon={isResponding ? <StopOutlined /> : <SendOutlined />}
                onClick={sendMessage}
                loading={sendingMessage}
                disabled={task.status !== 'active' || (!isResponding && !userMessage.trim())}
                style={{ height: 'auto', flex: 1 }}
                title={isResponding ? (isAutoDiscussing ? "中断当前智能体（自主任务将继续）" : "中断当前智能体") : "发送消息"}
              >
                {isResponding ? (isAutoDiscussing ? "中断" : "停止") : "发送"}
              </Button>
            </div>
          </div>
          <div style={{ fontSize: '12px', color: '#8c8c8c', textAlign: 'right' }}>
            {isResponding && (
              <span style={{ color: '#f5222d', fontWeight: 'bold' }}>
                {isAutoDiscussing
                  ? '点击中断按钮可中断当前智能体（自主任务将继续下一个智能体）'
                  : '点击停止按钮可中断当前响应'} |
              </span>
            )}
            {isAutoDiscussing
              ? '自主任务进行中，任务智能体将自动轮流响应（监督者智能体不参与）'
              : targetAgentIds.length > 0
                ? '消息将发送给选中的任务智能体'
                : '消息将发送给所有任务智能体，它们会按顺序响应（监督者智能体不参与）'} | 按 Ctrl+Enter 发送
          </div>
        </Space>
      </div>

      {/* 创建会话的模态框 */}
      <Modal
        title="创建会话"
        open={showNewConversationModal}
        onCancel={() => setShowNewConversationModal(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowNewConversationModal(false)}>
            取消
          </Button>,
          <Button
            key="create"
            type="primary"
            loading={creatingConversation}
            onClick={handleCreateConversation}
          >
            创建
          </Button>
        ]}
      >
        <Form layout="vertical">
          <Form.Item
            label="会话标题"
            required
            validateStatus={newConversationTitle.trim() ? 'success' : 'error'}
            help={!newConversationTitle.trim() && '请输入会话标题'}
          >
            <Input
              value={newConversationTitle}
              onChange={(e) => setNewConversationTitle(e.target.value)}
              placeholder="输入会话标题"
              disabled={creatingConversation}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 自主任务配置模态框 */}
      <AutonomousTaskModal
        visible={autoDiscussModalVisible}
        onCancel={handleAutoDiscussCancel}
        onConfirm={handleAutoDiscussConfirm}
        confirmLoading={startingAutoDiscussion}
        task={task}
        environmentVariables={environmentVariables}
        agentVariables={agentVariables}
        options={autoDiscussionOptions}
        onOptionsChange={setAutoDiscussionOptions}
      />


    </div>
  );
});

export default ActionTaskConversation;